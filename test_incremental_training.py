"""
测试模型增量训练功能

此脚本演示如何使用模型增量训练功能：
1. 添加训练数据到模型训练队列
2. 检查训练队列状态
3. 触发增量训练
4. 监控训练状态
"""

import requests
import json
import time
import numpy as np
import random

# API端点
BASE_URL = "http://localhost:5000/api"
ADD_DATA_URL = f"{BASE_URL}/add-training-data"
STATUS_URL = f"{BASE_URL}/model-training-status"
TRIGGER_URL = f"{BASE_URL}/trigger-model-training"

# 模型名称
MODEL_NAME = "encryption_detector"  # 或 "encryption_detector""anomaly_detector“

def print_json(data):
    """美化打印JSON数据"""
    print(json.dumps(data, indent=2, ensure_ascii=False))

def check_model_status():
    """检查模型状态"""
    print(f"\n正在检查模型 {MODEL_NAME} 的状态...")
    response = requests.get(f"{STATUS_URL}?model_name={MODEL_NAME}")
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            status = data['status']
            print(f"训练队列大小: {status['queue_size']}")
            print(f"训练中: {status['training_in_progress']}")
            print(f"已触发训练: {status['training_triggered']}")
            
            # 打印最近的训练日志
            if 'training_log' in status and status['training_log']:
                print("\n最近的训练记录:")
                for log in status['training_log'][-3:]:  # 只显示最近3条
                    print(f"- {log['timestamp']}: {'成功' if log['success'] else '失败'} - {log['message']}")
        else:
            print(f"获取状态失败: {data.get('error')}")
    else:
        print(f"请求失败，状态码: {response.status_code}")

def add_training_data(count=10):
    """添加训练数据到队列"""
    print(f"\n正在添加 {count} 个训练样本到模型 {MODEL_NAME}...")
    
    # 生成随机特征和标签
    for _ in range(count):
        # 生成64维的随机特征向量
        features = np.random.rand(64).tolist()
        
        # 随机标签 (0: 正常, 1: 异常)
        label = random.randint(0, 1)
        
        # 发送请求
        response = requests.post(ADD_DATA_URL, json={
            'model_name': MODEL_NAME,
            'features': features,
            'label': label
        })
        
        if response.status_code != 200 or not response.json().get('success'):
            print(f"添加样本失败: {response.json().get('error')}")
            return False
    
    # 检查结果
    response = requests.get(f"{STATUS_URL}?model_name={MODEL_NAME}")
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print(f"成功添加样本，当前队列大小: {data['status']['queue_size']}")
            return True
    
    return False

def trigger_training():
    """触发模型增量训练"""
    print(f"\n正在触发模型 {MODEL_NAME} 的增量训练...")
    response = requests.post(TRIGGER_URL, json={'model_name': MODEL_NAME})
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print(f"成功触发训练: {data.get('message')}")
            return True
        else:
            print(f"触发训练失败: {data.get('error')}")
    else:
        print(f"请求失败，状态码: {response.status_code}")
    
    return False

def monitor_training(max_wait=300, interval=5):
    """监控训练进度"""
    print(f"\n开始监控训练进度 (最多等待 {max_wait} 秒)...")
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        response = requests.get(f"{STATUS_URL}?model_name={MODEL_NAME}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                status = data['status']
                
                # 如果训练已完成
                if not status['training_in_progress'] and not status['training_triggered']:
                    print("\n训练已完成!")
                    
                    # 打印最近的训练记录
                    if 'training_log' in status and status['training_log']:
                        print("\n最近的训练记录:")
                        for log in status['training_log'][-1:]:  # 只显示最近一条
                            print(f"- {log['timestamp']}: {'成功' if log['success'] else '失败'} - {log['message']}")
                    
                    return True
                else:
                    print(f"训练中... (已等待 {int(time.time() - start_time)} 秒)")
        
        time.sleep(interval)
    
    print(f"\n等待超时 ({max_wait} 秒)，训练可能仍在进行中")
    return False

def main():
    """主函数"""
    print("===== 模型增量训练测试 =====")
    
    # 1. 检查初始状态
    check_model_status()
    
    # 2. 添加训练数据
    if not add_training_data(count=60):  # 添加60个样本
        print("添加训练数据失败，退出测试")
        return
    
    # 3. 再次检查状态
    check_model_status()
    
    # 4. 触发训练
    if not trigger_training():
        print("触发训练失败，退出测试")
        return
    
    # 5. 监控训练进度
    monitor_training()
    
    # 6. 检查最终状态
    check_model_status()
    
    print("\n===== 测试完成 =====")

if __name__ == "__main__":
    main()
