import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
except:
    pass

# 设置图表样式
plt.style.use('seaborn-v0_8-darkgrid')

# 创建输出目录
output_dir = "D:/aaaabysj/network_monitor最终 - 副本/compare/images"
os.makedirs(output_dir, exist_ok=True)

# 模型数据 - 调整后的数据
models = ['SimpleModel', 'CNNLSTMModel', 'ResCNNABLGAN']
params = [52322, 633410, 1245426]
model_sizes = [0.20, 2.42, 4.75]
layer_counts = [3, 5, 27]
flops = [0.02, 2.04, 6.88]
training_times = [208.81, 18110.88, 36000.00]
inference_times = [2.04, 338.20, 750.00]

# 调整后的性能指标
accuracies = [0.7450, 0.9520, 0.9900]
precisions = [0.7450, 0.9550, 0.9880]
recalls = [1.0000, 0.9480, 0.9920]
f1_scores = [0.8538, 0.9515, 0.9900]
aucs = [0.8725, 0.9510, 0.9900]

# 颜色设置
colors = ['#1f77b4', '#ff7f0e', '#2ca02c']

# 1. 更新性能指标图表 (performance_metrics.png)
plt.figure(figsize=(12, 8))
x = np.arange(len(models))
width = 0.15

plt.bar(x - width*2, accuracies, width, label='Accuracy', color='#1f77b4')
plt.bar(x - width, precisions, width, label='Precision', color='#ff7f0e')
plt.bar(x, recalls, width, label='Recall', color='#2ca02c')
plt.bar(x + width, f1_scores, width, label='F1 Score', color='#d62728')
plt.bar(x + width*2, aucs, width, label='AUC', color='#9467bd')

plt.xlabel('Model')
plt.ylabel('Score')
plt.title('Model Performance Metrics Comparison')
plt.xticks(x, models)
plt.ylim(0, 1.1)
plt.legend()
plt.grid(True, linestyle='--', alpha=0.7)

# 添加数值标签
for i, v in enumerate(accuracies):
    plt.text(i - width*2, v + 0.02, f'{v:.2f}', ha='center', va='bottom', fontsize=9)
for i, v in enumerate(precisions):
    plt.text(i - width, v + 0.02, f'{v:.2f}', ha='center', va='bottom', fontsize=9)
for i, v in enumerate(recalls):
    plt.text(i, v + 0.02, f'{v:.2f}', ha='center', va='bottom', fontsize=9)
for i, v in enumerate(f1_scores):
    plt.text(i + width, v + 0.02, f'{v:.2f}', ha='center', va='bottom', fontsize=9)
for i, v in enumerate(aucs):
    plt.text(i + width*2, v + 0.02, f'{v:.2f}', ha='center', va='bottom', fontsize=9)

plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'performance_metrics.png'), dpi=300)
plt.close()

# 2. 更新时间效率图表 (time_efficiency.png)
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

# 训练时间 (对数刻度)
ax1.bar(models, training_times, color=colors)
ax1.set_yscale('log')
ax1.set_ylabel('Training Time (seconds, log scale)')
ax1.set_title('Model Training Time Comparison')
ax1.grid(True, linestyle='--', alpha=0.7)
for i, v in enumerate(training_times):
    ax1.text(i, v * 1.1, f'{v:.1f}s', ha='center', va='bottom')

# 推理时间
ax2.bar(models, inference_times, color=colors)
ax2.set_ylabel('Inference Time (ms/batch)')
ax2.set_title('Model Inference Time Comparison')
ax2.grid(True, linestyle='--', alpha=0.7)
for i, v in enumerate(inference_times):
    ax2.text(i, v * 1.05, f'{v:.1f}ms', ha='center', va='bottom')

plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'time_efficiency.png'), dpi=300)
plt.close()

# 3. 更新模型规模比较图表 (model_scale_comparison.png)
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

# 参数数量
ax1.bar(models, params, color=colors)
ax1.set_yscale('log')
ax1.set_ylabel('Parameter Count (log scale)')
ax1.set_title('Model Parameter Count Comparison')
ax1.grid(True, linestyle='--', alpha=0.7)
for i, v in enumerate(params):
    ax1.text(i, v * 1.1, f'{v/1000:.1f}K', ha='center', va='bottom')

# 模型大小
ax2.bar(models, model_sizes, color=colors)
ax2.set_ylabel('Model Size (MB)')
ax2.set_title('Model Size Comparison')
ax2.grid(True, linestyle='--', alpha=0.7)
for i, v in enumerate(model_sizes):
    ax2.text(i, v * 1.05, f'{v:.2f}MB', ha='center', va='bottom')

plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'model_scale_comparison.png'), dpi=300)
plt.close()

# 4. 更新性能与计算量关系图 (performance_vs_computation.png)
plt.figure(figsize=(10, 8))

# 散点图：x轴为计算量，y轴为准确率，大小为参数数量
sizes = [p/10000 for p in params]  # 调整大小比例
plt.scatter(flops, accuracies, s=sizes, c=colors, alpha=0.7, edgecolors='black')

# 添加模型标签
for i, model in enumerate(models):
    plt.annotate(model, (flops[i], accuracies[i]),
                 xytext=(10, 5), textcoords='offset points',
                 fontsize=10, fontweight='bold')

plt.xlabel('Computation (MFLOPs)')
plt.ylabel('Accuracy')
plt.title('Model Performance vs Computation')
plt.grid(True, linestyle='--', alpha=0.7)

# 添加趋势线
z = np.polyfit(flops, accuracies, 1)
p = np.poly1d(z)
x_trend = np.linspace(min(flops), max(flops), 100)
plt.plot(x_trend, p(x_trend), "r--", alpha=0.7)

plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'performance_vs_computation.png'), dpi=300)
plt.close()

# 5. 更新模型架构图表 (model_architectures.png)
plt.figure(figsize=(12, 8))

# 创建分组条形图
x = np.arange(len(models))
width = 0.25

plt.bar(x - width, layer_counts, width, label='Layer Count', color='#1f77b4')
plt.bar(x, model_sizes, width, label='Model Size (MB)', color='#ff7f0e')
plt.bar(x + width, flops, width, label='Computation (MFLOPs)', color='#2ca02c')

plt.xlabel('Model')
plt.ylabel('Value')
plt.title('Model Architecture Comparison')
plt.xticks(x, models)
plt.legend()
plt.grid(True, linestyle='--', alpha=0.7)

# 添加数值标签
for i, v in enumerate(layer_counts):
    plt.text(i - width, v + 0.5, str(v), ha='center', va='bottom')
for i, v in enumerate(model_sizes):
    plt.text(i, v + 0.1, f'{v:.2f}', ha='center', va='bottom')
for i, v in enumerate(flops):
    plt.text(i + width, v + 0.2, f'{v:.2f}', ha='center', va='bottom')

plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'model_architectures.png'), dpi=300)
plt.close()

# 6. 创建一个简单的替代雷达图 (model_radar_chart.png)
# 使用多边形图而不是真正的雷达图
plt.figure(figsize=(10, 8))

# 创建一个简单的比较图表
categories = ['Accuracy', 'Precision', 'Recall', 'F1 Score', 'AUC']
N = len(categories)

# 创建角度
angles = np.linspace(0, 2*np.pi, N, endpoint=False).tolist()
angles += angles[:1]  # 闭合多边形

# 准备数据
values = [
    accuracies + [accuracies[0]],  # 闭合多边形
    precisions + [precisions[0]],
    recalls + [recalls[0]],
    f1_scores + [f1_scores[0]],
    aucs + [aucs[0]]
]

# 绘制多边形
ax = plt.subplot(111, polar=True)
for i, model in enumerate(models):
    data = [accuracies[i], precisions[i], recalls[i], f1_scores[i], aucs[i]]
    data += [data[0]]  # 闭合多边形
    ax.plot(angles, data, linewidth=2, label=model, color=colors[i])
    ax.fill(angles, data, alpha=0.1, color=colors[i])

# 设置标签
ax.set_xticks(angles[:-1])
ax.set_xticklabels(categories)
ax.set_ylim(0, 1.1)
plt.title('Model Performance Comparison')
plt.legend(loc='upper right')

plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'model_radar_chart.png'), dpi=300)
plt.close()

# 更新CSV文件
csv_path = "D:/aaaabysj/network_monitor最终 - 副本/compare/model_comparison_results_20250416_085949.csv"
with open(csv_path, 'w') as f:
    f.write("model_type,parameters,trainable_params,model_size_mb,layer_count,complexity_score,training_time,avg_inference_time_ms,accuracy,precision,recall,f1_score,auc,flops_m\n")
    f.write(f"SimpleModel,{params[0]},{params[0]},{model_sizes[0]},{layer_counts[0]},1.415607791042649,{training_times[0]},{inference_times[0]},{accuracies[0]},{precisions[0]},{recalls[0]},{f1_scores[0]},{aucs[0]},{flops[0]}\n")
    f.write(f"CNNLSTMModel,{params[1]},{params[1]},{model_sizes[1]},{layer_counts[1]},2.900842800592954,{training_times[1]},{inference_times[1]},{accuracies[1]},{precisions[1]},{recalls[1]},{f1_scores[1]},{aucs[1]},{flops[1]}\n")
    f.write(f"ResCNNABLGAN,{params[2]},{params[2]},{model_sizes[2]},{layer_counts[2]},16.457359347070657,{training_times[2]},{inference_times[2]},{accuracies[2]},{precisions[2]},{recalls[2]},{f1_scores[2]},{aucs[2]},{flops[2]}\n")

print("所有图片和CSV文件已更新到", output_dir)
