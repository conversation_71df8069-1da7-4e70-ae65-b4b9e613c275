#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
从Benign和Malware文件夹中提取PCAP数据，并保存为CSV格式的训练集和测试集
"""

import os
import sys
import pandas as pd
import numpy as np
import torch
import time
from datetime import datetime
import glob
from sklearn.model_selection import train_test_split

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入PCAP数据处理函数
from bijiao.model_comparison import extract_features_from_pcap, load_pcap_data

def extract_all_pcap_files(benign_dir, malware_dir, output_dir, test_size=0.2):
    """
    从Benign和Malware文件夹中提取所有PCAP文件的特征，并保存为CSV格式的训练集和测试集
    
    Args:
        benign_dir: Benign文件夹路径
        malware_dir: Malware文件夹路径
        output_dir: 输出目录路径
        test_size: 测试集比例
    
    Returns:
        train_file_path, test_file_path: 训练集和测试集文件路径
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取所有PCAP文件路径
    benign_files = glob.glob(os.path.join(benign_dir, "*.pcap"))
    malware_files = glob.glob(os.path.join(malware_dir, "*.pcap"))
    
    print(f"找到 {len(benign_files)} 个良性流量文件和 {len(malware_files)} 个恶意流量文件")
    
    # 创建文件路径列表
    pcap_files = benign_files + malware_files
    
    # 使用load_pcap_data函数加载数据
    print("开始加载PCAP文件并提取特征...")
    try:
        train_loader, test_loader = load_pcap_data(pcap_files, test_size=test_size, batch_size=1024)
        print("PCAP文件加载完成")
        
        # 保存训练集
        print("\n保存训练集...")
        train_file_path = save_dataloader_to_csv(train_loader, output_dir, "train_dataset")
        
        # 保存测试集
        print("\n保存测试集...")
        test_file_path = save_dataloader_to_csv(test_loader, output_dir, "test_dataset")
        
        return train_file_path, test_file_path
    except Exception as e:
        print(f"加载PCAP文件失败: {str(e)}")
        print("尝试手动提取特征...")
        
        # 手动提取特征
        return extract_features_manually(benign_files, malware_files, output_dir, test_size)

def extract_features_manually(benign_files, malware_files, output_dir, test_size=0.2):
    """
    手动从PCAP文件中提取特征
    
    Args:
        benign_files: 良性流量文件路径列表
        malware_files: 恶意流量文件路径列表
        output_dir: 输出目录路径
        test_size: 测试集比例
    
    Returns:
        train_file_path, test_file_path: 训练集和测试集文件路径
    """
    from scapy.all import rdpcap
    from scapy.layers.inet import IP
    
    all_features = []
    all_labels = []
    
    # 处理良性流量文件
    for file_path in benign_files:
        print(f"处理文件: {os.path.basename(file_path)}")
        try:
            # 读取PCAP文件
            packets = rdpcap(file_path)
            print(f"  - 读取了 {len(packets)} 个数据包")
            
            # 提取特征
            file_features = []
            for packet in packets:
                try:
                    # 只处理IP包
                    if IP in packet:
                        features = extract_features_from_pcap(packet)
                        file_features.append(features)
                except Exception as e:
                    continue
            
            print(f"  - 提取了 {len(file_features)} 个特征向量")
            all_features.extend(file_features)
            all_labels.extend([0] * len(file_features))  # 良性流量标签为0
        except Exception as e:
            print(f"  - 处理失败: {str(e)}")
    
    # 处理恶意流量文件
    for file_path in malware_files:
        print(f"处理文件: {os.path.basename(file_path)}")
        try:
            # 读取PCAP文件
            packets = rdpcap(file_path)
            print(f"  - 读取了 {len(packets)} 个数据包")
            
            # 提取特征
            file_features = []
            for packet in packets:
                try:
                    # 只处理IP包
                    if IP in packet:
                        features = extract_features_from_pcap(packet)
                        file_features.append(features)
                except Exception as e:
                    continue
            
            print(f"  - 提取了 {len(file_features)} 个特征向量")
            all_features.extend(file_features)
            all_labels.extend([1] * len(file_features))  # 恶意流量标签为1
        except Exception as e:
            print(f"  - 处理失败: {str(e)}")
    
    # 转换为NumPy数组
    X = np.array(all_features, dtype=np.float32)
    y = np.array(all_labels, dtype=np.int64)
    
    print(f"\n总共提取了 {len(X)} 个特征向量")
    print(f"类别分布: {np.bincount(y)}")
    
    # 分割训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=42, stratify=y)
    
    # 创建数据集
    train_dataset = torch.utils.data.TensorDataset(torch.FloatTensor(X_train), torch.LongTensor(y_train))
    test_dataset = torch.utils.data.TensorDataset(torch.FloatTensor(X_test), torch.LongTensor(y_test))
    
    # 创建数据加载器
    train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=1024, shuffle=True)
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=1024, shuffle=False)
    
    # 保存训练集
    print("\n保存训练集...")
    train_file_path = save_dataloader_to_csv(train_loader, output_dir, "train_dataset")
    
    # 保存测试集
    print("\n保存测试集...")
    test_file_path = save_dataloader_to_csv(test_loader, output_dir, "test_dataset")
    
    return train_file_path, test_file_path

def save_dataloader_to_csv(dataloader, output_path, prefix="dataset"):
    """
    将DataLoader中的数据保存为CSV文件
    
    Args:
        dataloader: PyTorch DataLoader对象
        output_path: 输出目录路径
        prefix: 文件名前缀
    
    Returns:
        保存的文件路径
    """
    # 确保输出目录存在
    os.makedirs(output_path, exist_ok=True)
    
    # 提取所有数据
    all_features = []
    all_labels = []
    
    print(f"正在从DataLoader提取数据...")
    for features, labels in dataloader:
        # 转换为NumPy数组
        features_np = features.numpy()
        labels_np = labels.numpy()
        
        all_features.append(features_np)
        all_labels.append(labels_np)
    
    # 合并所有批次
    X = np.vstack(all_features)
    y = np.concatenate(all_labels)
    
    # 创建DataFrame
    # 生成特征列名
    feature_cols = [f"feature_{i}" for i in range(X.shape[1])]
    
    # 创建DataFrame
    df = pd.DataFrame(X, columns=feature_cols)
    df['label'] = y
    
    # 生成文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_path = os.path.join(output_path, f"{prefix}_{timestamp}.csv")
    
    # 保存到CSV
    df.to_csv(file_path, index=False)
    print(f"数据已保存到: {file_path}")
    print(f"样本数: {len(df)}, 特征数: {len(feature_cols)}")
    print(f"类别分布: {np.bincount(y)}")
    
    return file_path

def main():
    """主函数"""
    # 设置路径
    benign_dir = "D:\\aaaabysj\\network_monitor5 - 副本\\bijiao\\Benign"
    malware_dir = "D:\\aaaabysj\\network_monitor5 - 副本\\bijiao\\Malware"
    output_dir = "D:\\aaaabysj\\network_monitor5 - 副本\\bijiao"
    
    # 提取特征并保存数据集
    print("开始从PCAP文件中提取特征并保存数据集...")
    train_file, test_file = extract_all_pcap_files(benign_dir, malware_dir, output_dir)
    
    print("\n数据提取和保存完成!")
    print(f"训练集: {train_file}")
    print(f"测试集: {test_file}")

if __name__ == "__main__":
    try:
        print("开始提取和保存数据集...")
        start_time = time.time()
        main()
        elapsed_time = time.time() - start_time
        print(f"数据提取和保存完成! 耗时: {elapsed_time:.2f} 秒")
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()
