"""
使用USTC-TFC2016数据集训练ResCNN-ABLGAN模型

此脚本从指定的良性和恶意PCAP文件夹中读取数据，
每个文件只提取500个数据包，并使用进度条显示训练过程。
训练完成后，将保存模型文件。
"""

import os
import sys
import glob
import torch
import numpy as np
from scapy.all import rdpcap
from tqdm import tqdm
import random
from torch.utils.data import TensorDataset, DataLoader
from app.utils.deep_learning_model import NetworkTrafficAnalyzer

# 设置随机种子，确保结果可复现
random.seed(42)
np.random.seed(42)
torch.manual_seed(42)
if torch.cuda.is_available():
    torch.cuda.manual_seed_all(42)

def ensure_model_dir():
    """确保模型目录存在"""
    model_dir = os.path.join('app', 'models')
    os.makedirs(model_dir, exist_ok=True)
    return model_dir

def extract_features_from_packet(packet):
    """从数据包中提取特征"""
    # 提取基本特征
    features = []

    # 1. 数据包长度
    packet_len = len(packet)
    features.append(packet_len)

    # 2. 协议类型（转换为数值）
    if 'IP' in packet:
        proto = packet['IP'].proto
        features.append(proto)
    else:
        features.append(0)

    # 3. 头部长度
    header_len = len(packet) - len(packet.payload) if packet.payload else len(packet)
    features.append(header_len)

    # 4. 负载长度
    payload_len = len(packet.payload) if packet.payload else 0
    features.append(payload_len)

    # 5-8. 时间特征（使用相对时间）
    features.append(float(packet.time))

    # 9-12. 统计特征 - 使用前8个字节的统计信息
    raw_bytes = bytes(packet)[:8]
    if raw_bytes:
        features.append(np.mean(list(raw_bytes)))
        features.append(np.std(list(raw_bytes)))
        features.append(np.max(list(raw_bytes)))
        features.append(np.min(list(raw_bytes)))
    else:
        features.extend([0, 0, 0, 0])

    # 13-14. TCP/UDP端口（如果存在）
    src_port = 0
    dst_port = 0
    if 'TCP' in packet:
        src_port = packet['TCP'].sport
        dst_port = packet['TCP'].dport
    elif 'UDP' in packet:
        src_port = packet['UDP'].sport
        dst_port = packet['UDP'].dport
    features.append(src_port)
    features.append(dst_port)

    # 15-16. TCP标志（如果存在）
    if 'TCP' in packet:
        flags = packet['TCP'].flags
        features.append(int(flags))
    else:
        features.append(0)

    # 填充到64维
    while len(features) < 64:
        features.append(0)

    # 截断到64维
    features = features[:64]

    return np.array(features, dtype=np.float32)

def load_pcap_files(benign_folder, malware_folder, max_packets_per_file=500):
    """加载PCAP文件并提取特征"""
    print("正在加载PCAP文件...")

    # 获取所有PCAP文件路径
    benign_files = glob.glob(os.path.join(benign_folder, "*.pcap"))
    malware_files = glob.glob(os.path.join(malware_folder, "*.pcap"))

    print(f"找到 {len(benign_files)} 个良性PCAP文件和 {len(malware_files)} 个恶意PCAP文件")

    all_features = []
    all_labels = []

    # 处理良性文件
    print("处理良性文件...")
    for pcap_file in tqdm(benign_files):
        try:
            packets = rdpcap(pcap_file)
            # 限制每个文件的数据包数量
            packets = packets[:max_packets_per_file]

            for packet in packets:
                try:
                    features = extract_features_from_packet(packet)
                    all_features.append(features)
                    all_labels.append(0)  # 良性标签为0
                except Exception as e:
                    # 跳过处理失败的数据包
                    continue
        except Exception as e:
            print(f"无法读取文件 {pcap_file}: {str(e)}")
            continue

    # 处理恶意文件
    print("处理恶意文件...")
    for pcap_file in tqdm(malware_files):
        try:
            packets = rdpcap(pcap_file)
            # 限制每个文件的数据包数量
            packets = packets[:max_packets_per_file]

            for packet in packets:
                try:
                    features = extract_features_from_packet(packet)
                    all_features.append(features)
                    all_labels.append(1)  # 恶意标签为1
                except Exception as e:
                    # 跳过处理失败的数据包
                    continue
        except Exception as e:
            print(f"无法读取文件 {pcap_file}: {str(e)}")
            continue

    # 转换为NumPy数组
    X = np.array(all_features, dtype=np.float32)
    y = np.array(all_labels, dtype=np.int64)

    print(f"总共提取了 {len(X)} 个特征向量")
    print(f"类别分布: 良性={np.sum(y==0)}, 恶意={np.sum(y==1)}")

    return X, y

def normalize_features(X):
    """标准化特征"""
    # 简单的最小-最大标准化
    for i in range(X.shape[1]):
        col_min = np.min(X[:, i])
        col_max = np.max(X[:, i])
        if col_max > col_min:
            X[:, i] = (X[:, i] - col_min) / (col_max - col_min)
        else:
            X[:, i] = 0
    return X

def train_model(X, y, model_type='advanced', batch_size=32, epochs=5):
    """训练模型"""
    # 检测是否有GPU可用
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 划分训练集和测试集
    from sklearn.model_selection import train_test_split
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

    # 标准化特征
    X_train = normalize_features(X_train)
    X_test = normalize_features(X_test)

    # 转换为PyTorch张量并移动到指定设备
    X_train_tensor = torch.tensor(X_train, dtype=torch.float32).to(device)
    y_train_tensor = torch.tensor(y_train, dtype=torch.long).to(device)
    X_test_tensor = torch.tensor(X_test, dtype=torch.float32).to(device)
    y_test_tensor = torch.tensor(y_test, dtype=torch.long).to(device)

    # 创建数据集和数据加载器
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    test_dataset = TensorDataset(X_test_tensor, y_test_tensor)

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

    # 创建模型
    print(f"创建 {model_type} 模型...")
    analyzer = NetworkTrafficAnalyzer(model_type=model_type)

    # 训练模型
    print(f"开始训练模型，共 {epochs} 个轮次...")
    analyzer.train(train_loader, num_epochs=epochs, val_loader=test_loader, adversarial=True)

    # 评估模型
    print("评估模型性能...")
    correct = 0
    total = 0
    analyzer.model.eval()
    with torch.no_grad():
        for inputs, labels in test_loader:
            # 注意：输入和标签已经在正确的设备上
            if analyzer.model_type in ['advanced', 'enhanced', 'multitask']:
                outputs = analyzer.model(inputs)
                outputs = outputs['anomaly'] if isinstance(outputs, dict) else outputs
            else:
                outputs = analyzer.model(inputs)

            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()

    accuracy = 100 * correct / total
    print(f"测试集准确率: {accuracy:.2f}%")

    return analyzer, accuracy

def train_both_models():
    """训练异常流量和加密流量两个模型"""
    # 确保模型目录存在
    model_dir = ensure_model_dir()

    # 默认参数
    benign_folder = r"D:\aaaabysj\network_monitor最终 - 副本\compare\USTC-TFC2016数据集\Benign"
    malware_folder = r"D:\aaaabysj\network_monitor最终 - 副本\compare\USTC-TFC2016数据集\Malware"
    max_packets = 200  # 每个文件提取的数据包数
    batch_size = 64    # 批次大小
    epochs = 5         # 训练轮次

    # 检查文件夹是否存在
    if not os.path.exists(benign_folder):
        print(f"错误: 良性文件夹 '{benign_folder}' 不存在")
        print("请修改脚本中的默认路径或确保文件夹存在")
        return

    if not os.path.exists(malware_folder):
        print(f"错误: 恶意文件夹 '{malware_folder}' 不存在")
        print("请修改脚本中的默认路径或确保文件夹存在")
        return

    # 首先训练异常流量检测模型
    print("\n===== 开始训练异常流量检测模型 (ResCNN-ABLGAN) =====")
    print(f"良性文件夹: {benign_folder}")
    print(f"恶意文件夹: {malware_folder}")
    print(f"每个文件最大数据包数: {max_packets}")
    print(f"批次大小: {batch_size}")
    print(f"训练轮次: {epochs}")

    # 加载PCAP文件并提取特征
    print("正在加载和处理PCAP文件...")
    X, y = load_pcap_files(benign_folder, malware_folder, max_packets)

    # 训练异常流量检测模型
    model_type = 'advanced'  # ResCNN-ABLGAN对应的模型类型
    print("开始训练异常流量检测模型...")
    analyzer, accuracy = train_model(X, y, model_type, batch_size, epochs)

    # 保存异常流量检测模型
    anomaly_model_path = os.path.join(model_dir, 'anomaly_model.pth')
    analyzer.save_model(anomaly_model_path)
    print(f"异常流量检测模型已保存到 {anomaly_model_path}")
    print(f"测试集准确率: {accuracy:.2f}%")

    # 然后训练加密流量检测模型
    print("\n===== 开始训练加密流量检测模型 (ResCNN-ABLGAN) =====")
    print(f"良性文件夹: {benign_folder}")
    print(f"恶意文件夹: {malware_folder}")
    print(f"每个文件最大数据包数: {max_packets}")
    print(f"批次大小: {batch_size}")
    print(f"训练轮次: {epochs}")

    # 使用相同的数据训练加密流量检测模型
    # 在实际应用中，可能需要为加密流量检测准备不同的数据集
    print("开始训练加密流量检测模型...")
    analyzer, accuracy = train_model(X, y, model_type, batch_size, epochs)

    # 保存加密流量检测模型
    encryption_model_path = os.path.join(model_dir, 'encryption_model.pth')
    analyzer.save_model(encryption_model_path)
    print(f"加密流量检测模型已保存到 {encryption_model_path}")
    print(f"测试集准确率: {accuracy:.2f}%")

    print("\n===== 所有模型训练完成 =====")
    print(f"异常流量检测模型: {anomaly_model_path}")
    print(f"加密流量检测模型: {encryption_model_path}")
    print("现在可以运行增量训练测试了。")

def main():
    """主函数"""
    # 如果有命令行参数，则使用参数进行训练
    if len(sys.argv) > 1:
        # 检查命令行参数
        if len(sys.argv) < 3:
            print("用法: python train_model_from_pcap.py <良性文件夹路径> <恶意文件夹路径> [模型类型] [每个文件最大数据包数] [批次大小] [训练轮次]")
            print("例如: python train_model_from_pcap.py ./Benign ./Malware anomaly 500 32 5")
            print("模型类型可选: anomaly (异常流量检测) 或 encryption (加密流量检测)")
            print("\n或者直接运行脚本而不提供参数，将使用默认设置训练两个模型")
            return

        # 获取参数
        benign_folder = sys.argv[1]
        malware_folder = sys.argv[2]
        model_purpose = sys.argv[3].lower() if len(sys.argv) > 3 else 'anomaly'
        max_packets = int(sys.argv[4]) if len(sys.argv) > 4 else 500
        batch_size = int(sys.argv[5]) if len(sys.argv) > 5 else 32
        epochs = int(sys.argv[6]) if len(sys.argv) > 6 else 5

        # 验证模型类型
        if model_purpose not in ['anomaly', 'encryption']:
            print(f"错误: 无效的模型类型 '{model_purpose}'，必须是 'anomaly' 或 'encryption'")
            return

        # 检查文件夹是否存在
        if not os.path.exists(benign_folder):
            print(f"错误: 良性文件夹 '{benign_folder}' 不存在")
            return

        if not os.path.exists(malware_folder):
            print(f"错误: 恶意文件夹 '{malware_folder}' 不存在")
            return

        # 确定模型文件名
        model_filename = 'anomaly_model.pth' if model_purpose == 'anomaly' else 'encryption_model.pth'

        print(f"===== 开始训练ResCNN-ABLGAN模型 ({model_purpose}) =====")
        print(f"良性文件夹: {benign_folder}")
        print(f"恶意文件夹: {malware_folder}")
        print(f"模型类型: {model_purpose}")
        print(f"每个文件最大数据包数: {max_packets}")
        print(f"批次大小: {batch_size}")
        print(f"训练轮次: {epochs}")

        # 加载PCAP文件并提取特征
        X, y = load_pcap_files(benign_folder, malware_folder, max_packets)

        # 训练模型
        model_type = 'advanced'  # ResCNN-ABLGAN对应的模型类型
        analyzer, accuracy = train_model(X, y, model_type, batch_size, epochs)

        # 保存模型
        model_dir = ensure_model_dir()
        model_path = os.path.join(model_dir, model_filename)
        analyzer.save_model(model_path)
        print(f"模型已保存到 {model_path}")

        print("\n===== 训练完成 =====")
        print(f"最终测试集准确率: {accuracy:.2f}%")
        print(f"模型已保存到 {model_path}")
        print("现在可以运行增量训练测试了。")
    else:
        # 如果没有命令行参数，使用默认设置训练两个模型
        print("没有提供命令行参数，将使用默认设置训练异常流量和加密流量两个模型")
        train_both_models()

if __name__ == "__main__":
    main()
