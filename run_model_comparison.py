#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模型比较运行脚本

使用指定的训练集和测试集训练三个模型，并比较它们的性能。
ResCNNABLGAN模型使用GAN联合训练，其他两个使用标准训练。
"""

import os
import argparse
import subprocess

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='运行模型比较')
    
    parser.add_argument('--train-csv', type=str, default='train_dataset_20250415_233855.csv',
                      help='训练集CSV文件路径')
    parser.add_argument('--test-csv', type=str, default='test_dataset_20250415_234156.csv',
                      help='测试集CSV文件路径')
    parser.add_argument('--epochs', type=int, default=10,
                      help='训练轮数')
    parser.add_argument('--batch-size', type=int, default=32,
                      help='批次大小')
    parser.add_argument('--output-dir', type=str, default='results/model_comparison',
                      help='输出目录')
    
    args = parser.parse_args()
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 构建命令
    cmd = [
        "python", "bijiao/model_comparison.py",
        "--models", "simple,cnnlstm,advanced",
        "--train-csv", args.train_csv,
        "--test-csv", args.test_csv,
        "--epochs", str(args.epochs),
        "--batch-size", str(args.batch_size),
        "--output-dir", args.output_dir,
        "--gan"  # 启用GAN联合训练（对于ResCNNABLGAN模型）
    ]
    
    print("开始运行模型比较...")
    print(f"训练集: {args.train_csv}")
    print(f"测试集: {args.test_csv}")
    print(f"训练轮数: {args.epochs}")
    print(f"批次大小: {args.batch_size}")
    print(f"输出目录: {args.output_dir}")
    
    # 运行命令
    subprocess.run(cmd)
    
    print("\n模型比较完成!")
    print(f"结果已保存到 {args.output_dir} 目录")

if __name__ == "__main__":
    main()
