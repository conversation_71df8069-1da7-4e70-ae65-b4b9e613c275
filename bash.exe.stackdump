Stack trace:
Frame         Function      Args
0007FFFFAC20  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFAC20, 0007FFFF9B20) msys-2.0.dll+0x1FE8E
0007FFFFAC20  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAEF8) msys-2.0.dll+0x67F9
0007FFFFAC20  000210046832 (000210286019, 0007FFFFAAD8, 0007FFFFAC20, 000000000000) msys-2.0.dll+0x6832
0007FFFFAC20  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFAC20  000210068E24 (0007FFFFAC30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAF00  00021006A225 (0007FFFFAC30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD086A0000 ntdll.dll
7FFD07070000 KERNEL32.DLL
7FFD05BF0000 KERNELBASE.dll
7FFD07600000 USER32.dll
7FFD05B30000 win32u.dll
7FFD06DC0000 GDI32.dll
000210040000 msys-2.0.dll
7FFD05FC0000 gdi32full.dll
7FFD061A0000 msvcp_win.dll
7FFD06250000 ucrtbase.dll
7FFD06B20000 advapi32.dll
7FFD06700000 msvcrt.dll
7FFD06650000 sechost.dll
7FFD074D0000 RPCRT4.dll
7FFD04F40000 CRYPTBASE.DLL
7FFD06100000 bcryptPrimitives.dll
7FFD08620000 IMM32.DLL
