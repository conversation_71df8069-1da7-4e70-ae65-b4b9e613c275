import matplotlib.pyplot as plt
from matplotlib import rcParams

# 设置字体
rcParams['font.sans-serif'] = ['SimSun']  # 中文用SimSun
rcParams['font.serif'] = ['Times New Roman']  # 英文用Times New Roman
rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 定义章节名称和内容
chapters = [
    "第一章 绪论",
    "第二章 加密流量异常检测基础理论",
    "第三章 基于深度学习的加密流量异常检测模型设计",
    "第四章 系统实现与测试",
    "第五章 结论与展望"
]

contents = [
    "介绍研究背景与意义、国内外研究现状以及研究内容与创新点。从加密流量普及、异常检测必要性和深度学习应用前景三方面阐述研究背景；梳理传统检测技术和深度学习在网络安全领域的研究进展；明确研究目标、内容和技术路线。",
    "系统介绍加密流量的基本概念，包括加密通信原理、常见加密协议分析、加密流量特征与异常类型；阐述深度学习基础理论，包括神经网络原理、CNN与LSTM、注意力机制与残差网络、GAN等；探讨加密流量特征提取方法，为后续模型设计奠定理论基础。",
    "详细阐述系统总体架构，包括功能需求分析、架构设计和关键模块设计；介绍数据预处理模块，包括流量捕获解析、数据清洗标准化和特征提取；重点描述ResCNN-ABLGAN模型设计，包括模型整体架构、各组件设计和多任务学习框架；阐述模型训练与优化方法，包括损失函数设计、优化算法和增量学习机制。",
    "介绍开发环境与技术栈；描述数据采集与预处理实现；阐述模型实现与部署；展示系统功能实现，包括实时监控、异常检测和加密流量分析功能；通过实验结果分析，评估模型性能和系统实用价值。",
    "总结研究工作及成果；分析存在的问题与局限性；提出未来研究方向，包括模型优化、特征工程深入研究和系统功能扩展等。"
]

# 设置图形大小和背景
plt.figure(figsize=(10, 6), facecolor='white')
plt.gca().set_facecolor('white')

# 绘制文本框用于显示章节和内容
for i, (chapter, content) in enumerate(zip(chapters, contents)):
    plt.text(0.1, 1 - i * 0.2, chapter, fontsize=10.5, fontweight='bold')  # 五号字(10.5pt)
    plt.text(0.2, 1 - (i + 1) * 0.2, content, fontsize=10.5)  # 五号字(10.5pt)

# 去除坐标轴和图题
plt.axis('off')
plt.tight_layout()

# 保存图像(无图题)
plt.savefig("论文架构安排图.png",
           bbox_inches='tight',
           pad_inches=0.1,
           facecolor='white',
           dpi=300)

# 显示图像
plt.show()
