# -*- coding: utf-8 -*-
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Rectangle, FancyArrowPatch, Circle
import matplotlib.font_manager as fm
from matplotlib.font_manager import FontProperties
import os
import sys

# 强制使用UTF-8编码
sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')

# 设置高质量绘图参数
plt.rcParams['figure.dpi'] = 600
plt.rcParams['savefig.dpi'] = 600
plt.rcParams['figure.autolayout'] = True
plt.rcParams['text.usetex'] = False  # 禁用LaTeX渲染

# 更可靠的字体设置
try:
    # 直接指定已知的中文字体路径
    font_paths = [
        'C:/Windows/Fonts/msyh.ttc',  # 微软雅黑
        'C:/Windows/Fonts/simhei.ttf',  # 黑体
        'C:/Windows/Fonts/arialuni.ttf'  # Arial Unicode
    ]

    for path in font_paths:
        if os.path.exists(path):
            font_prop = FontProperties(fname=path, size=12)
            plt.rcParams['font.family'] = font_prop.get_name()
            break
    else:
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']

    plt.rcParams['axes.unicode_minus'] = False
except Exception as e:
    print(f"字体设置错误: {str(e)}")
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']  # 回退方案

# 创建图表
fig, ax = plt.subplots(figsize=(16, 12))

# 使用更专业的颜色方案
colors = {
    'center': '#2c3e50',
    'main1': '#e74c3c',
    'main2': '#3498db',
    'main3': '#2ecc71',
    'main4': '#9b59b6',
    'main5': '#1abc9c',
    'sub': '#95a5a6'
}

# 定义中心主题
center_x, center_y = 8, 6
center_radius = 1.5

center = Circle((center_x, center_y), center_radius, fc=colors['center'], ec='black', alpha=0.8, lw=2)
ax.add_patch(center)
ax.text(center_x, center_y, '加密流量异常检测\n未来研究方向', ha='center', va='center', color='white', fontsize=16, fontweight='bold')

# 定义主要分支
main_branches = [
    {'name': '模型优化与改进方向', 'angle': 90, 'color': colors['main1'], 'distance': 4},
    {'name': '特征工程的深入研究', 'angle': 162, 'color': colors['main2'], 'distance': 4},
    {'name': '系统功能扩展与完善', 'angle': 234, 'color': colors['main3'], 'distance': 4},
    {'name': '跨领域应用探索', 'angle': 306, 'color': colors['main4'], 'distance': 4},
    {'name': '理论与实践结合', 'angle': 18, 'color': colors['main5'], 'distance': 4}
]

# 定义子分支
sub_branches = {
    '模型优化与改进方向': [
        '轻量级模型设计',
        '增强对抗鲁棒性',
        '提高模型可解释性',
        '自监督学习探索'
    ],
    '特征工程的深入研究': [
        '动态特征提取',
        '多模态特征融合',
        '时空特征建模',
        '特征隐私保护'
    ],
    '系统功能扩展与完善': [
        '分布式协同检测',
        '自适应响应机制',
        '多层次安全分析',
        '安全知识图谱'
    ],
    '跨领域应用探索': [
        '物联网安全',
        '工业控制系统安全',
        '云原生环境安全',
        '5G/6G网络安全'
    ],
    '理论与实践结合': [
        '安全度量与评估',
        '安全与隐私平衡',
        '理论基础研究',
        '标准化与生态建设'
    ]
}

# 绘制主要分支
for branch in main_branches:
    # 计算分支位置
    angle_rad = np.radians(branch['angle'])
    branch_x = center_x + branch['distance'] * np.cos(angle_rad)
    branch_y = center_y + branch['distance'] * np.sin(angle_rad)

    # 绘制连接线
    line = FancyArrowPatch(
        (center_x, center_y),
        (branch_x, branch_y),
        connectionstyle=f"arc3,rad=0.1",
        arrowstyle="-|>",
        color=branch['color'],
        lw=2
    )
    ax.add_patch(line)

    # 绘制分支节点
    branch_radius = 1.2
    branch_node = Circle((branch_x, branch_y), branch_radius, fc=branch['color'], ec='black', alpha=0.8, lw=2)
    ax.add_patch(branch_node)

    # 添加分支标签
    ax.text(branch_x, branch_y, branch['name'], ha='center', va='center', color='white', fontsize=14, fontweight='bold')

    # 绘制子分支
    sub_items = sub_branches[branch['name']]
    sub_angles = np.linspace(branch['angle'] - 30, branch['angle'] + 30, len(sub_items))

    for i, (sub_item, sub_angle) in enumerate(zip(sub_items, sub_angles)):
        # 计算子分支位置
        sub_angle_rad = np.radians(sub_angle)
        sub_distance = 2.5
        sub_x = branch_x + sub_distance * np.cos(sub_angle_rad)
        sub_y = branch_y + sub_distance * np.sin(sub_angle_rad)

        # 绘制连接线
        sub_line = FancyArrowPatch(
            (branch_x, branch_y),
            (sub_x, sub_y),
            connectionstyle=f"arc3,rad=0.1",
            arrowstyle="-|>",
            color=colors['sub'],
            lw=1.5
        )
        ax.add_patch(sub_line)

        # 绘制子分支节点
        sub_radius = 0.8
        sub_node = Circle((sub_x, sub_y), sub_radius, fc='white', ec=branch['color'], alpha=0.8, lw=2)
        ax.add_patch(sub_node)

        # 添加子分支标签
        ax.text(sub_x, sub_y, sub_item, ha='center', va='center', color='black', fontsize=12)

# 设置图表范围和标题
ax.set_xlim(0, 16)
ax.set_ylim(0, 12)
ax.set_title('图5-2 未来研究方向框架图', fontsize=20, pad=20)

# 隐藏坐标轴
ax.axis('off')

# 添加注释
plt.figtext(0.5, 0.01,
            '注: 图中展示了加密流量异常检测技术的五个主要未来研究方向，每个方向包含四个具体研究课题',
            ha='center', fontsize=12, style='italic')

# 调整布局
plt.tight_layout()

# 保存高质量图表
plt.savefig('tubiao/图5-2_未来研究方向框架图.png', dpi=600, bbox_inches='tight', transparent=True)
plt.savefig('tubiao/图5-2_未来研究方向框架图.pdf', dpi=600, bbox_inches='tight')
plt.savefig('tubiao/图5-2_未来研究方向框架图.svg', bbox_inches='tight')  # 矢量格式

# 直接保存所有格式的图片
print("正在生成图片文件...")
formats = {
    'PNG': ('tubiao/图5-2_未来研究方向框架图.png', {'dpi': 600, 'transparent': True}),
    'PDF': ('tubiao/图5-2_未来研究方向框架图.pdf', {'dpi': 600}),
    'SVG': ('tubiao/图5-2_未来研究方向框架图.svg', {})
}

for fmt, (path, kwargs) in formats.items():
    try:
        plt.savefig(path, bbox_inches='tight', **kwargs)
        print(f"成功生成{fmt}格式图片: {path}")
    except Exception as e:
        print(f"生成{fmt}格式图片失败: {str(e)}")

# 释放内存
plt.close()

# 添加代码注释说明
"""
图5-2 未来研究方向框架图生成脚本
优化内容：
1. 提高输出质量：DPI提升至600，增加SVG矢量格式
2. 优化字体渲染：优先使用微软雅黑，确保中文显示清晰
3. 专业配色方案：使用更专业的颜色组合
4. 代码精简：移除重复的思维导图版本生成
5. 增加注释：提高代码可维护性

使用说明：
1. 运行脚本将生成三种格式的图片：
   - PNG (600dpi)
   - PDF (矢量)
   - SVG (矢量)
2. 如需修改内容，请调整对应的分支和子分支定义
"""
