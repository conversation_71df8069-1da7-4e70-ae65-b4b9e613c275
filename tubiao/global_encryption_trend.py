
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.ticker import PercentFormatter
import os
from matplotlib.font_manager import FontProperties

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 使用微软雅黑
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

try:
    # 更新数据源（根据用户提供的研究报告）
    years = np.arange(2015, 2024)
    encryption_rates = [29, 42, 53, 68, 76, 85, 90, 93, 95]  # 单位：%
    data_sources = [
        "Sandvine Global Internet Phenomena Report",
        "Cisco Annual Internet Report",
        "Google Transparency Report",
        "Let's Encrypt Usage Statistics",
        "Akamai State of the Internet Report",
        "Mozilla HTTP Observatory",
        "Cloudflare SSL Encryption Report",
        "Google HTTPS Transparency Report",
        "最新行业数据"
    ]

    # 创建图表
    plt.figure(figsize=(10, 6), dpi=300)
    plt.plot(years, encryption_rates, 
            marker='o', 
            linestyle='--',
            color='#1f77b4',
            linewidth=2,
            markersize=8)

    # 设置图表样式
    plt.xlabel('年份', fontsize=10.5)  # 五号字
    plt.ylabel('加密流量占比', fontsize=10.5)

    # 添加图题（下方居中）

    # 设置全局字体
    plt.rcParams['font.sans-serif'] = ['SimSun']  # 中文默认宋体
    plt.rcParams['font.family'] = 'sans-serif'
    plt.rcParams['mathtext.fontset'] = 'stix'  # 数学公式字体

    plt.grid(True, linestyle='--', alpha=0.6)
    plt.ylim(20, 100)  # 调整Y轴范围适应新数据
    plt.gca().yaxis.set_major_formatter(PercentFormatter())

    # 添加数据标签
    for x, y in zip(years, encryption_rates):
        plt.text(x, y+1.5, f'{y}%', 
                ha='center',
                fontsize=10)

    # 确保输出目录存在
    os.makedirs('tubiao', exist_ok=True)

    # 设置图表区域白色背景
    plt.gca().set_facecolor('white')

    # 保存图表（不透明背景）
    plt.tight_layout()
    output_path = os.path.join('tubiao', 'global_encryption_trend.png')
    plt.savefig(output_path,
               bbox_inches='tight',
               transparent=False,  # 关闭透明背景
               facecolor='white')  # 设置白色背景
    print(f"图表已成功生成到: {output_path}")

except Exception as e:
    print(f"图表生成失败: {str(e)}")
    raise
