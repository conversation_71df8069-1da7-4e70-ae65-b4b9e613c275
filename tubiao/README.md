# 论文图表生成脚本

本目录包含用于生成论文中各种图表的Python脚本。

## 文件说明

### 主要脚本

- `生成所有图表.py`: 运行所有图表生成脚本的主程序

### 第1章 绪论

- `图1-1_全球加密流量比例增长趋势图.py`: 生成加密流量增长趋势图
- `表1-1_传统加密流量检测方法比较表.py`: 生成传统检测方法比较表

### 第2章 加密流量异常检测基础理论

- `图2-5_CNN与LSTM结构对比图.py`: 生成CNN与LSTM结构对比图
- `图2-8_GAN工作原理图.py`: 生成GAN工作原理图

### 第3章 基于深度学习的加密流量异常检测模型设计

- `图3-1_系统架构图.py`: 生成系统整体架构图
- `图3-8_ResCNN-ABLGAN模型整体架构图.py`: 生成模型架构图

### 第4章 系统实现与测试

- `图4-14_不同异常类型的检测性能对比图.py`: 生成异常类型性能对比图
- `图4-15_消融实验结果图.py`: 生成消融实验结果图
- `表4-5_模型在各数据集上的性能表.py`: 生成数据集性能表
- `表4-6_与现有方法的性能对比表.py`: 生成方法对比表
- `表4-7_系统性能测试结果表.py`: 生成系统性能测试表

### 第5章 结论与展望

- `图5-2_未来研究方向框架图.py`: 生成未来研究方向框架图

## 使用方法

1. 确保已安装所需的Python库：
   ```
   pip install matplotlib numpy pandas seaborn
   ```

2. 运行单个脚本生成特定图表：
   ```
   python 图1-1_全球加密流量比例增长趋势图.py
   ```

3. 或运行主程序生成所有图表：
   ```
   python 生成所有图表.py
   ```

## 输出文件

每个脚本会生成以下格式的输出文件：
- PNG格式图像 (300 DPI)
- PDF格式矢量图
- CSV格式数据文件 (表格数据)
- HTML格式表格 (美化版表格)

## 注意事项

- 脚本中使用的数据为模拟数据，实际论文中应替换为真实研究数据
- 图表样式和颜色可根据需要在脚本中调整
- 部分图表提供了多种样式版本，可根据需要选择使用
