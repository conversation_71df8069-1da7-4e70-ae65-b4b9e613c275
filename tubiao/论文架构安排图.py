import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 使用微软雅黑
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 设置画布
plt.figure(figsize=(10, 6))
ax = plt.gca()
ax.axis('off')

# 定义章节标题和内容
chapters = [
    {"title": "第一章 绪论",
     "content": ["研究背景与意义", "国内外研究现状", "研究内容与创新点"]},
    {"title": "第二章 基础理论",
     "content": ["加密流量基本概念", "深度学习基础理论", "特征提取方法"]},
    {"title": "第三章 模型设计",
     "content": ["系统架构设计", "数据预处理模块", "ResCNN-ABLGAN模型", "训练优化方法"]},
    {"title": "第四章 系统实现",
     "content": ["开发环境与技术栈", "数据采集与预处理", "模型实现与部署", "系统功能实现"]},
    {"title": "第五章 结论展望",
     "content": ["研究成果总结", "存在问题分析", "未来研究方向"]}
]

# 绘制章节框
y_pos = 0.8
box_width = 0.18
box_height = 0.1
content_height = 0.05

for i, chapter in enumerate(chapters):
    # 绘制章节标题框
    x_pos = 0.1 + i * 0.2
    rect = Rectangle((x_pos, y_pos), box_width, box_height,
                     facecolor='#1f77b4', edgecolor='black', alpha=0.7)
    ax.add_patch(rect)
    plt.text(x_pos + box_width/2, y_pos + box_height/2,
             chapter["title"], ha='center', va='center', color='white', weight='bold')

    # 绘制内容框
    for j, item in enumerate(chapter["content"]):
        content_y = y_pos - 0.1 - j * 0.08
        content_rect = Rectangle((x_pos, content_y), box_width, content_height,
                                facecolor='#ff7f0e', edgecolor='black', alpha=0.7)
        ax.add_patch(content_rect)
        plt.text(x_pos + box_width/2, content_y + content_height/2,
                 item, ha='center', va='center', fontsize=8)

    # 绘制连接线
    if i < len(chapters) - 1:
        plt.plot([x_pos + box_width, x_pos + box_width + 0.02],
                 [y_pos + box_height/2, y_pos + box_height/2],
                 'k-', linewidth=1)

# 添加标题
#plt.title('论文架构安排图', fontsize=16, pad=20)

# 保存图像
plt.tight_layout()
plt.savefig('d:\\aaaabysj\\network_monitor最终 - 副本\\tubiao\\论文架构安排图.png',
            dpi=300, bbox_inches='tight')
plt.close()