import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import matplotlib.font_manager as fm
from matplotlib.font_manager import FontProperties
import os

# 检查是否有支持中文的字体
font_path = None
for font in fm.findSystemFonts(fontpaths=None, fontext='ttf'):
    if os.path.basename(font) == 'SimHei.ttf' or os.path.basename(font) == 'simhei.ttf':
        font_path = font
        break
    elif os.path.basename(font) == 'Microsoft YaHei.ttf' or 'msyh.ttf' in os.path.basename(font).lower():
        font_path = font
        break

if font_path:
    font_prop = FontProperties(fname=font_path)
    plt.rcParams['font.family'] = font_prop.get_name()
else:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
    
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 创建数据
categories = ['加密隧道', '命令控制通信', '数据泄露', '扫描活动', 'DDoS攻击']
accuracy = [98.2, 96.7, 97.3, 98.5, 99.1]
precision = [97.5, 95.8, 96.5, 97.8, 98.7]
recall = [97.9, 96.2, 96.9, 98.1, 98.9]
f1_score = [97.7, 96.0, 96.7, 97.9, 98.8]

# 创建DataFrame
df = pd.DataFrame({
    '异常类型': categories,
    '准确率': accuracy,
    '精确率': precision,
    '召回率': recall,
    'F1分数': f1_score
})

# 保存为CSV
df.to_csv('tubiao/图4-14_不同异常类型的检测性能对比.csv', index=False, encoding='utf-8-sig')

# 创建柱状图
fig, ax = plt.subplots(figsize=(14, 10))

# 设置柱状图的宽度和位置
bar_width = 0.2
x = np.arange(len(categories))

# 绘制柱状图
bars1 = ax.bar(x - bar_width*1.5, accuracy, bar_width, label='准确率 (%)', color='#3498db', edgecolor='black', linewidth=1.5)
bars2 = ax.bar(x - bar_width/2, precision, bar_width, label='精确率 (%)', color='#2ecc71', edgecolor='black', linewidth=1.5)
bars3 = ax.bar(x + bar_width/2, recall, bar_width, label='召回率 (%)', color='#e74c3c', edgecolor='black', linewidth=1.5)
bars4 = ax.bar(x + bar_width*1.5, f1_score, bar_width, label='F1分数 (%)', color='#f39c12', edgecolor='black', linewidth=1.5)

# 添加数据标签
def add_labels(bars):
    for bar in bars:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2, height + 0.3,
                f'{height:.1f}%',
                ha='center', va='bottom', fontsize=10)

add_labels(bars1)
add_labels(bars2)
add_labels(bars3)
add_labels(bars4)

# 添加标题和标签
ax.set_title('图4-14 不同异常类型的检测性能对比图', fontsize=20, pad=20)
ax.set_xlabel('异常类型', fontsize=16, labelpad=10)
ax.set_ylabel('性能指标 (%)', fontsize=16, labelpad=10)

# 设置x轴刻度
ax.set_xticks(x)
ax.set_xticklabels(categories, fontsize=14)

# 设置y轴范围
ax.set_ylim(94, 100)

# 添加网格线
ax.grid(True, linestyle='--', alpha=0.7, axis='y')

# 添加图例
ax.legend(fontsize=14, loc='lower right')

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig('tubiao/图4-14_不同异常类型的检测性能对比图.png', dpi=300, bbox_inches='tight')
plt.savefig('tubiao/图4-14_不同异常类型的检测性能对比图.pdf', bbox_inches='tight')

# 显示图表
plt.show()

# 创建雷达图版本
fig = plt.figure(figsize=(12, 10))
ax = fig.add_subplot(111, polar=True)

# 准备雷达图数据
categories = np.array(categories)
N = len(categories)
angles = np.linspace(0, 2*np.pi, N, endpoint=False).tolist()
angles += angles[:1]  # 闭合雷达图

# 准备性能指标数据
accuracy_radar = np.array(accuracy)
accuracy_radar = np.append(accuracy_radar, accuracy_radar[0])

precision_radar = np.array(precision)
precision_radar = np.append(precision_radar, precision_radar[0])

recall_radar = np.array(recall)
recall_radar = np.append(recall_radar, recall_radar[0])

f1_score_radar = np.array(f1_score)
f1_score_radar = np.append(f1_score_radar, f1_score_radar[0])

# 绘制雷达图
ax.plot(angles, accuracy_radar, 'o-', linewidth=2, label='准确率 (%)', color='#3498db')
ax.fill(angles, accuracy_radar, alpha=0.1, color='#3498db')

ax.plot(angles, precision_radar, 's-', linewidth=2, label='精确率 (%)', color='#2ecc71')
ax.fill(angles, precision_radar, alpha=0.1, color='#2ecc71')

ax.plot(angles, recall_radar, '^-', linewidth=2, label='召回率 (%)', color='#e74c3c')
ax.fill(angles, recall_radar, alpha=0.1, color='#e74c3c')

ax.plot(angles, f1_score_radar, 'd-', linewidth=2, label='F1分数 (%)', color='#f39c12')
ax.fill(angles, f1_score_radar, alpha=0.1, color='#f39c12')

# 设置雷达图属性
ax.set_theta_offset(np.pi / 2)  # 从顶部开始
ax.set_theta_direction(-1)  # 顺时针方向

# 设置刻度标签
ax.set_xticks(angles[:-1])
ax.set_xticklabels(categories, fontsize=14)

# 设置y轴范围
ax.set_ylim(94, 100)
ax.set_yticks(np.arange(94, 101, 1))
ax.set_yticklabels([f'{i}%' for i in range(94, 101, 1)], fontsize=12)

# 添加图例
ax.legend(loc='upper right', fontsize=14, bbox_to_anchor=(0.1, 0.1))

# 添加标题
plt.title('图4-14 不同异常类型的检测性能对比图 (雷达图版本)', fontsize=20, pad=20)

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig('tubiao/图4-14_不同异常类型的检测性能对比图_雷达版.png', dpi=300, bbox_inches='tight')
plt.savefig('tubiao/图4-14_不同异常类型的检测性能对比图_雷达版.pdf', bbox_inches='tight')

# 显示图表
plt.show()
