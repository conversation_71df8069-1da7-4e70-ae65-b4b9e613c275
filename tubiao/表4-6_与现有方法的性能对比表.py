import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import matplotlib.font_manager as fm
from matplotlib.font_manager import FontProperties
import os
import seaborn as sns

# 检查是否有支持中文的字体
font_path = None
for font in fm.findSystemFonts(fontpaths=None, fontext='ttf'):
    if os.path.basename(font) == 'SimHei.ttf' or os.path.basename(font) == 'simhei.ttf':
        font_path = font
        break
    elif os.path.basename(font) == 'Microsoft YaHei.ttf' or 'msyh.ttf' in os.path.basename(font).lower():
        font_path = font
        break

if font_path:
    font_prop = FontProperties(fname=font_path)
    plt.rcParams['font.family'] = font_prop.get_name()
else:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
    
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 创建数据
data = {
    '模型': ['CNN', 'LSTM', 'CNN-LSTM', 'Attention-LSTM', 'ResNet', 'GAN-based', 'ResCNN-ABLGAN (ours)', 'ResCNN-ABLGAN-Light (ours)'],
    '准确率 (%)': [92.3, 93.5, 95.1, 96.2, 94.8, 95.5, 98.7, 97.5],
    '精确率 (%)': [91.5, 92.7, 94.3, 95.5, 94.0, 94.8, 97.9, 96.8],
    '召回率 (%)': [91.9, 93.1, 94.8, 96.0, 94.5, 95.2, 98.3, 97.2],
    'F1分数': [0.918, 0.929, 0.947, 0.958, 0.942, 0.950, 0.981, 0.970],
    'AUC': [0.957, 0.963, 0.975, 0.981, 0.973, 0.978, 0.995, 0.989],
    '训练时间 (相对)': ['1.0×', '1.2×', '1.5×', '1.7×', '1.3×', '2.2×', '2.0×', '1.6×'],
    '推理速度 (相对)': ['1.0×', '0.9×', '0.8×', '0.7×', '0.9×', '0.6×', '0.6×', '0.8×'],
    '参数量 (百万)': [2.5, 3.8, 5.2, 6.5, 4.7, 7.8, 8.5, 4.2]
}

# 创建DataFrame
df = pd.DataFrame(data)

# 保存为CSV
df.to_csv('tubiao/表4-6_与现有方法的性能对比表.csv', index=False, encoding='utf-8-sig')

# 创建表格可视化
fig, ax = plt.subplots(figsize=(16, 10))
ax.axis('tight')
ax.axis('off')

# 创建表格
table = ax.table(cellText=df.values, colLabels=df.columns, loc='center', 
                cellLoc='center', rowLoc='center')

# 设置表格样式
table.auto_set_font_size(False)
table.set_fontsize(12)
table.scale(1, 1.5)  # 调整表格大小

# 设置标题
plt.title('表4-6 与现有方法的性能对比表', fontsize=20, pad=20)

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig('tubiao/表4-6_与现有方法的性能对比表.png', dpi=300, bbox_inches='tight')
plt.savefig('tubiao/表4-6_与现有方法的性能对比表.pdf', bbox_inches='tight')

# 显示图表
plt.show()

# 创建一个热力图版本，更直观地展示性能对比
# 选择数值型列进行可视化
numeric_cols = ['准确率 (%)', '精确率 (%)', '召回率 (%)', 'F1分数', 'AUC', '参数量 (百万)']
df_numeric = df[['模型'] + numeric_cols].copy()

# 将F1分数和AUC转换为百分比格式，便于可视化
df_numeric['F1分数'] = df_numeric['F1分数'] * 100
df_numeric['AUC'] = df_numeric['AUC'] * 100

# 设置模型为索引
df_heatmap = df_numeric.set_index('模型')

# 创建热力图
plt.figure(figsize=(14, 10))
sns.heatmap(df_heatmap, annot=True, cmap='YlGnBu', fmt='.1f', linewidths=.5, cbar_kws={'label': '性能值'})

plt.title('表4-6 与现有方法的性能对比热力图', fontsize=20, pad=20)
plt.tight_layout()

# 保存热力图
plt.savefig('tubiao/表4-6_与现有方法的性能对比热力图.png', dpi=300, bbox_inches='tight')
plt.savefig('tubiao/表4-6_与现有方法的性能对比热力图.pdf', bbox_inches='tight')

# 显示热力图
plt.show()

# 另外，生成一个更美观的HTML表格
html_table = df.to_html(index=False)
with open('tubiao/表4-6_与现有方法的性能对比表.html', 'w', encoding='utf-8') as f:
    f.write(f'''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>表4-6 与现有方法的性能对比表</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 20px;
            }}
            table {{
                border-collapse: collapse;
                width: 100%;
                margin-top: 20px;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 12px;
                text-align: center;
            }}
            th {{
                background-color: #f2f2f2;
                font-weight: bold;
            }}
            tr:nth-child(even) {{
                background-color: #f9f9f9;
            }}
            tr:hover {{
                background-color: #f5f5f5;
            }}
            .highlight {{
                background-color: #e8f4f8;
                font-weight: bold;
            }}
            h1 {{
                text-align: center;
            }}
        </style>
    </head>
    <body>
        <h1>表4-6 与现有方法的性能对比表</h1>
        {html_table.replace('ResCNN-ABLGAN (ours)', '<span class="highlight">ResCNN-ABLGAN (ours)</span>').replace('ResCNN-ABLGAN-Light (ours)', '<span class="highlight">ResCNN-ABLGAN-Light (ours)</span>')}
    </body>
    </html>
    ''')
