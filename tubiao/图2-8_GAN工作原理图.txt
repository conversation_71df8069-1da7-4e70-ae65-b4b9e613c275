图2-8 GAN工作原理图

[此处为图表内容]
- 图表类型：流程示意图
- 主要组件：
  1. 生成器(Generator)
     * 输入：随机噪声向量z
     * 内部结构：多层神经网络
     * 输出：生成的样本G(z)
  
  2. 判别器(Discriminator)
     * 输入：真实样本x或生成样本G(z)
     * 内部结构：多层神经网络
     * 输出：真实概率D(x)或D(G(z))
  
  3. 对抗训练过程
     * 判别器目标：最大化D(x)，最小化D(G(z))
     * 生成器目标：最大化D(G(z))
     * 极小极大博弈：min_G max_D V(D,G)
  
  4. 在加密流量分析中的应用
     * 生成器：生成逼真的加密流量特征
     * 判别器：区分正常流量和异常流量
     * 应用场景：数据增强、对抗训练、异常检测

- 使用不同颜色区分生成器和判别器
- 添加数据流向箭头和训练过程循环
- 包含损失函数和优化目标的数学表达
