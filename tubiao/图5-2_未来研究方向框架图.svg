<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="1144.8pt" height="861.941209pt" viewBox="0 0 1144.8 861.941209" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-05-07T10:52:16.807454</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.0, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 861.941209 
L 1144.8 861.941209 
L 1144.8 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 572.4 547.681026 
C 600.504899 547.681026 627.462477 537.077064 647.335641 518.20454 
C 667.208805 499.332016 678.375 473.731787 678.375 447.042007 
C 678.375 420.352227 667.208805 394.751999 647.335641 375.879474 
C 627.462477 357.00695 600.504899 346.402988 572.4 346.402988 
C 544.295101 346.402988 517.337523 357.00695 497.464359 375.879474 
C 477.591195 394.751999 466.425 420.352227 466.425 447.042007 
C 466.425 473.731787 477.591195 499.332016 497.464359 518.20454 
C 517.337523 537.077064 544.295101 547.681026 572.4 547.681026 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #2c3e50; opacity: 0.8; stroke: #000000; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_3">
    <path d="M 572.789435 445.080496 
Q 598.847447 312.85761 573.222168 182.828595 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #e74c3c; stroke-width: 2; stroke-linecap: round"/>
    <path d="M 573.103284 183.259718 
L 573.222168 182.828595 
L 573.495736 183.182376 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #e74c3c; stroke: #e74c3c; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="patch_4">
    <path d="M 572.4 259.182505 
C 594.883919 259.182505 616.449981 250.699335 632.348513 235.601316 
C 648.247044 220.503296 657.18 200.023113 657.18 178.67129 
C 657.18 157.319466 648.247044 136.839283 632.348513 121.741263 
C 616.449981 106.643244 594.883919 98.160074 572.4 98.160074 
C 549.916081 98.160074 528.350019 106.643244 512.451487 121.741263 
C 496.552956 136.839283 487.62 157.319466 487.62 178.67129 
C 487.62 200.023113 496.552956 220.503296 512.451487 235.601316 
C 528.350019 250.699335 549.916081 259.182505 572.4 259.182505 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #e74c3c; opacity: 0.8; stroke: #000000; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 573.75179 177.195068 
Q 630.751285 114.670106 659.444624 36.862318 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 659.118579 37.168413 
L 659.444624 36.862318 
L 659.493874 37.306812 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_6">
    <path d="M 660.7125 87.085521 
C 675.701779 87.085521 690.079154 81.430075 700.678175 71.364729 
C 711.277196 61.299382 717.2325 47.645927 717.2325 33.411378 
C 717.2325 19.176828 711.277196 5.523373 700.678175 -4.541973 
C 690.079154 -14.607319 675.701779 -20.262766 660.7125 -20.262766 
C 645.723221 -20.262766 631.345846 -14.607319 620.746825 -4.541973 
C 610.147804 5.523373 604.1925 19.176828 604.1925 33.411378 
C 604.1925 47.645927 610.147804 61.299382 620.746825 71.364729 
C 631.345846 81.430075 645.723221 87.085521 660.7125 87.085521 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #e74c3c; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_7">
    <path d="M 573.139152 176.813875 
Q 603.87206 99.077002 603.109365 17.166231 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 602.913098 17.568076 
L 603.109365 17.166231 
L 603.313081 17.564352 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_8">
    <path d="M 603.070609 67.161956 
C 618.059889 67.161956 632.437264 61.50651 643.036285 51.441163 
C 653.635306 41.375817 659.590609 27.722362 659.590609 13.487812 
C 659.590609 -0.746737 653.635306 -14.400192 643.036285 -24.465538 
C 632.437264 -34.530885 618.059889 -40.186331 603.070609 -40.186331 
C 588.08133 -40.186331 573.703955 -34.530885 563.104934 -24.465538 
C 552.505913 -14.400192 546.550609 -0.746737 546.550609 13.487812 
C 546.550609 27.722362 552.505913 41.375817 563.104934 51.441163 
C 573.703955 61.50651 588.08133 67.161956 603.070609 67.161956 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #e74c3c; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_9">
    <path d="M 572.423118 176.67234 
Q 573.201896 93.084503 543.086028 16.90714 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 543.047095 17.352656 
L 543.086028 16.90714 
L 543.419081 17.205596 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_10">
    <path d="M 541.729391 67.161956 
C 556.71867 67.161956 571.096045 61.50651 581.695066 51.441163 
C 592.294087 41.375817 598.249391 27.722362 598.249391 13.487812 
C 598.249391 -0.746737 592.294087 -14.400192 581.695066 -24.465538 
C 571.096045 -34.530885 556.71867 -40.186331 541.729391 -40.186331 
C 526.740111 -40.186331 512.362736 -34.530885 501.763715 -24.465538 
C 491.164694 -14.400192 485.209391 -0.746737 485.209391 13.487812 
C 485.209391 27.722362 491.164694 41.375817 501.763715 51.441163 
C 512.362736 61.50651 526.740111 67.161956 541.729391 67.161956 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #e74c3c; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_11">
    <path d="M 571.711611 176.791745 
Q 542.437658 97.410708 486.567633 36.125311 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 486.689314 36.555653 
L 486.567633 36.125311 
L 486.984915 36.286172 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_12">
    <path d="M 484.0875 87.085521 
C 499.076779 87.085521 513.454154 81.430075 524.053175 71.364729 
C 534.652196 61.299382 540.6075 47.645927 540.6075 33.411378 
C 540.6075 19.176828 534.652196 5.523373 524.053175 -4.541973 
C 513.454154 -14.607319 499.076779 -20.262766 484.0875 -20.262766 
C 469.098221 -20.262766 454.720846 -14.607319 444.121825 -4.541973 
C 433.522804 5.523373 427.5675 19.176828 427.5675 33.411378 
C 427.5675 47.645927 433.522804 61.299382 444.121825 71.364729 
C 454.720846 81.430075 469.098221 87.085521 484.0875 87.085521 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #e74c3c; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_13">
    <path d="M 570.640659 446.091481 
Q 446.195484 379.072633 307.847744 364.550759 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #3498db; stroke-width: 2; stroke-linecap: round"/>
    <path d="M 308.22468 364.791423 
L 307.847744 364.550759 
L 308.266437 364.393609 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #3498db; stroke: #3498db; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="patch_14">
    <path d="M 303.631428 444.62211 
C 326.115347 444.62211 347.68141 436.13894 363.579941 421.040921 
C 379.478473 405.942902 388.411428 385.462719 388.411428 364.110895 
C 388.411428 342.759071 379.478473 322.278888 363.579941 307.180868 
C 347.68141 292.082849 326.115347 283.599679 303.631428 283.599679 
C 281.14751 283.599679 259.581447 292.082849 243.682916 307.180868 
C 227.784384 322.278888 218.851428 342.759071 218.851428 364.110895 
C 218.851428 385.462719 227.784384 405.942902 243.682916 421.040921 
C 259.581447 436.13894 281.14751 444.62211 303.631428 444.62211 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #3498db; opacity: 0.8; stroke: #000000; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_15">
    <path d="M 302.562372 362.419246 
Q 256.721598 290.234225 188.443078 241.591551 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 188.652814 241.986533 
L 188.443078 241.591551 
L 188.884906 241.660752 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_16">
    <path d="M 185.446235 293.136094 
C 200.435514 293.136094 214.812889 287.480648 225.41191 277.415302 
C 236.010931 267.349955 241.966235 253.6965 241.966235 239.461951 
C 241.966235 225.227402 236.010931 211.573946 225.41191 201.5086 
C 214.812889 191.443254 200.435514 185.787807 185.446235 185.787807 
C 170.456956 185.787807 156.079581 191.443254 145.48056 201.5086 
C 134.881539 211.573946 128.926235 225.227402 128.926235 239.461951 
C 128.926235 253.6965 134.881539 267.349955 145.48056 277.415302 
C 156.079581 287.480648 170.456956 293.136094 185.446235 293.136094 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #3498db; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_17">
    <path d="M 302.057058 362.881869 
Q 233.357761 309.490453 151.223031 286.358732 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 151.553836 286.659677 
L 151.223031 286.358732 
L 151.66227 286.274655 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_18">
    <path d="M 147.68081 339.039776 
C 162.670089 339.039776 177.047464 333.384329 187.646485 323.318983 
C 198.245506 313.253637 204.20081 299.600181 204.20081 285.365632 
C 204.20081 271.131083 198.245506 257.477628 187.646485 247.412281 
C 177.047464 237.346935 162.670089 231.691489 147.68081 231.691489 
C 132.691531 231.691489 118.314156 237.346935 107.715135 247.412281 
C 97.116114 257.477628 91.16081 271.131083 91.16081 285.365632 
C 91.16081 299.600181 97.116114 313.253637 107.715135 323.318983 
C 118.314156 333.384329 132.691531 339.039776 147.68081 339.039776 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #3498db; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_19">
    <path d="M 301.737189 363.466436 
Q 218.46114 335.333118 132.395729 340.540774 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 132.807078 340.71625 
L 132.395729 340.540774 
L 132.782919 340.31698 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_20">
    <path d="M 128.725331 394.441298 
C 143.71461 394.441298 158.091985 388.785851 168.691006 378.720505 
C 179.290027 368.655159 185.245331 355.001703 185.245331 340.767154 
C 185.245331 326.532605 179.290027 312.87915 168.691006 302.813803 
C 158.091985 292.748457 143.71461 287.093011 128.725331 287.093011 
C 113.736052 287.093011 99.358677 292.748457 88.759656 302.813803 
C 78.160635 312.87915 72.205331 326.532605 72.205331 340.767154 
C 72.205331 355.001703 78.160635 368.655159 88.759656 378.720505 
C 99.358677 388.785851 113.736052 394.441298 128.725331 394.441298 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #3498db; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_21">
    <path d="M 301.632925 364.118733 
Q 213.83788 364.651136 134.260322 397.575333 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 134.706398 397.607217 
L 134.260322 397.575333 
L 134.553475 397.237603 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_22">
    <path d="M 130.866109 452.658419 
C 145.855388 452.658419 160.232763 447.002973 170.831784 436.937627 
C 181.430805 426.87228 187.386109 413.218825 187.386109 398.984276 
C 187.386109 384.749726 181.430805 371.096271 170.831784 361.030925 
C 160.232763 350.965579 145.855388 345.310132 130.866109 345.310132 
C 115.876829 345.310132 101.499454 350.965579 90.900433 361.030925 
C 80.301412 371.096271 74.346109 384.749726 74.346109 398.984276 
C 74.346109 413.218825 80.301412 426.87228 90.900433 436.937627 
C 101.499454 447.002973 115.876829 452.658419 130.866109 452.658419 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #3498db; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_23">
    <path d="M 570.896364 448.365355 
Q 467.941902 539.228444 408.162781 660.361192 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #2ecc71; stroke-width: 2; stroke-linecap: round"/>
    <path d="M 408.519148 660.091002 
L 408.162781 660.361192 
L 408.16045 659.913984 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #2ecc71; stroke: #2ecc71; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="patch_24">
    <path d="M 406.291888 744.669694 
C 428.775807 744.669694 450.341869 736.186524 466.240401 721.088505 
C 482.138932 705.990485 491.071888 685.510302 491.071888 664.158479 
C 491.071888 642.806655 482.138932 622.326472 466.240401 607.228452 
C 450.341869 592.130433 428.775807 583.647263 406.291888 583.647263 
C 383.807969 583.647263 362.241906 592.130433 346.343375 607.228452 
C 330.444843 622.326472 321.511888 642.806655 321.511888 664.158479 
C 321.511888 685.510302 330.444843 705.990485 346.343375 721.088505 
C 362.241906 736.186524 383.807969 744.669694 406.291888 744.669694 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #2ecc71; opacity: 0.8; stroke: #000000; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_25">
    <path d="M 404.336279 664.564624 
Q 318.945297 682.490727 247.985176 730.321226 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 248.428648 730.263497 
L 247.985176 730.321226 
L 248.205076 729.931811 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_26">
    <path d="M 244.936921 786.05525 
C 259.9262 786.05525 274.303576 780.399804 284.902597 770.334457 
C 295.501617 760.269111 301.456921 746.615656 301.456921 732.381107 
C 301.456921 718.146557 295.501617 704.493102 284.902597 694.427756 
C 274.303576 684.362409 259.9262 678.706963 244.936921 678.706963 
C 229.947642 678.706963 215.570267 684.362409 204.971246 694.427756 
C 194.372225 704.493102 188.416921 718.146557 188.416921 732.381107 
C 188.416921 746.615656 194.372225 760.269111 204.971246 770.334457 
C 215.570267 780.399804 229.947642 786.05525 244.936921 786.05525 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #2ecc71; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_27">
    <path d="M 404.585675 665.197432 
Q 331.377084 709.995842 281.418801 777.71201 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 281.817212 777.508864 
L 281.418801 777.71201 
L 281.495331 777.271393 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_28">
    <path d="M 279.238495 834.34885 
C 294.227775 834.34885 308.60515 828.693404 319.204171 818.628058 
C 329.803192 808.562711 335.758495 794.909256 335.758495 780.674707 
C 335.758495 766.440158 329.803192 752.786702 319.204171 742.721356 
C 308.60515 732.65601 294.227775 727.000563 279.238495 727.000563 
C 264.249216 727.000563 249.871841 732.65601 239.27282 742.721356 
C 228.673799 752.786702 222.718495 766.440158 222.718495 780.674707 
C 222.718495 794.909256 228.673799 808.562711 239.27282 818.628058 
C 249.871841 828.693404 264.249216 834.34885 279.238495 834.34885 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #2ecc71; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_29">
    <path d="M 405.050019 665.727184 
Q 352.847041 731.972054 329.881418 811.382695 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 330.184671 811.054004 
L 329.881418 811.382695 
L 329.800417 810.942878 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_30">
    <path d="M 328.864584 868.588874 
C 343.853863 868.588874 358.231238 862.933428 368.830259 852.868081 
C 379.42928 842.802735 385.384584 829.14928 385.384584 814.914731 
C 385.384584 800.680181 379.42928 787.026726 368.830259 776.96138 
C 358.231238 766.896033 343.853863 761.240587 328.864584 761.240587 
C 313.875305 761.240587 299.49793 766.896033 288.898909 776.96138 
C 278.299888 787.026726 272.344584 800.680181 272.344584 814.914731 
C 272.344584 829.14928 278.299888 842.802735 288.898909 852.868081 
C 299.49793 862.933428 313.875305 868.588874 328.864584 868.588874 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #2ecc71; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_31">
    <path d="M 405.689925 666.068062 
Q 380.764905 745.76271 387.521483 827.307783 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 387.68777 826.892634 
L 387.521483 827.307783 
L 387.289136 826.925664 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_32">
    <path d="M 387.829548 884.645469 
C 402.818827 884.645469 417.196202 878.990022 427.795223 868.924676 
C 438.394244 858.85933 444.349548 845.205875 444.349548 830.971325 
C 444.349548 816.736776 438.394244 803.083321 427.795223 793.017974 
C 417.196202 782.952628 402.818827 777.297182 387.829548 777.297182 
C 372.840269 777.297182 358.462894 782.952628 347.863873 793.017974 
C 337.264852 803.083321 331.309548 816.736776 331.309548 830.971325 
C 331.309548 845.205875 337.264852 858.85933 347.863873 868.924676 
C 358.462894 878.990022 372.840269 884.645469 387.829548 884.645469 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #2ecc71; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_33">
    <path d="M 573.283965 448.839444 
Q 634.053561 571.976678 735.332405 661.359459 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #9b59b6; stroke-width: 2; stroke-linecap: round"/>
    <path d="M 735.164838 660.944825 
L 735.332405 661.359459 
L 734.900158 661.244732 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #9b59b6; stroke: #9b59b6; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="patch_34">
    <path d="M 738.508112 744.669694 
C 760.992031 744.669694 782.558094 736.186524 798.456625 721.088505 
C 814.355157 705.990485 823.288112 685.510302 823.288112 664.158479 
C 823.288112 642.806655 814.355157 622.326472 798.456625 607.228452 
C 782.558094 592.130433 760.992031 583.647263 738.508112 583.647263 
C 716.024193 583.647263 694.458131 592.130433 678.559599 607.228452 
C 662.661068 622.326472 653.728112 642.806655 653.728112 664.158479 
C 653.728112 685.510302 662.661068 705.990485 678.559599 721.088505 
C 694.458131 736.186524 716.024193 744.669694 738.508112 744.669694 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #9b59b6; opacity: 0.8; stroke: #000000; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_35">
    <path d="M 738.338297 666.15348 
Q 731.443771 749.369818 755.868717 827.463815 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 755.940197 827.022351 
L 755.868717 827.463815 
L 755.558433 827.141753 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_36">
    <path d="M 756.970452 884.645469 
C 771.959731 884.645469 786.337106 878.990022 796.936127 868.924676 
C 807.535148 858.85933 813.490452 845.205875 813.490452 830.971325 
C 813.490452 816.736776 807.535148 803.083321 796.936127 793.017974 
C 786.337106 782.952628 771.959731 777.297182 756.970452 777.297182 
C 741.981173 777.297182 727.603798 782.952628 717.004777 793.017974 
C 706.405756 803.083321 700.450452 816.736776 700.450452 830.971325 
C 700.450452 845.205875 706.405756 858.85933 717.004777 868.924676 
C 727.603798 878.990022 741.981173 884.645469 756.970452 884.645469 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #9b59b6; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_37">
    <path d="M 739.05968 666.081717 
Q 762.491612 747.103186 813.656977 812.030646 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 813.566483 811.592684 
L 813.656977 812.030646 
L 813.252311 811.840264 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_38">
    <path d="M 815.935416 868.588874 
C 830.924695 868.588874 845.30207 862.933428 855.901091 852.868081 
C 866.500112 842.802735 872.455416 829.14928 872.455416 814.914731 
C 872.455416 800.680181 866.500112 787.026726 855.901091 776.96138 
C 845.30207 766.896033 830.924695 761.240587 815.935416 761.240587 
C 800.946137 761.240587 786.568762 766.896033 775.969741 776.96138 
C 765.37072 787.026726 759.415416 800.680181 759.415416 814.914731 
C 759.415416 829.14928 765.37072 842.802735 775.969741 852.868081 
C 786.568762 862.933428 800.946137 868.588874 815.935416 868.588874 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #9b59b6; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_39">
    <path d="M 739.690534 665.768591 
Q 790.643953 734.834824 862.421582 778.75841 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 862.184788 778.37903 
L 862.421582 778.75841 
L 861.976003 778.720217 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_40">
    <path d="M 865.561505 834.34885 
C 880.550784 834.34885 894.928159 828.693404 905.52718 818.628058 
C 916.126201 808.562711 922.081505 794.909256 922.081505 780.674707 
C 922.081505 766.440158 916.126201 752.786702 905.52718 742.721356 
C 894.928159 732.65601 880.550784 727.000563 865.561505 727.000563 
C 850.572225 727.000563 836.19485 732.65601 825.595829 742.721356 
C 814.996808 752.786702 809.041505 766.440158 809.041505 780.674707 
C 809.041505 794.909256 814.996808 808.562711 825.595829 818.628058 
C 836.19485 828.693404 850.572225 834.34885 865.561505 834.34885 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #9b59b6; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_41">
    <path d="M 740.161915 665.278432 
Q 812.512327 714.047099 896.261694 731.629447 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 895.91132 731.35153 
L 896.261694 731.629447 
L 895.829136 731.742996 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_42">
    <path d="M 899.863079 786.05525 
C 914.852358 786.05525 929.229733 780.399804 939.828754 770.334457 
C 950.427775 760.269111 956.383079 746.615656 956.383079 732.381107 
C 956.383079 718.146557 950.427775 704.493102 939.828754 694.427756 
C 929.229733 684.362409 914.852358 678.706963 899.863079 678.706963 
C 884.8738 678.706963 870.496424 684.362409 859.897403 694.427756 
C 849.298383 704.493102 843.343079 718.146557 843.343079 732.381107 
C 843.343079 746.615656 849.298383 760.269111 859.897403 770.334457 
C 870.496424 780.399804 884.8738 786.05525 899.863079 786.05525 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #9b59b6; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_43">
    <path d="M 574.389049 446.835932 
Q 714.960835 432.081262 837.437473 366.123281 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #1abc9c; stroke-width: 2; stroke-linecap: round"/>
    <path d="M 836.990465 366.136853 
L 837.437473 366.123281 
L 837.180125 366.48903 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #1abc9c; stroke: #1abc9c; stroke-width: 2; stroke-linecap: round"/>
   </g>
   <g id="patch_44">
    <path d="M 841.168572 444.62211 
C 863.65249 444.62211 885.218553 436.13894 901.117084 421.040921 
C 917.015616 405.942902 925.948572 385.462719 925.948572 364.110895 
C 925.948572 342.759071 917.015616 322.278888 901.117084 307.180868 
C 885.218553 292.082849 863.65249 283.599679 841.168572 283.599679 
C 818.684653 283.599679 797.11859 292.082849 781.220059 307.180868 
C 765.321527 322.278888 756.388572 342.759071 756.388572 364.110895 
C 756.388572 385.462719 765.321527 405.942902 781.220059 421.040921 
C 797.11859 436.13894 818.684653 444.62211 841.168572 444.62211 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #1abc9c; opacity: 0.8; stroke: #000000; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_45">
    <path d="M 843.013633 364.878895 
Q 924.140865 398.44414 1010.258911 398.966273 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 1009.860131 398.763851 
L 1010.258911 398.966273 
L 1009.857706 399.163844 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_46">
    <path d="M 1013.933891 452.658419 
C 1028.923171 452.658419 1043.300546 447.002973 1053.899567 436.937627 
C 1064.498588 426.87228 1070.453891 413.218825 1070.453891 398.984276 
C 1070.453891 384.749726 1064.498588 371.096271 1053.899567 361.030925 
C 1043.300546 350.965579 1028.923171 345.310132 1013.933891 345.310132 
C 998.944612 345.310132 984.567237 350.965579 973.968216 361.030925 
C 963.369195 371.096271 957.413891 384.749726 957.413891 398.984276 
C 957.413891 413.218825 963.369195 426.87228 973.968216 436.937627 
C 984.567237 447.002973 998.944612 452.658419 1013.933891 452.658419 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #1abc9c; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_47">
    <path d="M 843.165523 364.236018 
Q 930.90491 369.54486 1012.592112 341.948199 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 1012.149141 341.886745 
L 1012.592112 341.948199 
L 1012.277166 342.265703 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_48">
    <path d="M 1016.074669 394.441298 
C 1031.063948 394.441298 1045.441323 388.785851 1056.040344 378.720505 
C 1066.639365 368.655159 1072.594669 355.001703 1072.594669 340.767154 
C 1072.594669 326.532605 1066.639365 312.87915 1056.040344 302.813803 
C 1045.441323 292.748457 1031.063948 287.093011 1016.074669 287.093011 
C 1001.08539 287.093011 986.708015 292.748457 976.108994 302.813803 
C 965.509973 312.87915 959.554669 326.532605 959.554669 340.767154 
C 959.554669 355.001703 965.509973 368.655159 976.108994 378.720505 
C 986.708015 388.785851 1001.08539 394.441298 1016.074669 394.441298 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #1abc9c; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_49">
    <path d="M 843.092238 363.573631 
Q 926.841609 339.98805 994.217112 287.626499 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 993.778549 287.714036 
L 994.217112 287.626499 
L 994.024004 288.029871 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_50">
    <path d="M 997.11919 339.039776 
C 1012.108469 339.039776 1026.485844 333.384329 1037.084865 323.318983 
C 1047.683886 313.253637 1053.63919 299.600181 1053.63919 285.365632 
C 1053.63919 271.131083 1047.683886 257.477628 1037.084865 247.412281 
C 1026.485844 237.346935 1012.108469 231.691489 997.11919 231.691489 
C 982.129911 231.691489 967.752536 237.346935 957.153515 247.412281 
C 946.554494 257.477628 940.59919 271.131083 940.59919 285.365632 
C 940.59919 299.600181 946.554494 313.253637 957.153515 323.318983 
C 967.752536 333.384329 982.129911 339.039776 997.11919 339.039776 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #1abc9c; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_51">
    <path d="M 842.800952 362.953359 
Q 912.445109 313.337381 957.386607 242.567841 
" clip-path="url(#p9e98fc1fd4)" style="fill: none; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
    <path d="M 957.003342 242.798292 
L 957.386607 242.567841 
L 957.341009 243.012724 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #95a5a6; stroke: #95a5a6; stroke-width: 1.5; stroke-linecap: round"/>
   </g>
   <g id="patch_52">
    <path d="M 959.353765 293.136094 
C 974.343044 293.136094 988.720419 287.480648 999.31944 277.415302 
C 1009.918461 267.349955 1015.873765 253.6965 1015.873765 239.461951 
C 1015.873765 225.227402 1009.918461 211.573946 999.31944 201.5086 
C 988.720419 191.443254 974.343044 185.787807 959.353765 185.787807 
C 944.364486 185.787807 929.987111 191.443254 919.38809 201.5086 
C 908.789069 211.573946 902.833765 225.227402 902.833765 239.461951 
C 902.833765 253.6965 908.789069 267.349955 919.38809 277.415302 
C 929.987111 287.480648 944.364486 293.136094 959.353765 293.136094 
z
" clip-path="url(#p9e98fc1fd4)" style="fill: #ffffff; opacity: 0.8; stroke: #1abc9c; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="text_1">
    <!-- 加密流量异常检测 -->
    <g style="fill: #ffffff" transform="translate(508.4 441.785507) scale(0.16 -0.16)">
     <defs>
      <path id="MicrosoftYaHei-Bold-52a0" d="M 1475 -716 
Q 1381 -278 1256 222 
Q 1644 197 1894 197 
Q 2044 197 2126 261 
Q 2209 325 2228 437 
Q 2247 550 2272 1386 
Q 2297 2222 2306 3472 
L 1700 3472 
Q 1666 503 575 -878 
Q 356 -453 81 16 
Q 863 1125 891 3472 
L 175 3472 
L 175 4291 
L 909 4291 
Q 919 4828 919 5322 
L 1731 5322 
L 1716 4291 
L 3131 4291 
L 3091 1766 
L 3050 300 
Q 3034 -147 2861 -378 
Q 2688 -609 2448 -662 
Q 2209 -716 1475 -716 
z
M 3500 -716 
L 3500 4797 
L 6025 4797 
L 6025 -678 
L 5188 -678 
L 5188 -166 
L 4338 -166 
L 4338 -716 
L 3500 -716 
z
M 5188 3978 
L 4338 3978 
L 4338 653 
L 5188 653 
L 5188 3978 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-5bc6" d="M 2488 5228 
L 3563 5359 
Q 3616 5159 3703 4797 
L 6100 4797 
L 6100 3384 
L 5256 3384 
L 5256 4078 
L 3272 4078 
Q 3463 3844 3713 3503 
L 3081 3103 
Q 2788 3559 2506 3909 
L 2778 4078 
L 1156 4078 
L 1156 3613 
L 1706 3197 
Q 1231 2603 744 2128 
L 200 2634 
Q 594 3028 903 3384 
L 313 3384 
L 313 4797 
L 2628 4797 
Q 2547 5059 2488 5228 
z
M 4369 3991 
L 5019 3547 
Q 4028 2322 2675 1744 
Q 2750 1656 2925 1659 
L 3813 1659 
Q 4066 1659 4130 1818 
Q 4194 1978 4250 2503 
Q 4725 2303 5031 2203 
Q 4938 1759 4852 1515 
Q 4766 1272 4653 1176 
Q 4541 1081 4348 1032 
Q 4156 984 3894 984 
L 3656 984 
L 3656 153 
L 4919 153 
L 4919 1097 
L 5769 1097 
L 5769 -828 
L 4919 -828 
L 4919 -572 
L 625 -572 
L 625 972 
L 1475 972 
L 1475 153 
L 2738 153 
L 2738 984 
L 2581 984 
Q 1997 984 1844 1444 
Q 1200 1244 456 1097 
Q 256 1572 113 1878 
Q 1041 2028 1788 2256 
L 1788 3591 
L 2619 3591 
L 2619 2563 
Q 3625 3019 4369 3991 
z
M 5256 3341 
Q 5713 2872 6300 2147 
L 5613 1653 
Q 5156 2322 4650 2909 
L 5256 3341 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-6d41" d="M 1775 4666 
L 3359 4666 
Q 3256 4906 3131 5172 
L 4063 5341 
Q 4228 5003 4375 4666 
L 6038 4666 
L 6038 3928 
L 3925 3928 
Q 3528 3463 3088 3016 
L 4728 3088 
Q 4572 3278 4419 3453 
L 5094 3884 
Q 5756 3153 6275 2528 
L 5550 2022 
L 5266 2403 
Q 2725 2313 2006 2216 
L 1744 2909 
Q 2163 3056 2825 3928 
L 1775 3928 
L 1775 4666 
z
M 700 5253 
Q 1188 4909 1725 4447 
L 1163 3809 
Q 619 4347 181 4697 
L 700 5253 
z
M 631 3591 
Q 1206 3178 1656 2784 
L 1069 2134 
Q 669 2528 88 3003 
L 631 3591 
z
M 5694 1253 
Q 6000 1128 6306 1022 
Q 6241 141 6189 -156 
Q 6138 -453 5945 -618 
Q 5753 -784 5419 -784 
L 5194 -784 
Q 4475 -784 4475 -3 
L 4475 2103 
L 5281 2103 
L 5281 103 
Q 5281 -84 5406 -84 
L 5450 -84 
Q 5578 -84 5614 100 
Q 5650 284 5694 1253 
z
M 3000 2084 
L 3000 1422 
Q 3000 -128 2000 -859 
Q 1738 -591 1375 -284 
Q 2194 322 2194 1597 
L 2194 2084 
L 3000 2084 
z
M 3319 2084 
L 4125 2084 
L 4125 -709 
L 3319 -709 
L 3319 2084 
z
M 813 1916 
Q 1219 1728 1631 1591 
L 969 -803 
Q 519 -634 119 -509 
Q 481 528 813 1916 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-91cf" d="M 725 5103 
L 5675 5103 
L 5675 3416 
L 725 3416 
L 725 5103 
z
M 4825 4666 
L 1575 4666 
L 1575 4447 
L 4825 4447 
L 4825 4666 
z
M 4825 3853 
L 4825 4072 
L 1575 4072 
L 1575 3853 
L 4825 3853 
z
M 138 3209 
L 6288 3209 
L 6288 2747 
L 138 2747 
L 138 3209 
z
M 113 -184 
L 2781 -184 
L 2781 53 
L 506 53 
L 506 491 
L 2781 491 
L 2781 716 
L 713 716 
L 713 2534 
L 5681 2534 
L 5681 716 
L 3606 716 
L 3606 491 
L 5925 491 
L 5925 53 
L 3606 53 
L 3606 -184 
L 6300 -184 
L 6300 -647 
L 113 -647 
L 113 -184 
z
M 4881 2097 
L 3606 2097 
L 3606 1816 
L 4881 1816 
L 4881 2097 
z
M 1513 1816 
L 2781 1816 
L 2781 2097 
L 1513 2097 
L 1513 1816 
z
M 4881 1153 
L 4881 1434 
L 3606 1434 
L 3606 1153 
L 4881 1153 
z
M 1513 1153 
L 2781 1153 
L 2781 1434 
L 1513 1434 
L 1513 1153 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-5f02" d="M 2019 2703 
L 4850 2703 
Q 5100 2703 5245 2789 
Q 5391 2875 5433 3031 
Q 5475 3188 5531 3678 
Q 5881 3553 6294 3434 
Q 6166 2763 6094 2545 
Q 6022 2328 5884 2203 
Q 5747 2078 5534 2015 
Q 5322 1953 5031 1953 
L 1819 1953 
Q 675 1953 675 3047 
L 675 5091 
L 5419 5091 
L 5419 3191 
L 1550 3191 
L 1550 3153 
Q 1550 2703 2019 2703 
z
M 4544 3878 
L 4544 4347 
L 1550 4347 
L 1550 3878 
L 4544 3878 
z
M 150 1341 
L 1688 1341 
Q 1700 1575 1694 1859 
L 2569 1859 
Q 2569 1581 2556 1341 
L 4056 1341 
L 4056 1859 
L 4906 1859 
L 4906 1341 
L 6250 1341 
L 6250 603 
L 4906 603 
L 4906 -728 
L 4056 -728 
L 4056 603 
L 2475 603 
Q 2344 -322 1219 -872 
Q 869 -553 494 -278 
Q 1406 109 1556 603 
L 150 603 
L 150 1341 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-5e38" d="M 2788 5303 
L 3613 5303 
L 3613 4403 
L 4341 4403 
L 4150 4566 
Q 4556 4934 4925 5316 
L 5519 4891 
Q 5275 4659 4988 4403 
L 6144 4403 
L 6144 3022 
L 5381 3022 
L 5381 3741 
L 1031 3741 
L 1031 3022 
L 269 3022 
L 269 4403 
L 1391 4403 
Q 1159 4659 925 4891 
L 1456 5316 
Q 1831 4984 2200 4603 
L 1950 4403 
L 2788 4403 
L 2788 5303 
z
M 1431 -484 
L 606 -484 
L 606 1509 
L 2781 1509 
L 2781 1859 
L 1250 1859 
L 1250 3397 
L 5163 3397 
L 5163 1859 
L 3631 1859 
L 3631 1509 
L 5863 1509 
L 5863 284 
Q 5863 53 5789 -109 
Q 5716 -272 5569 -367 
Q 5422 -463 5253 -483 
Q 5084 -503 4338 -503 
Q 4269 -191 4144 203 
Q 4466 184 4759 184 
Q 5038 184 5038 516 
L 5038 847 
L 3631 847 
L 3631 -853 
L 2781 -853 
L 2781 847 
L 1431 847 
L 1431 -484 
z
M 2013 2434 
L 4400 2434 
L 4400 2822 
L 2013 2822 
L 2013 2434 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-68c0" d="M 5463 2653 
L 2900 2653 
L 2900 2966 
Q 2688 2784 2463 2616 
Q 2238 3072 2031 3403 
L 1656 3403 
L 1656 2972 
Q 2100 2622 2519 2247 
L 2000 1616 
Q 1838 1828 1656 2028 
L 1656 -853 
L 863 -853 
L 863 1784 
Q 613 1172 275 597 
Q 175 1234 75 1741 
Q 538 2534 806 3403 
L 138 3403 
L 138 4216 
L 863 4216 
L 863 5291 
L 1656 5291 
L 1656 4216 
L 2300 4216 
L 2300 3603 
Q 3194 4291 3781 5303 
L 4781 5303 
L 4675 5128 
Q 5356 4028 6350 3484 
L 5894 2703 
Q 5669 2847 5463 3003 
L 5463 2653 
z
M 2200 122 
L 4550 122 
Q 4950 1266 5144 2316 
L 5975 2116 
Q 5756 1341 5344 122 
L 6175 122 
L 6175 -641 
L 2200 -641 
L 2200 122 
z
M 2956 2147 
Q 3313 1347 3588 503 
L 2850 259 
Q 2631 1047 2281 1884 
L 2956 2147 
z
M 4263 2441 
Q 4513 1584 4669 759 
L 3931 591 
Q 3844 1203 3581 2272 
L 4263 2441 
z
M 4194 4422 
Q 3813 3872 3363 3403 
L 5013 3403 
Q 4581 3834 4194 4422 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-6d4b" d="M 1706 1084 
L 1706 4475 
L 1144 3866 
Q 738 4272 238 4709 
L 750 5284 
Q 1191 4966 1706 4513 
L 1706 5016 
L 4113 5016 
L 4113 1116 
L 3463 1116 
L 3463 4328 
L 2356 4328 
L 2356 1084 
L 1706 1084 
z
M 5344 5234 
L 6069 5234 
L 6069 59 
Q 6069 -272 5926 -465 
Q 5784 -659 5550 -707 
Q 5316 -756 4600 -747 
Q 4550 -328 4406 128 
Q 4750 91 5063 91 
Q 5344 91 5344 428 
L 5344 5234 
z
M 4406 4772 
L 5044 4772 
L 5044 591 
L 4406 591 
L 4406 4772 
z
M 2619 3903 
L 3194 3903 
L 3194 2297 
Q 3194 1259 2953 609 
L 3369 934 
Q 3900 384 4350 -134 
L 3731 -622 
Q 3338 -56 2900 478 
Q 2613 -209 1900 -841 
Q 1613 -428 1369 -166 
Q 2081 331 2350 920 
Q 2619 1509 2619 2428 
L 2619 3903 
z
M 600 3622 
Q 1088 3266 1588 2834 
L 1025 2159 
Q 656 2516 88 3028 
L 600 3622 
z
M 813 1834 
Q 1256 1647 1594 1541 
L 1000 -822 
Q 556 -659 200 -566 
Q 525 466 813 1834 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-Bold-52a0"/>
     <use xlink:href="#MicrosoftYaHei-Bold-5bc6" x="100"/>
     <use xlink:href="#MicrosoftYaHei-Bold-6d41" x="200"/>
     <use xlink:href="#MicrosoftYaHei-Bold-91cf" x="300"/>
     <use xlink:href="#MicrosoftYaHei-Bold-5f02" x="400"/>
     <use xlink:href="#MicrosoftYaHei-Bold-5e38" x="500"/>
     <use xlink:href="#MicrosoftYaHei-Bold-68c0" x="600"/>
     <use xlink:href="#MicrosoftYaHei-Bold-6d4b" x="700"/>
    </g>
    <!-- 未来研究方向 -->
    <g style="fill: #ffffff" transform="translate(524.4 461.111007) scale(0.16 -0.16)">
     <defs>
      <path id="MicrosoftYaHei-Bold-672a" d="M 219 2878 
L 2744 2878 
L 2744 3609 
L 563 3609 
L 563 4422 
L 2744 4422 
L 2744 5316 
L 3619 5316 
L 3619 4422 
L 5863 4422 
L 5863 3609 
L 3619 3609 
L 3619 2878 
L 6181 2878 
L 6181 2066 
L 3919 2066 
Q 4866 938 6306 384 
Q 5994 -3 5675 -441 
Q 4463 253 3619 1391 
L 3619 -809 
L 2744 -809 
L 2744 1334 
Q 1966 322 700 -491 
Q 406 -72 94 284 
Q 1559 1006 2456 2066 
L 219 2066 
L 219 2878 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-6765" d="M 513 4628 
L 2731 4628 
L 2731 5316 
L 3638 5316 
L 3638 4628 
L 5888 4628 
L 5888 3816 
L 4872 3816 
L 5494 3353 
Q 5078 2844 4722 2516 
L 6213 2516 
L 6213 1709 
L 4159 1709 
Q 5028 816 6269 347 
Q 5988 -22 5713 -441 
Q 4388 281 3638 1263 
L 3638 -809 
L 2731 -809 
L 2731 1256 
Q 1959 253 656 -478 
Q 369 -66 106 266 
Q 1325 794 2175 1709 
L 213 1709 
L 213 2516 
L 1609 2516 
Q 1363 2856 906 3378 
L 1488 3816 
L 513 3816 
L 513 4628 
z
M 4847 3816 
L 3638 3816 
L 3638 2516 
L 4638 2516 
L 4081 2984 
Q 4469 3347 4847 3816 
z
M 2294 2966 
L 1688 2516 
L 2731 2516 
L 2731 3816 
L 1531 3816 
Q 1897 3441 2294 2966 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-7814" d="M 2494 2591 
L 3163 2591 
L 3163 4216 
L 2650 4216 
L 2650 4966 
L 6138 4966 
L 6138 4216 
L 5575 4216 
L 5575 2591 
L 6306 2591 
L 6306 1841 
L 5575 1841 
L 5575 -759 
L 4763 -759 
L 4763 1841 
L 3963 1841 
Q 3859 6 2956 -866 
Q 2588 -578 2175 -309 
Q 3013 400 3138 1841 
L 2494 1841 
L 2494 2591 
z
M 3975 2591 
L 4763 2591 
L 4763 4216 
L 3975 4216 
L 3975 2591 
z
M 2400 4191 
L 1659 4191 
Q 1531 3669 1356 3184 
L 2344 3184 
L 2344 -84 
L 1281 -84 
L 1281 -459 
L 588 -459 
L 588 1603 
Q 478 1431 363 1266 
Q 244 1797 38 2228 
Q 619 3081 866 4191 
L 250 4191 
L 250 4941 
L 2400 4941 
L 2400 4191 
z
M 1650 603 
L 1650 2497 
L 1281 2497 
L 1281 603 
L 1650 603 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-7a76" d="M 3506 5334 
Q 3625 5019 3719 4728 
L 6075 4728 
L 6075 3391 
L 5275 3391 
L 5275 3978 
L 1125 3978 
L 1125 3391 
L 325 3391 
L 325 4728 
L 2741 4728 
Q 2656 4988 2569 5234 
L 3506 5334 
z
M 3956 3959 
Q 5038 3409 5988 2853 
L 5506 2122 
L 3463 3334 
L 3956 3959 
z
M 2331 3953 
L 2800 3297 
Q 1794 2734 750 2253 
Q 538 2628 300 2984 
Q 1431 3416 2331 3953 
z
M 4775 2191 
L 4775 303 
Q 4775 22 5031 22 
L 5169 22 
Q 5391 22 5445 189 
Q 5500 356 5588 1234 
L 6338 997 
Q 6200 -284 5934 -478 
Q 5669 -672 5356 -672 
L 4719 -672 
Q 3925 -672 3925 97 
L 3925 1466 
L 2988 1466 
Q 2769 -206 825 -859 
Q 619 -522 319 -59 
Q 1881 394 2122 1466 
L 588 1466 
L 588 2191 
L 2194 2191 
L 2250 2922 
L 3100 2922 
Q 3088 2591 3056 2191 
L 4775 2191 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-65b9" d="M 2975 -747 
Q 2863 -184 2750 209 
Q 3528 178 3822 178 
Q 4031 178 4142 214 
Q 4253 250 4325 329 
Q 4397 409 4422 509 
Q 4447 609 4476 1196 
Q 4506 1784 4506 2003 
L 2531 2003 
Q 2341 213 769 -878 
Q 613 -653 94 -53 
Q 784 397 1142 962 
Q 1500 1528 1562 2073 
Q 1625 2619 1641 3628 
L 231 3628 
L 231 4453 
L 2828 4453 
L 2581 5084 
L 3575 5316 
Q 3781 4800 3888 4453 
L 6175 4453 
L 6175 3628 
L 2594 3628 
Q 2588 3241 2572 2828 
L 5494 2828 
Q 5466 2006 5419 1132 
Q 5372 259 5330 46 
Q 5288 -166 5152 -352 
Q 5016 -538 4825 -617 
Q 4634 -697 4232 -722 
Q 3831 -747 2975 -747 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-5411" d="M 400 4466 
L 2253 4466 
Q 2388 4928 2475 5347 
L 3438 5203 
L 3197 4466 
L 6006 4466 
L 6006 409 
Q 6006 -166 5743 -464 
Q 5481 -763 4928 -780 
Q 4375 -797 3625 -797 
Q 3538 -366 3394 116 
Q 3856 84 4209 84 
Q 4622 84 4781 131 
Q 4941 178 5033 315 
Q 5125 453 5125 672 
L 5125 3603 
L 1288 3603 
L 1288 -766 
L 400 -766 
L 400 4466 
z
M 4613 3091 
L 4613 666 
L 2650 666 
L 2650 291 
L 1800 291 
L 1800 3091 
L 4613 3091 
z
M 2650 2291 
L 2650 1466 
L 3763 1466 
L 3763 2291 
L 2650 2291 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-Bold-672a"/>
     <use xlink:href="#MicrosoftYaHei-Bold-6765" x="100"/>
     <use xlink:href="#MicrosoftYaHei-Bold-7814" x="200"/>
     <use xlink:href="#MicrosoftYaHei-Bold-7a76" x="300"/>
     <use xlink:href="#MicrosoftYaHei-Bold-65b9" x="400"/>
     <use xlink:href="#MicrosoftYaHei-Bold-5411" x="500"/>
    </g>
   </g>
   <g id="text_2">
    <!-- 模型优化与改进方向 -->
    <g style="fill: #ffffff" transform="translate(509.4 182.526758) scale(0.14 -0.14)">
     <defs>
      <path id="MicrosoftYaHei-Bold-6a21" d="M 6250 534 
L 4825 534 
Q 5419 84 6325 -53 
Q 6131 -403 5913 -859 
Q 4769 -522 4169 334 
Q 3694 -503 2406 -897 
Q 2206 -509 1925 -159 
Q 3013 66 3419 534 
L 2088 534 
L 2088 1141 
L 3719 1141 
Q 3756 1284 3769 1459 
L 2425 1459 
L 2425 2134 
L 1944 1659 
Q 1800 1878 1638 2109 
L 1638 -841 
L 863 -841 
L 863 1803 
Q 619 1141 275 547 
Q 213 978 75 1691 
Q 525 2453 788 3328 
L 138 3328 
L 138 4066 
L 863 4066 
L 863 5291 
L 1638 5291 
L 1638 4066 
L 2219 4066 
L 2219 3328 
L 1638 3328 
L 1638 2972 
Q 2081 2559 2425 2209 
L 2425 3703 
L 6025 3703 
L 6025 1459 
L 4556 1459 
Q 4544 1291 4519 1141 
L 6250 1141 
L 6250 534 
z
M 5250 2009 
L 5250 2353 
L 3200 2353 
L 3200 2009 
L 5250 2009 
z
M 5250 3147 
L 3200 3147 
L 3200 2816 
L 5250 2816 
L 5250 3147 
z
M 5413 3853 
L 4675 3853 
L 4675 4203 
L 3719 4203 
L 3719 3853 
L 2981 3853 
L 2981 4203 
L 2175 4203 
L 2175 4822 
L 2981 4822 
L 2981 5291 
L 3719 5291 
L 3719 4822 
L 4675 4822 
L 4675 5291 
L 5413 5291 
L 5413 4822 
L 6275 4822 
L 6275 4203 
L 5413 4203 
L 5413 3853 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-578b" d="M 5963 5216 
L 5963 2547 
Q 5963 2141 5752 1928 
Q 5541 1716 5095 1700 
Q 4650 1684 4269 1684 
Q 4231 2059 4150 2447 
Q 4469 2428 4813 2428 
Q 5150 2428 5150 2709 
L 5150 5216 
L 5963 5216 
z
M 150 3491 
L 891 3491 
Q 900 3616 900 4341 
L 325 4341 
L 325 5066 
L 3625 5066 
L 3625 4341 
L 3113 4341 
L 3113 3491 
L 3750 3491 
L 3750 2766 
L 3113 2766 
L 3113 2091 
L 3656 2091 
L 3656 1441 
L 5781 1441 
L 5781 678 
L 3656 678 
L 3656 103 
L 6294 103 
L 6294 -647 
L 138 -647 
L 138 103 
L 2763 103 
L 2763 678 
L 650 678 
L 650 1441 
L 2763 1441 
L 2763 1766 
L 2350 1766 
L 2350 2766 
L 1559 2766 
Q 1375 2016 888 1522 
Q 675 1697 275 2009 
Q 584 2334 741 2766 
L 150 2766 
L 150 3491 
z
M 1663 3491 
L 2350 3491 
L 2350 4341 
L 1669 4341 
Q 1669 3753 1663 3491 
z
M 3900 4866 
L 4650 4866 
L 4650 2753 
L 3900 2753 
L 3900 4866 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-4f18" d="M 1406 5334 
L 2269 5084 
Q 1981 4238 1600 3491 
L 1600 0 
Q 2728 944 2850 2897 
L 1825 2897 
L 1825 3716 
L 2866 3716 
Q 2881 4513 2881 5303 
L 3719 5303 
Q 3719 5091 3700 3716 
L 6188 3716 
L 6188 2897 
L 4806 2897 
L 4806 472 
Q 4806 178 5031 178 
L 5150 178 
Q 5328 178 5372 301 
Q 5416 425 5506 1378 
Q 5881 1203 6288 1053 
Q 6191 291 6119 -23 
Q 6047 -338 5830 -470 
Q 5613 -603 5288 -603 
L 4756 -603 
Q 3969 -603 3969 266 
L 3969 2897 
L 3706 2897 
Q 3634 475 2194 -791 
Q 1875 -359 1600 -50 
L 1600 -797 
L 763 -797 
L 763 2131 
Q 547 1838 313 1566 
Q 219 2103 81 2697 
Q 844 3634 1406 5334 
z
M 4713 5278 
Q 5319 4922 5831 4503 
L 5300 3878 
Q 5019 4116 4231 4722 
L 4713 5278 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-5316" d="M 1625 5297 
L 2494 5009 
Q 2200 4284 1856 3641 
L 1856 -797 
L 956 -797 
L 956 2191 
Q 653 1775 325 1403 
Q 219 1991 88 2497 
Q 1063 3728 1625 5297 
z
M 5456 4447 
L 6238 3847 
Q 5306 2809 4106 1900 
L 4106 622 
Q 4106 153 4513 153 
L 4781 153 
Q 5091 153 5189 359 
Q 5288 566 5350 1597 
Q 5863 1434 6300 1322 
Q 6188 419 6105 37 
Q 6022 -344 5705 -542 
Q 5388 -741 4831 -741 
L 4294 -741 
Q 3169 -741 3169 378 
L 3169 1234 
Q 2806 994 2425 766 
Q 2225 1097 1875 1584 
Q 2553 1947 3169 2378 
L 3169 5272 
L 4106 5272 
L 4106 3100 
Q 4834 3719 5456 4447 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-4e0e" d="M 6138 3747 
L 2000 3747 
L 1956 3041 
L 5838 3041 
L 5738 541 
Q 5709 -169 5351 -453 
Q 4994 -738 4381 -741 
L 3031 -741 
Q 2956 -359 2813 166 
Q 3463 134 4081 134 
Q 4731 134 4784 526 
Q 4838 919 4863 2234 
L 969 2234 
L 1144 5272 
L 2094 5272 
L 2050 4566 
L 6138 4566 
L 6138 3747 
z
M 4456 766 
L 181 766 
L 181 1584 
L 4456 1584 
L 4456 766 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-6539" d="M 3475 5347 
L 4294 5184 
Q 4206 4753 4106 4366 
L 6250 4366 
L 6250 3534 
L 5822 3534 
Q 5650 1950 4966 941 
Q 5528 356 6325 -34 
L 5775 -841 
Q 4969 -359 4400 259 
Q 3772 -378 2931 -847 
Q 2769 -559 2419 -41 
Q 3259 375 3853 969 
Q 3472 1563 3266 2259 
Q 3166 2103 3063 1966 
Q 2834 2322 2650 2559 
L 2650 2078 
L 1225 2078 
L 1225 491 
Q 1781 791 2550 1172 
Q 2569 897 2656 322 
Q 1069 -472 700 -822 
L 238 -209 
Q 406 -3 400 316 
L 400 2878 
L 1825 2878 
L 1825 4091 
L 250 4091 
L 250 4891 
L 2650 4891 
L 2650 2897 
Q 3234 3900 3475 5347 
z
M 4403 1666 
Q 4819 2391 5000 3534 
L 3856 3534 
Q 3813 3409 3766 3291 
Q 3959 2375 4403 1666 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-8fdb" d="M 1806 2516 
L 2700 2516 
Q 2719 2728 2719 3591 
L 2044 3591 
L 2044 4341 
L 2719 4341 
L 2719 5291 
L 3556 5291 
L 3556 4341 
L 4494 4341 
L 4494 5291 
L 5331 5291 
L 5331 4341 
L 6000 4341 
L 6000 3591 
L 5331 3591 
L 5331 2516 
L 6244 2516 
L 6244 1766 
L 5331 1766 
L 5331 291 
L 4494 291 
L 4494 1766 
L 3463 1766 
Q 3309 844 2900 178 
Q 2519 509 2088 803 
Q 2413 1200 2572 1766 
L 1806 1766 
L 1806 2516 
z
M 3544 2516 
L 4494 2516 
L 4494 3591 
L 3556 3591 
Q 3556 2959 3544 2516 
z
M 988 5253 
Q 1456 4641 1831 4103 
L 1100 3609 
Q 719 4259 319 4841 
L 988 5253 
z
M 6319 141 
Q 6144 -309 6050 -691 
Q 5634 -703 4181 -703 
L 3075 -703 
Q 2434 -703 1956 -553 
Q 1478 -403 1175 -103 
L 450 -847 
L 94 109 
Q 425 428 738 694 
L 738 2309 
L 138 2309 
L 138 3103 
L 1556 3103 
L 1556 613 
Q 2047 97 3019 97 
Q 5722 97 6319 141 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-Bold-6a21"/>
     <use xlink:href="#MicrosoftYaHei-Bold-578b" x="100"/>
     <use xlink:href="#MicrosoftYaHei-Bold-4f18" x="200"/>
     <use xlink:href="#MicrosoftYaHei-Bold-5316" x="300"/>
     <use xlink:href="#MicrosoftYaHei-Bold-4e0e" x="400"/>
     <use xlink:href="#MicrosoftYaHei-Bold-6539" x="500"/>
     <use xlink:href="#MicrosoftYaHei-Bold-8fdb" x="600"/>
     <use xlink:href="#MicrosoftYaHei-Bold-65b9" x="700"/>
     <use xlink:href="#MicrosoftYaHei-Bold-5411" x="800"/>
    </g>
   </g>
   <g id="text_3">
    <!-- 轻量级模型设计 -->
    <g transform="translate(618.7125 36.716065) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-8f7b" d="M 694 2441 
L 1350 2441 
L 1350 3616 
L 1788 3616 
L 1788 2441 
L 2413 2441 
L 2413 2028 
L 1788 2028 
L 1788 1134 
Q 2159 1178 2556 1228 
Q 2525 984 2513 791 
Q 2147 750 1788 709 
L 1788 -847 
L 1350 -847 
L 1350 656 
Q 744 581 150 503 
L 100 966 
Q 681 1016 1350 1088 
L 1350 2028 
L 238 2028 
L 238 2441 
Q 494 3181 728 4022 
L 150 4022 
L 150 4434 
L 844 4434 
Q 969 4900 1088 5397 
L 1550 5303 
Q 1425 4856 1303 4434 
L 2494 4434 
L 2494 4022 
L 1184 4022 
Q 934 3175 694 2441 
z
M 2719 4897 
L 5619 4897 
L 5619 4472 
Q 5266 3906 4763 3425 
Q 5591 2975 6344 2516 
L 6050 2103 
Q 5191 2678 4422 3125 
Q 3713 2550 2756 2116 
Q 2606 2341 2438 2534 
Q 4175 3241 5069 4459 
L 2719 4459 
L 2719 4897 
z
M 2331 -128 
L 4069 -128 
L 4069 1441 
L 2788 1441 
L 2788 1878 
L 5788 1878 
L 5788 1441 
L 4531 1441 
L 4531 -128 
L 6256 -128 
L 6256 -566 
L 2331 -566 
L 2331 -128 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-91cf" d="M 113 -278 
L 2981 -278 
L 2981 78 
L 475 78 
L 475 403 
L 2981 403 
L 2981 759 
L 1494 759 
L 1494 597 
L 1069 597 
L 1069 2516 
L 5350 2516 
L 5350 597 
L 4925 597 
L 4925 759 
L 3419 759 
L 3419 403 
L 5931 403 
L 5931 78 
L 3419 78 
L 3419 -278 
L 6288 -278 
L 6288 -616 
L 113 -616 
L 113 -278 
z
M 1119 5072 
L 5306 5072 
L 5306 3284 
L 4881 3284 
L 4881 3453 
L 1544 3453 
L 1544 3284 
L 1119 3284 
L 1119 5072 
z
M 163 3153 
L 6263 3153 
L 6263 2816 
L 163 2816 
L 163 3153 
z
M 1544 3778 
L 4881 3778 
L 4881 4116 
L 1544 4116 
L 1544 3778 
z
M 4881 4747 
L 1544 4747 
L 1544 4416 
L 4881 4416 
L 4881 4747 
z
M 3419 1084 
L 4925 1084 
L 4925 1491 
L 3419 1491 
L 3419 1084 
z
M 1494 1084 
L 2981 1084 
L 2981 1491 
L 1494 1491 
L 1494 1084 
z
M 4925 2191 
L 3419 2191 
L 3419 1791 
L 4925 1791 
L 4925 2191 
z
M 1494 1791 
L 2981 1791 
L 2981 2191 
L 1494 2191 
L 1494 1791 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-7ea7" d="M 3272 2522 
Q 3175 466 1994 -803 
Q 1769 -578 1594 -453 
Q 2394 391 2631 1428 
Q 2859 2322 2844 4497 
L 2288 4497 
L 2288 4941 
L 5406 4941 
L 5406 4472 
L 4800 3216 
L 5838 3216 
L 5838 2753 
Q 5475 1622 4853 775 
Q 5456 163 6331 -291 
Q 6144 -472 5913 -753 
Q 5088 -231 4522 359 
Q 3881 -366 3013 -847 
Q 2838 -616 2663 -447 
Q 3563 63 4188 756 
Q 3625 1500 3272 2522 
z
M 4281 3228 
L 4881 4497 
L 3319 4497 
Q 3322 4050 3316 3653 
Q 3794 2034 4513 1153 
Q 5044 1878 5319 2772 
L 4281 2772 
L 4281 3228 
z
M 938 1897 
Q 1588 1966 2263 2053 
Q 2238 1847 2213 1647 
Q 1194 1522 413 1391 
L 300 1834 
Q 672 2163 1234 3028 
Q 753 2994 244 2941 
L 125 3334 
Q 644 3909 1331 5334 
L 1806 5128 
Q 1163 4041 681 3366 
Q 1031 3378 1469 3397 
Q 1694 3766 1950 4209 
L 2400 3997 
Q 1456 2566 938 1897 
z
M 100 441 
Q 1100 678 2131 959 
Q 2138 741 2150 528 
Q 1094 241 275 -3 
L 100 441 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-6a21" d="M 2050 984 
L 3859 984 
Q 3900 1213 3913 1547 
L 2950 1547 
L 2950 1372 
L 2500 1372 
L 2500 3634 
L 5844 3634 
L 5844 1372 
L 5394 1372 
L 5394 1547 
L 4344 1547 
Q 4325 1203 4288 984 
L 6250 984 
L 6250 584 
L 4525 584 
Q 5203 -175 6319 -341 
Q 6144 -572 6013 -809 
Q 4803 -456 4128 466 
Q 3681 -431 2075 -847 
Q 1969 -628 1831 -416 
Q 3303 -106 3713 584 
L 2050 584 
L 2050 984 
z
M 63 1409 
Q 603 2316 922 3509 
L 138 3509 
L 138 3922 
L 1006 3922 
L 1006 5309 
L 1444 5309 
L 1444 3922 
L 2256 3922 
L 2256 3509 
L 1444 3509 
L 1444 2684 
L 1688 2884 
Q 2044 2503 2350 2116 
L 2006 1828 
Q 1719 2244 1444 2563 
L 1444 -822 
L 1006 -822 
L 1006 2581 
Q 672 1438 275 859 
Q 188 1128 63 1409 
z
M 4713 5303 
L 5150 5303 
L 5150 4703 
L 6225 4703 
L 6225 4303 
L 5150 4303 
L 5150 3797 
L 4713 3797 
L 4713 4303 
L 3600 4303 
L 3600 3797 
L 3163 3797 
L 3163 4303 
L 2175 4303 
L 2175 4703 
L 3163 4703 
L 3163 5309 
L 3600 5309 
L 3600 4703 
L 4713 4703 
L 4713 5303 
z
M 5394 3247 
L 2950 3247 
L 2950 2778 
L 5394 2778 
L 5394 3247 
z
M 2950 1934 
L 5394 1934 
L 5394 2403 
L 2950 2403 
L 2950 1934 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-578b" d="M 150 3503 
L 1100 3503 
Q 1106 3638 1106 4547 
L 413 4547 
L 413 4959 
L 3675 4959 
L 3675 4547 
L 2994 4547 
L 2994 3503 
L 3881 3503 
L 3881 3091 
L 2994 3091 
L 2994 1747 
L 2538 1747 
L 2538 3091 
L 1528 3091 
Q 1450 2191 638 1534 
Q 475 1716 294 1897 
Q 1013 2453 1066 3091 
L 150 3091 
L 150 3503 
z
M 138 -103 
L 2938 -103 
L 2938 741 
L 706 741 
L 706 1172 
L 2938 1172 
L 2938 1659 
L 3425 1659 
L 3425 1172 
L 5713 1172 
L 5713 741 
L 3425 741 
L 3425 -103 
L 6256 -103 
L 6256 -534 
L 138 -534 
L 138 -103 
z
M 5856 5209 
L 5856 2334 
Q 5856 1672 5200 1659 
Q 4825 1647 4331 1659 
Q 4306 1897 4256 2153 
Q 4781 2122 5044 2122 
Q 5400 2122 5400 2516 
L 5400 5209 
L 5856 5209 
z
M 1556 3503 
L 2538 3503 
L 2538 4547 
L 1563 4547 
Q 1563 3791 1556 3503 
z
M 4138 4859 
L 4588 4859 
L 4588 2697 
L 4138 2697 
L 4138 4859 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-8bbe" d="M 2231 2116 
L 2231 2547 
L 5638 2547 
L 5638 2116 
Q 5144 1031 4419 416 
Q 5216 -109 6338 -316 
Q 6119 -597 5956 -822 
Q 4841 -500 4013 113 
Q 3163 -463 1863 -872 
Q 1700 -641 1538 -422 
Q 2778 -88 3613 431 
Q 2888 1131 2559 2116 
L 2231 2116 
z
M 4856 4553 
L 3294 4553 
L 3294 4372 
Q 3294 3222 2244 2659 
Q 2075 2866 1881 3047 
Q 2806 3522 2806 4359 
L 2806 5016 
L 5344 5016 
L 5344 3741 
Q 5344 3459 5631 3459 
L 6319 3459 
Q 6288 3203 6269 2997 
L 5444 2997 
Q 4856 2997 4856 3647 
L 4856 4553 
z
M 2250 1028 
Q 2263 734 2294 472 
Q 1438 -84 1019 -422 
L 719 -9 
Q 931 197 931 553 
L 931 2778 
L 138 2778 
L 138 3228 
L 1406 3228 
L 1406 453 
Q 1813 722 2250 1028 
z
M 988 5128 
Q 1469 4653 1906 4166 
L 1469 3803 
Q 1013 4391 613 4803 
L 988 5128 
z
M 3031 2116 
Q 3347 1319 4022 713 
Q 4684 1225 5081 2116 
L 3031 2116 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-8ba1" d="M 2100 3153 
L 3919 3153 
L 3919 5303 
L 4456 5303 
L 4456 3153 
L 6288 3153 
L 6288 2703 
L 4456 2703 
L 4456 -822 
L 3919 -822 
L 3919 2703 
L 2100 2703 
L 2100 3153 
z
M 2675 903 
Q 2694 578 2719 359 
Q 1719 -266 1069 -728 
L 769 -303 
Q 988 -116 988 266 
L 988 2741 
L 100 2741 
L 100 3191 
L 1488 3191 
L 1488 153 
Q 2031 484 2675 903 
z
M 738 5222 
Q 1419 4522 1725 4184 
L 1288 3816 
Q 894 4334 356 4891 
L 738 5222 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-8f7b"/>
     <use xlink:href="#MicrosoftYaHei-91cf" x="100"/>
     <use xlink:href="#MicrosoftYaHei-7ea7" x="200"/>
     <use xlink:href="#MicrosoftYaHei-6a21" x="300"/>
     <use xlink:href="#MicrosoftYaHei-578b" x="400"/>
     <use xlink:href="#MicrosoftYaHei-8bbe" x="500"/>
     <use xlink:href="#MicrosoftYaHei-8ba1" x="600"/>
    </g>
   </g>
   <g id="text_4">
    <!-- 增强对抗鲁棒性 -->
    <g transform="translate(561.070609 16.7925) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-589e" d="M 2188 4459 
L 3259 4459 
Q 3041 4781 2775 5116 
L 3131 5366 
Q 3456 4991 3694 4666 
L 3403 4459 
L 4453 4459 
Q 4759 4888 5050 5372 
L 5500 5116 
Q 5234 4772 4978 4459 
L 6038 4459 
L 6038 2122 
L 5600 2122 
L 5600 2309 
L 2625 2309 
L 2625 2122 
L 2188 2122 
L 2188 4459 
z
M 2438 1797 
L 5788 1797 
L 5788 -847 
L 5338 -847 
L 5338 -547 
L 2888 -547 
L 2888 -847 
L 2438 -847 
L 2438 1797 
z
M 81 428 
Q 466 538 894 672 
L 894 3253 
L 144 3253 
L 144 3697 
L 894 3697 
L 894 5259 
L 1356 5259 
L 1356 3697 
L 2000 3697 
L 2000 3253 
L 1356 3253 
L 1356 819 
Q 1709 931 2094 1059 
Q 2106 759 2125 553 
Q 1306 316 250 -53 
L 81 428 
z
M 5600 4084 
L 4325 4084 
L 4325 2684 
L 5600 2684 
L 5600 4084 
z
M 2625 2684 
L 3894 2684 
L 3894 4084 
L 2625 4084 
L 2625 2684 
z
M 5338 1409 
L 2888 1409 
L 2888 809 
L 5338 809 
L 5338 1409 
z
M 2888 -159 
L 5338 -159 
L 5338 434 
L 2888 434 
L 2888 -159 
z
M 5350 3609 
Q 5006 3066 4856 2884 
L 4519 3084 
Q 4744 3391 4981 3828 
L 5350 3609 
z
M 2863 3597 
L 3181 3803 
Q 3463 3441 3669 3122 
L 3319 2897 
Q 3119 3228 2863 3597 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-5f3a" d="M 5250 466 
L 5669 666 
Q 6031 28 6363 -641 
L 5894 -878 
Q 5794 -641 5688 -416 
Q 3750 -547 2219 -653 
L 2150 -134 
Q 3088 -97 3969 -59 
L 3969 866 
L 2894 866 
L 2894 622 
L 2431 622 
L 2431 2616 
L 3969 2616 
L 3969 3297 
L 3144 3297 
L 3144 3047 
L 2681 3047 
L 2681 4972 
L 5731 4972 
L 5731 3047 
L 5269 3047 
L 5269 3297 
L 4444 3297 
L 4444 2616 
L 5988 2616 
L 5988 866 
L 4444 866 
L 4444 -41 
Q 4975 -22 5488 3 
Q 5369 241 5250 466 
z
M 425 3466 
L 1650 3466 
L 1650 4516 
L 238 4516 
L 238 4941 
L 2113 4941 
L 2113 2772 
L 1650 2772 
L 1650 3041 
L 831 3041 
L 719 2072 
L 2056 2072 
Q 1994 741 1944 128 
Q 1888 -722 1100 -722 
Q 819 -722 425 -716 
Q 388 -484 325 -216 
Q 731 -247 1019 -247 
Q 1444 -247 1475 191 
Q 1525 828 1563 1647 
L 225 1647 
L 425 3466 
z
M 5269 4547 
L 3144 4547 
L 3144 3722 
L 5269 3722 
L 5269 4547 
z
M 5525 1291 
L 5525 2191 
L 4444 2191 
L 4444 1291 
L 5525 1291 
z
M 2894 1291 
L 3969 1291 
L 3969 2191 
L 2894 2191 
L 2894 1291 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-5bf9" d="M 694 3691 
Q 1234 2909 1741 2166 
Q 2113 3081 2213 4147 
L 250 4147 
L 250 4622 
L 2725 4622 
L 2725 4166 
Q 2553 2809 2059 1700 
Q 2569 953 3044 247 
L 2594 -53 
Q 2203 563 1803 1181 
Q 1247 163 388 -622 
Q 263 -428 81 -166 
Q 975 638 1500 1647 
Q 919 2538 319 3434 
L 694 3691 
z
M 2975 3941 
L 5056 3941 
L 5056 5278 
L 5563 5278 
L 5563 3941 
L 6325 3941 
L 6325 3466 
L 5563 3466 
L 5563 191 
Q 5563 -716 4669 -716 
Q 4388 -716 3744 -709 
Q 3713 -478 3644 -147 
Q 4263 -197 4588 -197 
Q 5056 -197 5056 278 
L 5056 3466 
L 2975 3466 
L 2975 3941 
z
M 3088 2684 
L 3494 2928 
Q 4000 2184 4388 1484 
L 3919 1216 
Q 3550 1922 3088 2684 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-6297" d="M 3238 2634 
L 3238 1728 
Q 3238 28 2019 -866 
Q 1931 -747 1650 -497 
Q 2750 266 2750 1716 
L 2750 3084 
L 5106 3084 
L 5106 -53 
Q 5106 -266 5325 -266 
L 5538 -266 
Q 5788 -266 5825 128 
Q 5850 372 5881 922 
Q 6131 809 6350 728 
Q 6313 272 6269 -41 
Q 6188 -716 5594 -716 
L 5163 -716 
Q 4619 -716 4619 -141 
L 4619 2634 
L 3238 2634 
z
M 175 4041 
L 981 4041 
L 981 5266 
L 1463 5266 
L 1463 4041 
L 2150 4041 
L 2150 3609 
L 1463 3609 
L 1463 2403 
Q 1838 2519 2219 2641 
Q 2219 2347 2231 2153 
Q 1841 2031 1463 1909 
L 1463 -41 
Q 1463 -741 831 -747 
Q 619 -753 238 -747 
Q 200 -484 156 -216 
Q 506 -253 713 -253 
Q 981 -253 981 34 
L 981 1753 
Q 563 1616 156 1478 
L 94 1997 
Q 534 2122 981 2256 
L 981 3609 
L 175 3609 
L 175 4041 
z
M 2275 4359 
L 3925 4359 
Q 3694 4841 3513 5147 
L 3950 5359 
Q 4219 4909 4394 4553 
L 3988 4359 
L 6025 4359 
L 6025 3909 
L 2275 3909 
L 2275 4359 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-9c81" d="M 119 3997 
Q 1119 4547 1700 5334 
L 2238 5334 
Q 2094 5150 1944 4978 
L 4331 4978 
L 4331 4641 
L 3919 4184 
L 5563 4184 
L 5563 2266 
L 5138 2266 
L 5138 2397 
L 1263 2397 
L 1263 2266 
L 838 2266 
L 838 3941 
Q 619 3775 394 3634 
Q 269 3828 119 3997 
z
M 950 1284 
L 5450 1284 
L 5450 -834 
L 5013 -834 
L 5013 -578 
L 1388 -578 
L 1388 -834 
L 950 -834 
L 950 1284 
z
M 175 2009 
L 6225 2009 
L 6225 1659 
L 175 1659 
L 175 2009 
z
M 5013 941 
L 1388 941 
L 1388 516 
L 5013 516 
L 5013 941 
z
M 1388 -234 
L 5013 -234 
L 5013 191 
L 1388 191 
L 1388 -234 
z
M 1141 4184 
L 3356 4184 
L 3744 4628 
L 1619 4628 
Q 1388 4391 1141 4184 
z
M 5138 3853 
L 3413 3853 
L 3413 3447 
L 5138 3447 
L 5138 3853 
z
M 1263 2728 
L 2988 2728 
L 2988 3134 
L 1263 3134 
L 1263 2728 
z
M 1263 3447 
L 2988 3447 
L 2988 3853 
L 1263 3853 
L 1263 3447 
z
M 3413 2728 
L 5138 2728 
L 5138 3134 
L 3413 3134 
L 3413 2728 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-68d2" d="M 2244 547 
L 3856 547 
L 3856 1159 
L 2788 1159 
L 2788 1519 
Q 2481 1231 2100 972 
Q 2000 1103 1775 1334 
Q 2563 1831 3006 2466 
L 2069 2466 
L 2069 2866 
L 3247 2866 
Q 3372 3113 3450 3378 
L 2431 3378 
L 2431 3778 
L 3550 3778 
Q 3600 4019 3634 4284 
L 2250 4284 
L 2250 4684 
L 3678 4684 
Q 3703 4981 3713 5309 
L 4175 5309 
Q 4163 4981 4138 4684 
L 6044 4684 
L 6044 4284 
L 4094 4284 
Q 4063 4016 4013 3778 
L 5875 3778 
L 5875 3378 
L 3925 3378 
Q 3859 3116 3753 2866 
L 6294 2866 
L 6294 2466 
L 5006 2466 
Q 5484 1775 6344 1459 
Q 6181 1247 6050 1034 
Q 5663 1241 5350 1503 
L 5350 1159 
L 4313 1159 
L 4313 547 
L 5944 547 
L 5944 147 
L 4313 147 
L 4313 -847 
L 3856 -847 
L 3856 147 
L 2244 147 
L 2244 547 
z
M 38 1403 
Q 603 2319 947 3666 
L 113 3666 
L 113 4103 
L 963 4103 
L 963 5322 
L 1400 5322 
L 1400 4103 
L 2125 4103 
L 2125 3666 
L 1400 3666 
L 1400 2606 
L 1625 2784 
Q 1931 2441 2175 2141 
L 1850 1866 
Q 1656 2144 1400 2456 
L 1400 -841 
L 963 -841 
L 963 2672 
Q 650 1547 238 853 
Q 156 1122 38 1403 
z
M 3856 2097 
L 4313 2097 
L 4313 1559 
L 5284 1559 
Q 4841 1953 4556 2466 
L 3550 2466 
Q 3269 1984 2825 1559 
L 3856 1559 
L 3856 2097 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-6027" d="M 1963 -153 
L 3988 -153 
L 3988 1522 
L 2375 1522 
L 2375 1959 
L 3988 1959 
L 3988 3472 
L 2975 3472 
Q 2744 2878 2438 2291 
Q 2169 2509 2019 2578 
Q 2644 3709 2894 4891 
L 3388 4759 
Q 3281 4328 3138 3909 
L 3988 3909 
L 3988 5284 
L 4481 5284 
L 4481 3909 
L 6125 3909 
L 6125 3472 
L 4481 3472 
L 4481 1959 
L 6000 1959 
L 6000 1522 
L 4481 1522 
L 4481 -153 
L 6319 -153 
L 6319 -591 
L 1963 -591 
L 1963 -153 
z
M 988 5291 
L 1469 5291 
L 1469 4091 
L 1738 4241 
Q 2063 3734 2294 3291 
L 1906 3041 
Q 1688 3503 1469 3859 
L 1469 -872 
L 988 -872 
L 988 5291 
z
M 406 3878 
L 819 3784 
Q 706 3028 506 2178 
Q 338 2241 75 2297 
Q 263 2978 406 3878 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-589e"/>
     <use xlink:href="#MicrosoftYaHei-5f3a" x="100"/>
     <use xlink:href="#MicrosoftYaHei-5bf9" x="200"/>
     <use xlink:href="#MicrosoftYaHei-6297" x="300"/>
     <use xlink:href="#MicrosoftYaHei-9c81" x="400"/>
     <use xlink:href="#MicrosoftYaHei-68d2" x="500"/>
     <use xlink:href="#MicrosoftYaHei-6027" x="600"/>
    </g>
   </g>
   <g id="text_5">
    <!-- 提高模型可解释性 -->
    <g transform="translate(493.729391 16.7925) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-63d0" d="M 2569 5041 
L 5688 5041 
L 5688 2572 
L 5238 2572 
L 5238 2753 
L 3019 2753 
L 3019 2572 
L 2569 2572 
L 2569 5041 
z
M 1594 -559 
Q 2475 178 2581 1553 
L 3050 1553 
Q 3019 1116 2922 734 
Q 3319 128 3956 -84 
L 3956 1797 
L 2025 1797 
L 2025 2209 
L 6238 2209 
L 6238 1797 
L 4425 1797 
L 4425 1097 
L 5994 1097 
L 5994 697 
L 4425 697 
L 4425 -184 
Q 4850 -234 6313 -178 
Q 6300 -209 6169 -641 
Q 5425 -659 4625 -622 
Q 3363 -606 2769 266 
Q 2506 -388 2006 -853 
Q 1819 -697 1594 -559 
z
M 81 2003 
Q 481 2175 888 2366 
L 888 3697 
L 131 3697 
L 131 4122 
L 888 4122 
L 888 5284 
L 1363 5284 
L 1363 4122 
L 2163 4122 
L 2163 3697 
L 1363 3697 
L 1363 2597 
Q 1722 2775 2081 2972 
Q 2100 2728 2131 2472 
Q 1747 2281 1363 2088 
L 1363 -91 
Q 1363 -697 900 -747 
Q 575 -778 169 -766 
Q 131 -503 88 -247 
Q 431 -284 675 -284 
Q 888 -284 888 -53 
L 888 1844 
Q 513 1650 144 1453 
L 81 2003 
z
M 3019 3153 
L 5238 3153 
L 5238 3703 
L 3019 3703 
L 3019 3153 
z
M 5238 4641 
L 3019 4641 
L 3019 4097 
L 5238 4097 
L 5238 4641 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-9ad8" d="M 1194 3922 
L 5250 3922 
L 5250 2397 
L 4775 2397 
L 4775 2597 
L 1669 2597 
L 1669 2403 
L 1194 2403 
L 1194 3922 
z
M 969 -784 
L 494 -784 
L 494 2147 
L 5956 2147 
L 5956 22 
Q 5956 -747 5231 -747 
Q 4825 -747 4319 -734 
Q 4281 -522 4219 -241 
Q 4794 -284 5100 -284 
Q 5481 -284 5481 97 
L 5481 1747 
L 969 1747 
L 969 -784 
z
M 1813 1334 
L 4638 1334 
L 4638 28 
L 2263 28 
L 2263 -241 
L 1813 -241 
L 1813 1334 
z
M 125 4747 
L 2988 4747 
Q 2850 5013 2725 5216 
L 3194 5403 
Q 3372 5094 3541 4747 
L 6275 4747 
L 6275 4334 
L 125 4334 
L 125 4747 
z
M 4775 3534 
L 1669 3534 
L 1669 2984 
L 4775 2984 
L 4775 3534 
z
M 4188 416 
L 4188 947 
L 2263 947 
L 2263 416 
L 4188 416 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-53ef" d="M 813 3409 
L 3569 3409 
L 3569 941 
L 1306 941 
L 1306 497 
L 813 497 
L 813 3409 
z
M 119 4809 
L 6294 4809 
L 6294 4334 
L 5256 4334 
L 5256 103 
Q 5256 -741 4438 -747 
Q 3850 -747 3294 -741 
Q 3263 -453 3194 -172 
Q 3900 -222 4269 -222 
Q 4731 -222 4731 278 
L 4731 4334 
L 119 4334 
L 119 4809 
z
M 3075 2966 
L 1306 2966 
L 1306 1384 
L 3075 1384 
L 3075 2966 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-89e3" d="M 75 3559 
Q 756 4316 1188 5322 
L 1625 5203 
Q 1497 4941 1366 4697 
L 2544 4697 
L 2544 4309 
L 2084 3641 
L 2881 3641 
L 2881 -128 
Q 2881 -753 2263 -759 
Q 1994 -766 1706 -747 
Q 1688 -591 1638 -328 
Q 1938 -359 2175 -359 
Q 2475 -359 2475 -66 
L 2475 953 
L 1913 953 
L 1913 -203 
L 1506 -203 
L 1506 953 
L 950 953 
Q 897 -225 438 -847 
Q 294 -684 88 -528 
Q 550 153 550 1303 
L 550 3428 
Q 463 3319 375 3216 
Q 269 3359 75 3559 
z
M 3075 897 
L 4550 897 
L 4550 1797 
L 3794 1797 
Q 3659 1438 3513 1122 
Q 3331 1228 3106 1309 
Q 3456 2059 3663 2828 
L 4094 2709 
Q 4019 2456 3941 2222 
L 4550 2222 
L 4550 2897 
L 5013 2897 
L 5013 2222 
L 6131 2222 
L 6131 1797 
L 5013 1797 
L 5013 897 
L 6281 897 
L 6281 472 
L 5013 472 
L 5013 -841 
L 4550 -841 
L 4550 472 
L 3075 472 
L 3075 897 
z
M 3138 4997 
L 5950 4997 
Q 5931 4384 5888 3847 
Q 5844 3084 5081 3084 
Q 4819 3084 4438 3103 
Q 4400 3366 4350 3603 
Q 4800 3547 5056 3547 
Q 5394 3547 5425 3909 
Q 5456 4191 5469 4572 
L 4284 4572 
Q 4263 3950 4063 3584 
Q 3844 3159 3219 2759 
Q 3119 2916 2919 3159 
Q 3438 3441 3638 3778 
Q 3806 4050 3828 4572 
L 3138 4572 
L 3138 4997 
z
M 709 3641 
L 1581 3641 
L 2038 4316 
L 1150 4316 
Q 934 3950 709 3641 
z
M 1913 1322 
L 2475 1322 
L 2475 2116 
L 1913 2116 
L 1913 1322 
z
M 2475 3272 
L 1913 3272 
L 1913 2484 
L 2475 2484 
L 2475 3272 
z
M 956 1322 
L 1506 1322 
L 1506 2116 
L 956 2116 
L 956 1322 
z
M 956 2484 
L 1506 2484 
L 1506 3272 
L 956 3272 
L 956 2484 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-91ca" d="M 2806 4559 
L 2806 4984 
L 5944 4984 
L 5944 4572 
Q 5575 3828 4863 3284 
Q 5475 2984 6319 2884 
Q 6244 2741 6081 2391 
Q 5119 2597 4444 3003 
Q 3756 2584 2825 2316 
Q 2769 2453 2594 2766 
Q 3419 2978 4031 3284 
Q 3394 3803 3100 4559 
L 2806 4559 
z
M 88 534 
Q 744 1403 1113 2472 
L 144 2472 
L 144 2878 
L 1213 2878 
L 1213 4516 
Q 719 4478 244 4447 
Q 213 4659 156 4878 
Q 1513 4953 2431 5084 
L 2613 4634 
Q 2125 4591 1656 4553 
L 1656 2878 
L 2544 2878 
L 2544 2472 
L 1656 2472 
L 1656 1922 
L 1925 2116 
Q 2388 1659 2638 1366 
L 2288 1053 
Q 2050 1366 1656 1797 
L 1656 -828 
L 1213 -828 
L 1213 1753 
Q 838 791 281 34 
Q 200 291 88 534 
z
M 2556 697 
L 4200 697 
L 4200 1434 
L 2844 1434 
L 2844 1872 
L 4200 1872 
L 4200 2597 
L 4669 2597 
L 4669 1872 
L 6050 1872 
L 6050 1434 
L 4669 1434 
L 4669 697 
L 6319 697 
L 6319 259 
L 4669 259 
L 4669 -809 
L 4200 -809 
L 4200 259 
L 2556 259 
L 2556 697 
z
M 5450 4559 
L 3550 4559 
Q 3863 3922 4444 3522 
Q 5119 3966 5450 4559 
z
M 1800 3197 
Q 2113 3759 2294 4228 
L 2700 4059 
Q 2450 3503 2156 3016 
L 1800 3197 
z
M 188 4072 
L 550 4234 
Q 875 3609 1013 3247 
L 600 3047 
Q 394 3634 188 4072 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-63d0"/>
     <use xlink:href="#MicrosoftYaHei-9ad8" x="100"/>
     <use xlink:href="#MicrosoftYaHei-6a21" x="200"/>
     <use xlink:href="#MicrosoftYaHei-578b" x="300"/>
     <use xlink:href="#MicrosoftYaHei-53ef" x="400"/>
     <use xlink:href="#MicrosoftYaHei-89e3" x="500"/>
     <use xlink:href="#MicrosoftYaHei-91ca" x="600"/>
     <use xlink:href="#MicrosoftYaHei-6027" x="700"/>
    </g>
   </g>
   <g id="text_6">
    <!-- 自监督学习探索 -->
    <g transform="translate(442.0875 36.716065) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-81ea" d="M 775 4466 
L 2391 4466 
Q 2525 4828 2688 5328 
L 3219 5166 
Q 3059 4778 2909 4466 
L 5625 4466 
L 5625 -822 
L 5125 -822 
L 5125 -447 
L 1275 -447 
L 1275 -822 
L 775 -822 
L 775 4466 
z
M 1275 3 
L 5125 3 
L 5125 1053 
L 1275 1053 
L 1275 3 
z
M 1275 1497 
L 5125 1497 
L 5125 2534 
L 1275 2534 
L 1275 1497 
z
M 5125 4016 
L 1275 4016 
L 1275 2978 
L 5125 2978 
L 5125 4016 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-76d1" d="M 131 -153 
L 863 -153 
L 863 1859 
L 5538 1859 
L 5538 -153 
L 6281 -153 
L 6281 -603 
L 131 -603 
L 131 -153 
z
M 2569 3041 
Q 3363 4028 3825 5359 
L 4300 5216 
Q 4138 4797 3963 4422 
L 6225 4422 
L 6225 3984 
L 3747 3984 
Q 3378 3284 2956 2747 
Q 2769 2897 2569 3041 
z
M 2456 5297 
L 2456 2059 
L 1981 2059 
L 1981 5297 
L 2456 5297 
z
M 5063 1422 
L 4125 1422 
L 4125 -153 
L 5063 -153 
L 5063 1422 
z
M 1338 -153 
L 2269 -153 
L 2269 1422 
L 1338 1422 
L 1338 -153 
z
M 2731 -153 
L 3663 -153 
L 3663 1422 
L 2731 1422 
L 2731 -153 
z
M 625 4934 
L 1094 4934 
L 1094 2347 
L 625 2347 
L 625 4934 
z
M 3938 3309 
L 4256 3641 
Q 4963 3128 5600 2572 
L 5250 2191 
Q 4569 2828 3938 3309 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-7763" d="M 1025 2166 
L 5463 2166 
L 5463 -866 
L 5013 -866 
L 5013 -528 
L 1475 -528 
L 1475 -866 
L 1025 -866 
L 1025 2166 
z
M 3306 4503 
L 3306 4878 
L 5944 4878 
L 5944 4541 
Q 5575 3697 4944 3128 
Q 5494 2747 6331 2541 
Q 6181 2322 6088 2116 
Q 5175 2409 4594 2853 
Q 4094 2503 3444 2209 
Q 3350 2366 3175 2572 
Q 3794 2809 4275 3134 
Q 3738 3697 3475 4503 
L 3306 4503 
z
M 100 3997 
L 1350 3997 
L 1350 5309 
L 1788 5309 
L 1788 4816 
L 3063 4816 
L 3063 4453 
L 1788 4453 
L 1788 3997 
L 3400 3997 
L 3400 3634 
L 100 3634 
L 100 3997 
z
M 1475 -153 
L 5013 -153 
L 5013 266 
L 1475 266 
L 1475 -153 
z
M 5013 1791 
L 1475 1791 
L 1475 1378 
L 5013 1378 
L 5013 1791 
z
M 1475 616 
L 5013 616 
L 5013 1028 
L 1475 1028 
L 1475 616 
z
M 5438 4503 
L 3881 4503 
Q 4150 3822 4606 3391 
Q 5163 3872 5438 4503 
z
M 56 2541 
Q 519 2928 894 3372 
L 1231 3109 
Q 794 2634 344 2234 
Q 206 2403 56 2541 
z
M 2219 3041 
L 2463 3334 
Q 2838 3097 3213 2841 
L 2950 2509 
Q 2619 2772 2219 3041 
z
M 1569 3509 
L 2006 3509 
L 2006 2316 
L 1569 2316 
L 1569 3509 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-5b66" d="M 144 1359 
L 3069 1359 
L 3069 1709 
L 4263 2384 
L 1325 2384 
L 1325 2841 
L 4994 2841 
L 4994 2316 
L 3575 1497 
L 3575 1359 
L 6263 1359 
L 6263 903 
L 3575 903 
L 3575 -41 
Q 3575 -778 2831 -778 
Q 2419 -778 1863 -766 
Q 1844 -553 1788 -234 
Q 2288 -284 2744 -291 
Q 3069 -297 3069 53 
L 3069 903 
L 144 903 
L 144 1359 
z
M 356 3909 
L 6056 3909 
L 6056 2647 
L 5563 2647 
L 5563 3453 
L 850 3453 
L 850 2647 
L 356 2647 
L 356 3909 
z
M 4313 4272 
Q 4731 4722 5100 5241 
L 5550 4909 
Q 5144 4447 4681 3941 
L 4313 4272 
z
M 2538 5116 
L 2944 5353 
Q 3325 4853 3650 4334 
L 3200 4078 
Q 2925 4566 2538 5116 
z
M 850 4928 
L 1219 5197 
Q 1600 4778 1969 4291 
L 1544 3991 
Q 1194 4522 850 4928 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-4e60" d="M 500 4803 
L 5725 4803 
Q 5663 1853 5606 784 
Q 5550 -616 4181 -616 
Q 3644 -616 3013 -591 
Q 2988 -347 2913 -9 
Q 3713 -66 4113 -66 
Q 4631 -72 4850 153 
Q 5056 378 5081 916 
Q 5144 2416 5169 4291 
L 500 4291 
L 500 4803 
z
M 363 834 
Q 2856 1591 4688 2253 
Q 4669 1834 4688 1709 
Q 2513 978 556 297 
L 363 834 
z
M 1131 3372 
L 1400 3778 
Q 2713 3203 3563 2766 
L 3275 2291 
Q 2069 2947 1131 3372 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-63a2" d="M 6375 91 
Q 6188 -141 6038 -359 
Q 5000 353 4400 1703 
L 4400 -853 
L 3938 -853 
L 3938 1678 
Q 3244 416 2131 -422 
Q 1981 -234 1781 -34 
Q 2844 609 3525 1784 
L 2144 1784 
L 2144 2209 
L 3938 2209 
L 3938 2997 
L 4400 2997 
L 4400 2209 
L 6244 2209 
L 6244 1784 
L 4800 1784 
Q 5394 591 6375 91 
z
M 100 1941 
Q 531 2053 944 2178 
L 944 3616 
L 175 3616 
L 175 4066 
L 944 4066 
L 944 5241 
L 1406 5241 
L 1406 4066 
L 2038 4066 
L 2038 3616 
L 1406 3616 
L 1406 2322 
Q 1694 2416 1981 2516 
Q 1988 2234 2019 2028 
Q 1713 1928 1406 1828 
L 1406 -78 
Q 1406 -766 806 -772 
Q 575 -778 213 -766 
Q 188 -509 138 -247 
Q 500 -284 694 -284 
Q 944 -284 944 34 
L 944 1672 
Q 556 1541 163 1403 
L 100 1941 
z
M 2263 4853 
L 6125 4853 
L 6125 3809 
L 5675 3809 
L 5675 4441 
L 2713 4441 
L 2713 3809 
L 2263 3809 
L 2263 4853 
z
M 4438 3753 
L 4763 4078 
Q 5494 3478 6106 2897 
L 5744 2522 
Q 5038 3247 4438 3753 
z
M 3381 4059 
L 3781 3784 
Q 3363 3228 2650 2447 
Q 2488 2591 2275 2759 
Q 2875 3334 3381 4059 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-7d22" d="M 4431 2197 
Q 5281 1559 5956 966 
L 5588 622 
Q 5391 816 5178 1009 
Q 4331 978 3531 941 
L 3531 3 
Q 3531 -766 2713 -766 
Q 2344 -766 1925 -753 
Q 1894 -528 1838 -253 
Q 2288 -303 2619 -303 
Q 3044 -303 3044 109 
L 3044 913 
Q 2413 881 1813 841 
Q 1450 816 1088 759 
L 900 1253 
Q 1325 1341 1750 1541 
Q 2350 1831 2884 2131 
Q 2381 2097 1919 2059 
Q 1625 2028 1400 1984 
L 1244 2409 
Q 1513 2509 1769 2672 
Q 2175 2934 2619 3297 
L 806 3297 
L 806 2578 
L 319 2578 
L 319 3722 
L 2944 3722 
L 2944 4303 
L 500 4303 
L 500 4728 
L 2944 4728 
L 2944 5341 
L 3456 5341 
L 3456 4728 
L 5925 4728 
L 5925 4303 
L 3456 4303 
L 3456 3722 
L 6081 3722 
L 6081 2578 
L 5594 2578 
L 5594 3297 
L 3231 3297 
Q 2584 2791 2000 2416 
Q 2731 2447 3459 2472 
Q 3891 2738 4269 3009 
L 4650 2716 
Q 3244 1866 1988 1253 
Q 3313 1309 4747 1388 
Q 4441 1650 4106 1916 
L 4431 2197 
z
M 4356 678 
Q 5425 203 6294 -266 
Q 6113 -503 6000 -697 
Q 5181 -191 4094 322 
L 4356 678 
z
M 1838 728 
L 2131 372 
Q 1294 -228 394 -684 
Q 275 -491 88 -278 
Q 1031 141 1838 728 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-81ea"/>
     <use xlink:href="#MicrosoftYaHei-76d1" x="100"/>
     <use xlink:href="#MicrosoftYaHei-7763" x="200"/>
     <use xlink:href="#MicrosoftYaHei-5b66" x="300"/>
     <use xlink:href="#MicrosoftYaHei-4e60" x="400"/>
     <use xlink:href="#MicrosoftYaHei-63a2" x="500"/>
     <use xlink:href="#MicrosoftYaHei-7d22" x="600"/>
    </g>
   </g>
   <g id="text_7">
    <!-- 特征工程的深入研究 -->
    <g style="fill: #ffffff" transform="translate(240.631428 367.966363) scale(0.14 -0.14)">
     <defs>
      <path id="MicrosoftYaHei-Bold-7279" d="M 206 922 
L 94 1809 
L 1175 1988 
L 1175 3209 
L 763 3209 
Q 697 2747 606 2316 
Q 369 2466 88 2597 
Q 281 3603 350 4778 
L 919 4722 
Q 897 4359 863 4016 
L 1175 4016 
L 1175 5291 
L 1938 5291 
L 1938 4016 
L 2300 4016 
L 2300 3209 
L 1938 3209 
L 1938 2128 
L 2344 2209 
Q 2319 1859 2331 1372 
L 1938 1297 
L 1938 -834 
L 1175 -834 
L 1175 1144 
Q 663 1034 206 922 
z
M 2363 3309 
L 3863 3309 
L 3863 3828 
L 2613 3828 
L 2613 4578 
L 3863 4578 
L 3863 5291 
L 4725 5291 
L 4725 4578 
L 6013 4578 
L 6013 3828 
L 4725 3828 
L 4725 3309 
L 6306 3309 
L 6306 2559 
L 2363 2559 
L 2363 3309 
z
M 2556 966 
L 2984 1278 
L 2469 1278 
L 2469 2028 
L 4738 2028 
L 4738 2428 
L 5600 2428 
L 5600 2028 
L 6225 2028 
L 6225 1278 
L 5600 1278 
L 5600 347 
Q 5600 -131 5414 -403 
Q 5228 -675 4918 -751 
Q 4609 -828 3681 -828 
Q 3581 -284 3506 -3 
Q 3956 -41 4281 -41 
Q 4738 -41 4738 491 
L 4738 1278 
L 3278 1278 
Q 3628 913 3981 497 
L 3344 3 
Q 2963 522 2556 966 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-5f81" d="M 1456 5353 
L 2213 4834 
Q 1369 3791 438 3016 
Q 313 3547 131 4003 
Q 863 4603 1456 5353 
z
M 1988 241 
L 2575 241 
L 2575 3397 
L 3413 3397 
L 3413 241 
L 4125 241 
L 4125 4116 
L 2400 4116 
L 2400 4916 
L 6225 4916 
L 6225 4116 
L 4975 4116 
L 4975 2728 
L 6075 2728 
L 6075 1941 
L 4975 1941 
L 4975 241 
L 6300 241 
L 6300 -559 
L 1988 -559 
L 1988 241 
z
M 1606 3622 
L 2356 3159 
Q 2031 2678 1681 2250 
L 1681 -797 
L 844 -797 
L 844 1322 
Q 597 1081 344 859 
Q 219 1441 88 1878 
Q 988 2709 1606 3622 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-5de5" d="M 163 384 
L 2675 384 
L 2675 3966 
L 500 3966 
L 500 4853 
L 5925 4853 
L 5925 3966 
L 3700 3966 
L 3700 384 
L 6244 384 
L 6244 -503 
L 163 -503 
L 163 384 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-7a0b" d="M 125 3484 
L 956 3484 
L 956 4172 
Q 584 4134 213 4109 
Q 169 4584 100 4909 
Q 1088 4959 2325 5172 
L 2519 4391 
Q 2138 4322 1756 4269 
L 1756 3484 
L 2469 3484 
L 2469 2741 
L 1756 2741 
L 1756 2053 
L 1931 2228 
Q 2244 1966 2744 1491 
L 2188 953 
Q 1966 1228 1756 1466 
L 1756 -866 
L 956 -866 
L 956 1544 
Q 725 841 388 203 
Q 269 741 88 1253 
Q 500 1825 838 2741 
L 125 2741 
L 125 3484 
z
M 2688 5066 
L 5944 5066 
L 5944 2966 
L 2688 2966 
L 2688 5066 
z
M 5113 4341 
L 3519 4341 
L 3519 3691 
L 5113 3691 
L 5113 4341 
z
M 2238 53 
L 3888 53 
L 3888 591 
L 2663 591 
L 2663 1291 
L 3888 1291 
L 3888 1809 
L 2550 1809 
L 2550 2534 
L 6150 2534 
L 6150 1809 
L 4744 1809 
L 4744 1291 
L 5981 1291 
L 5981 591 
L 4744 591 
L 4744 53 
L 6300 53 
L 6300 -672 
L 2238 -672 
L 2238 53 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-7684" d="M 1250 5303 
L 2181 5159 
L 1900 4309 
L 2963 4309 
L 2963 3091 
Q 3466 3934 3925 5316 
L 4788 5091 
Q 4650 4675 4494 4278 
L 6106 4278 
L 6034 681 
Q 6019 100 5937 -133 
Q 5856 -366 5684 -512 
Q 5513 -659 5205 -712 
Q 4897 -766 3863 -766 
Q 3769 -216 3625 178 
Q 4088 141 4609 141 
Q 4759 141 4868 175 
Q 4978 209 5048 279 
Q 5119 350 5144 437 
Q 5169 525 5184 1206 
L 5213 2484 
Q 5225 3081 5225 3453 
L 4138 3453 
Q 3931 3016 3700 2603 
Q 3403 2797 2963 2988 
L 2963 -734 
L 2094 -734 
L 2094 -284 
L 1206 -284 
L 1206 -772 
L 338 -772 
L 338 4309 
L 997 4309 
Q 1178 4922 1250 5303 
z
M 2094 3491 
L 1206 3491 
L 1206 2422 
L 2094 2422 
L 2094 3491 
z
M 1206 534 
L 2094 534 
L 2094 1603 
L 1206 1603 
L 1206 534 
z
M 3944 2703 
Q 4394 2166 4913 1397 
L 4125 872 
Q 3650 1653 3219 2203 
L 3944 2703 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-6df1" d="M 694 5297 
Q 1288 4928 1869 4497 
L 1313 3859 
Q 844 4272 175 4741 
L 694 5297 
z
M 1925 3634 
L 1925 5003 
L 6056 5003 
L 6056 3634 
L 5250 3634 
L 5250 4278 
L 2731 4278 
L 2731 3634 
L 1925 3634 
z
M 3825 3447 
Q 3075 2841 2419 2397 
L 1906 2984 
Q 2638 3422 3269 3978 
L 3825 3447 
z
M 4175 3447 
L 4694 3972 
Q 5481 3428 6081 2972 
L 5550 2384 
Q 4869 2947 4175 3447 
z
M 625 3634 
Q 1231 3209 1738 2784 
L 1150 2134 
Q 738 2541 81 3047 
L 625 3634 
z
M 6188 1447 
L 4716 1447 
Q 5353 694 6331 316 
Q 6100 -47 5844 -491 
Q 4972 9 4406 828 
L 4406 -834 
L 3569 -834 
L 3569 878 
Q 2991 78 2081 -566 
Q 1863 -184 1606 178 
Q 2516 684 3203 1447 
L 1850 1447 
L 1850 2172 
L 3569 2172 
L 3569 2997 
L 4406 2997 
L 4406 2172 
L 6188 2172 
L 6188 1447 
z
M 900 1872 
Q 1281 1684 1719 1547 
Q 1275 172 988 -834 
Q 544 -666 138 -541 
Q 506 416 900 1872 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-5165" d="M 1606 4822 
L 2438 5303 
Q 3106 4778 4019 2934 
Q 4900 1134 6300 128 
Q 5769 -441 5488 -797 
Q 4138 525 3238 2369 
Q 2447 466 813 -822 
Q 525 -459 75 -3 
Q 2013 1391 2709 3494 
Q 2309 4197 1606 4822 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-Bold-7279"/>
     <use xlink:href="#MicrosoftYaHei-Bold-5f81" x="100"/>
     <use xlink:href="#MicrosoftYaHei-Bold-5de5" x="200"/>
     <use xlink:href="#MicrosoftYaHei-Bold-7a0b" x="300"/>
     <use xlink:href="#MicrosoftYaHei-Bold-7684" x="400"/>
     <use xlink:href="#MicrosoftYaHei-Bold-6df1" x="500"/>
     <use xlink:href="#MicrosoftYaHei-Bold-5165" x="600"/>
     <use xlink:href="#MicrosoftYaHei-Bold-7814" x="700"/>
     <use xlink:href="#MicrosoftYaHei-Bold-7a76" x="800"/>
    </g>
   </g>
   <g id="text_8">
    <!-- 动态特征提取 -->
    <g transform="translate(149.446235 242.766638) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-52a8" d="M 2975 4022 
L 3906 4022 
Q 3916 4650 3913 5309 
L 4425 5309 
Q 4422 4656 4409 4022 
L 6094 4022 
Q 6050 1659 5969 216 
Q 5900 -666 5150 -678 
Q 4763 -678 4306 -659 
Q 4263 -397 4206 -109 
Q 4638 -166 4994 -172 
Q 5456 -172 5475 347 
Q 5569 1897 5588 3547 
L 4400 3547 
Q 4394 3278 4388 3016 
Q 4356 553 2900 -872 
Q 2725 -659 2525 -447 
Q 3831 716 3888 3028 
Q 3894 3284 3900 3547 
L 2975 3547 
L 2975 4022 
z
M 2338 1866 
Q 2731 1016 3063 178 
Q 2788 59 2594 -28 
Q 2550 91 2506 209 
Q 1316 97 338 -16 
L 119 491 
Q 394 647 563 997 
Q 869 1613 1200 2559 
L 144 2559 
L 144 3022 
L 3163 3022 
L 3163 2559 
L 1725 2559 
Q 1191 1228 800 491 
Q 1519 544 2347 631 
Q 2138 1163 1906 1672 
L 2338 1866 
z
M 406 4778 
L 2913 4778 
L 2913 4316 
L 406 4316 
L 406 4778 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-6001" d="M 325 4434 
L 2888 4434 
Q 2972 4822 2969 5328 
L 3438 5328 
Q 3444 4841 3372 4434 
L 6075 4434 
L 6075 3991 
L 3528 3991 
Q 4419 2706 6231 2241 
Q 6050 2034 5881 1759 
Q 4041 2394 3172 3772 
Q 2975 3331 2588 2953 
Q 3078 2616 3538 2259 
L 3225 1891 
Q 2828 2244 2259 2675 
Q 1556 2138 388 1703 
Q 294 1922 113 2178 
Q 2250 2866 2750 3991 
L 325 3991 
L 325 4434 
z
M 1713 1547 
L 2200 1547 
L 2200 203 
Q 2200 -172 2575 -172 
L 3763 -172 
Q 4175 -172 4256 134 
Q 4331 428 4381 1028 
Q 4556 953 4863 847 
Q 4763 128 4706 -47 
Q 4563 -616 3869 -616 
L 2488 -616 
Q 1713 -616 1713 172 
L 1713 1547 
z
M 813 1447 
L 1281 1297 
Q 1038 522 663 -297 
Q 463 -203 194 -116 
Q 569 591 813 1447 
z
M 4894 1272 
L 5263 1553 
Q 5781 972 6306 309 
L 5888 -9 
Q 5425 641 4894 1272 
z
M 2594 1503 
L 2913 1809 
Q 3419 1403 3850 991 
L 3488 641 
Q 3094 1059 2594 1503 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-7279" d="M 81 1609 
Q 666 1806 1238 2003 
L 1238 3472 
L 744 3472 
Q 616 2878 431 2384 
Q 238 2522 19 2659 
Q 369 3484 475 4716 
L 925 4641 
Q 884 4250 822 3891 
L 1238 3891 
L 1238 5291 
L 1706 5291 
L 1706 3891 
L 2488 3891 
L 2488 3472 
L 1706 3472 
L 1706 2166 
Q 2059 2291 2406 2416 
Q 2438 2141 2475 1941 
Q 2113 1813 1706 1663 
L 1706 -828 
L 1238 -828 
L 1238 1488 
Q 734 1297 175 1078 
L 81 1609 
z
M 2263 3103 
L 3981 3103 
L 3981 4028 
L 2525 4028 
L 2525 4453 
L 3981 4453 
L 3981 5322 
L 4469 5322 
L 4469 4453 
L 5988 4453 
L 5988 4028 
L 4469 4028 
L 4469 3103 
L 6275 3103 
L 6275 2678 
L 2263 2678 
L 2263 3103 
z
M 2394 1834 
L 4906 1834 
L 4906 2497 
L 5394 2497 
L 5394 1834 
L 6306 1834 
L 6306 1409 
L 5394 1409 
L 5394 34 
Q 5394 -747 4606 -753 
Q 4325 -759 3756 -747 
Q 3719 -503 3663 -209 
Q 4156 -259 4513 -259 
Q 4906 -259 4906 178 
L 4906 1409 
L 2394 1409 
L 2394 1834 
z
M 2744 978 
L 3106 1266 
Q 3556 772 3881 366 
L 3481 59 
Q 3125 541 2744 978 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-5f81" d="M 2013 -59 
L 2656 -59 
L 2656 3141 
L 3144 3141 
L 3144 -59 
L 4231 -59 
L 4231 4453 
L 2438 4453 
L 2438 4922 
L 6175 4922 
L 6175 4453 
L 4719 4453 
L 4719 2559 
L 6050 2559 
L 6050 2097 
L 4719 2097 
L 4719 -59 
L 6269 -59 
L 6269 -522 
L 2013 -522 
L 2013 -59 
z
M 94 1578 
Q 1069 2372 1856 3809 
L 2294 3541 
Q 1975 2991 1619 2506 
L 1619 -816 
L 1100 -816 
L 1100 1863 
Q 725 1434 313 1072 
Q 213 1334 94 1578 
z
M 231 3766 
Q 1225 4503 1656 5222 
L 2106 4947 
Q 1456 4053 481 3284 
Q 369 3522 231 3766 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-53d6" d="M 88 641 
Q 363 675 650 713 
L 650 4578 
L 181 4578 
L 181 5016 
L 3244 5016 
L 3244 4797 
L 5975 4797 
L 5975 4403 
Q 5613 2097 4906 1006 
Q 5497 172 6325 -291 
Q 6150 -472 5944 -753 
Q 5275 -266 4625 609 
Q 3994 -219 3125 -828 
Q 2947 -594 2806 -434 
L 2806 -822 
L 2338 -822 
L 2338 531 
Q 1197 353 156 178 
L 88 641 
z
M 3194 4372 
L 3194 4578 
L 2806 4578 
L 2806 1025 
Q 3156 1081 3519 1141 
Q 3500 884 3500 709 
Q 3147 656 2806 603 
L 2806 -403 
Q 3681 109 4350 1006 
Q 3628 2156 3422 4372 
L 3194 4372 
z
M 5506 4372 
L 3875 4372 
Q 4022 2544 4638 1428 
Q 5181 2344 5506 4372 
z
M 2338 953 
L 2338 1866 
L 1119 1866 
L 1119 775 
Q 1703 859 2338 953 
z
M 1119 2266 
L 2338 2266 
L 2338 3222 
L 1119 3222 
L 1119 2266 
z
M 1119 3622 
L 2338 3622 
L 2338 4578 
L 1119 4578 
L 1119 3622 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-52a8"/>
     <use xlink:href="#MicrosoftYaHei-6001" x="100"/>
     <use xlink:href="#MicrosoftYaHei-7279" x="200"/>
     <use xlink:href="#MicrosoftYaHei-5f81" x="300"/>
     <use xlink:href="#MicrosoftYaHei-63d0" x="400"/>
     <use xlink:href="#MicrosoftYaHei-53d6" x="500"/>
    </g>
   </g>
   <g id="text_9">
    <!-- 多模态特征融合 -->
    <g transform="translate(105.68081 288.67032) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-591a" d="M 3719 2803 
L 4338 2803 
Q 4050 2522 3744 2272 
L 6019 2272 
L 6019 1816 
Q 4006 -366 650 -891 
Q 538 -666 369 -422 
Q 1894 -203 3100 322 
Q 2919 559 2581 903 
L 2956 1178 
Q 3275 847 3538 522 
Q 4588 1059 5344 1834 
L 3200 1834 
Q 2100 1009 881 484 
Q 769 666 581 909 
Q 2319 1609 3719 2803 
z
M 2775 5316 
L 3406 5316 
Q 3088 4972 2756 4672 
L 5700 4672 
L 5700 4247 
Q 3550 2516 481 1666 
Q 350 1916 194 2103 
Q 1481 2441 2506 2872 
Q 2281 3122 1913 3484 
L 2263 3759 
Q 2631 3403 2944 3066 
Q 4088 3584 4950 4234 
L 2256 4234 
Q 1606 3678 713 3172 
Q 575 3359 369 3566 
Q 1800 4328 2775 5316 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-878d" d="M 5425 1016 
L 5844 1134 
Q 6125 178 6319 -666 
L 5881 -778 
Q 5844 -609 5800 -441 
Q 4613 -572 3569 -697 
L 3475 -178 
Q 4063 -141 4631 -97 
L 4631 1591 
L 4113 1591 
L 4113 1384 
L 3656 1384 
L 3656 4141 
L 4631 4141 
L 4631 5259 
L 5069 5259 
L 5069 4141 
L 6013 4141 
L 6013 1591 
L 5069 1591 
L 5069 -59 
Q 5388 -34 5694 -16 
Q 5575 484 5425 1016 
z
M 763 -797 
L 350 -797 
L 350 2403 
L 3319 2403 
L 3319 -203 
Q 3319 -791 2700 -791 
L 2219 -791 
Q 2188 -572 2125 -359 
Q 2425 -384 2613 -384 
Q 2906 -384 2906 -116 
L 2906 2016 
L 763 2016 
L 763 -797 
z
M 606 4184 
L 3069 4184 
L 3069 2678 
L 2656 2678 
L 2656 2841 
L 1019 2841 
L 1019 2678 
L 606 2678 
L 606 4184 
z
M 875 1016 
L 1838 1016 
Q 2194 1659 2300 1909 
L 2669 1734 
Q 2413 1316 2213 1016 
L 2781 1016 
L 2781 641 
L 2025 641 
L 2025 -659 
L 1638 -659 
L 1638 641 
L 875 641 
L 875 1016 
z
M 300 5016 
L 3344 5016 
L 3344 4616 
L 300 4616 
L 300 5016 
z
M 2656 3809 
L 1019 3809 
L 1019 3216 
L 2656 3216 
L 2656 3809 
z
M 5588 2003 
L 5588 3728 
L 5069 3728 
L 5069 2003 
L 5588 2003 
z
M 4113 2003 
L 4631 2003 
L 4631 3728 
L 4113 3728 
L 4113 2003 
z
M 988 1697 
L 1288 1884 
Q 1513 1584 1694 1241 
L 1369 1041 
Q 1188 1378 988 1697 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-5408" d="M 1025 1909 
L 5381 1909 
L 5381 -866 
L 4881 -866 
L 4881 -447 
L 1525 -447 
L 1525 -878 
L 1025 -878 
L 1025 1909 
z
M 3700 5234 
L 3550 5053 
Q 4594 3697 6319 3009 
Q 6125 2734 5988 2509 
Q 5481 2753 5025 3059 
L 5025 2703 
L 1369 2703 
L 1369 3016 
Q 913 2716 394 2428 
Q 269 2641 81 2897 
Q 2013 3866 3000 5234 
L 3700 5234 
z
M 4881 1453 
L 1525 1453 
L 1525 -3 
L 4881 -3 
L 4881 1453 
z
M 3219 4691 
Q 2581 3872 1550 3147 
L 4894 3147 
Q 3944 3797 3219 4691 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-591a"/>
     <use xlink:href="#MicrosoftYaHei-6a21" x="100"/>
     <use xlink:href="#MicrosoftYaHei-6001" x="200"/>
     <use xlink:href="#MicrosoftYaHei-7279" x="300"/>
     <use xlink:href="#MicrosoftYaHei-5f81" x="400"/>
     <use xlink:href="#MicrosoftYaHei-878d" x="500"/>
     <use xlink:href="#MicrosoftYaHei-5408" x="600"/>
    </g>
   </g>
   <g id="text_10">
    <!-- 时空特征建模 -->
    <g transform="translate(92.725331 344.071842) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-65f6" d="M 344 4728 
L 2500 4728 
L 2500 -366 
L 2006 -366 
L 2006 153 
L 838 153 
L 838 -441 
L 344 -441 
L 344 4728 
z
M 2706 3941 
L 4925 3941 
L 4925 5322 
L 5425 5322 
L 5425 3941 
L 6313 3941 
L 6313 3478 
L 5425 3478 
L 5425 116 
Q 5425 -722 4625 -722 
Q 4263 -722 3563 -703 
Q 3531 -478 3463 -178 
Q 4119 -228 4488 -228 
Q 4925 -228 4925 216 
L 4925 3478 
L 2706 3478 
L 2706 3941 
z
M 838 616 
L 2006 616 
L 2006 2234 
L 838 2234 
L 838 616 
z
M 2006 4266 
L 838 4266 
L 838 2684 
L 2006 2684 
L 2006 4266 
z
M 3406 2878 
Q 3906 1966 4163 1422 
Q 4000 1347 3663 1172 
Q 3419 1766 2963 2653 
L 3406 2878 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-7a7a" d="M 331 -34 
L 2931 -34 
L 2931 1603 
L 975 1603 
L 975 2078 
L 5425 2078 
L 5425 1603 
L 3463 1603 
L 3463 -34 
L 6069 -34 
L 6069 -509 
L 331 -509 
L 331 -34 
z
M 369 4528 
L 3084 4528 
Q 2941 4800 2725 5141 
L 3188 5366 
Q 3444 4997 3631 4641 
L 3403 4528 
L 6038 4528 
L 6038 3316 
L 5525 3316 
L 5525 4053 
L 881 4053 
L 881 3316 
L 369 3316 
L 369 4528 
z
M 2381 3741 
L 2750 3384 
Q 1538 2434 469 1834 
Q 275 2091 94 2241 
Q 1319 2866 2381 3741 
z
M 3988 3734 
Q 5300 2953 6269 2259 
L 5913 1828 
Q 4969 2566 3688 3372 
L 3988 3734 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-5efa" d="M 2075 1009 
L 3675 1009 
L 3675 1553 
L 2288 1553 
L 2288 1928 
L 3675 1928 
L 3675 2472 
L 2375 2472 
L 2375 2847 
L 3675 2847 
L 3675 3391 
L 2050 3391 
L 2050 3778 
L 3675 3778 
L 3675 4322 
L 2375 4322 
L 2375 4697 
L 3675 4697 
L 3675 5309 
L 4138 5309 
L 4138 4697 
L 5588 4697 
L 5588 3778 
L 6244 3778 
L 6244 3391 
L 5588 3391 
L 5588 2303 
L 5138 2303 
L 5138 2472 
L 4138 2472 
L 4138 1928 
L 5713 1928 
L 5713 1553 
L 4138 1553 
L 4138 1009 
L 6000 1009 
L 6000 634 
L 4138 634 
L 4138 -9 
L 3675 -9 
L 3675 634 
L 2075 634 
L 2075 1009 
z
M 600 2066 
Q 788 1303 1125 813 
Q 1459 1500 1544 2397 
L 294 2397 
L 294 2853 
L 1444 4272 
L 169 4272 
L 169 4697 
L 2019 4697 
L 2019 4272 
L 844 2822 
L 2013 2822 
L 2013 2453 
Q 1897 1294 1447 444 
Q 2128 -147 3563 -153 
Q 4750 -178 6319 -122 
Q 6213 -378 6113 -616 
Q 4681 -609 3344 -603 
Q 1950 -603 1206 47 
Q 853 -466 350 -847 
Q 250 -666 94 -441 
Q 563 -84 881 391 
Q 463 922 188 1909 
L 600 2066 
z
M 4138 2847 
L 5138 2847 
L 5138 3391 
L 4138 3391 
L 4138 2847 
z
M 5138 4322 
L 4138 4322 
L 4138 3778 
L 5138 3778 
L 5138 4322 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-65f6"/>
     <use xlink:href="#MicrosoftYaHei-7a7a" x="100"/>
     <use xlink:href="#MicrosoftYaHei-7279" x="200"/>
     <use xlink:href="#MicrosoftYaHei-5f81" x="300"/>
     <use xlink:href="#MicrosoftYaHei-5efa" x="400"/>
     <use xlink:href="#MicrosoftYaHei-6a21" x="500"/>
    </g>
   </g>
   <g id="text_11">
    <!-- 特征隐私保护 -->
    <g transform="translate(94.866109 402.288963) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-9690" d="M 2481 1791 
L 5425 1791 
L 5425 2353 
L 2631 2353 
L 2631 2741 
L 5425 2741 
L 5425 3291 
L 2619 3291 
L 2619 3581 
Q 2459 3394 2294 3216 
Q 2213 3366 2019 3572 
Q 2800 4397 3300 5353 
L 3763 5228 
Q 3606 4953 3438 4691 
L 5356 4691 
L 5356 4272 
Q 5088 3947 4869 3691 
L 5875 3691 
L 5875 1228 
L 5425 1228 
L 5425 1391 
L 2481 1391 
L 2481 1791 
z
M 2181 1897 
Q 2319 866 1613 741 
Q 1388 697 1019 734 
Q 969 991 881 1228 
Q 1181 1153 1444 1178 
Q 1850 1216 1719 1916 
Q 1644 2422 1181 3028 
Q 1588 4022 1788 4616 
L 856 4616 
L 856 -847 
L 400 -847 
L 400 5053 
L 2256 5053 
L 2256 4616 
Q 1963 3834 1650 3103 
Q 2113 2472 2181 1897 
z
M 2881 1109 
L 3319 1109 
L 3319 147 
Q 3319 -234 3631 -234 
L 4319 -234 
Q 4769 -234 4819 53 
Q 4863 272 4888 659 
Q 5119 559 5338 484 
Q 5275 78 5225 -109 
Q 5106 -647 4481 -641 
L 3525 -641 
Q 2881 -641 2881 116 
L 2881 1109 
z
M 4775 4291 
L 3169 4291 
Q 2947 3981 2709 3691 
L 4316 3691 
Q 4553 4000 4775 4291 
z
M 2238 978 
L 2650 834 
Q 2438 153 2106 -422 
Q 1956 -341 1700 -216 
Q 2031 303 2238 978 
z
M 5256 859 
L 5588 1091 
Q 5969 647 6338 116 
L 5950 -141 
Q 5594 416 5256 859 
z
M 3588 1103 
L 3913 1347 
Q 4338 891 4525 616 
L 4156 334 
Q 3938 697 3588 1103 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-79c1" d="M 75 872 
Q 819 1791 1213 3003 
L 106 3003 
L 106 3459 
L 1250 3459 
L 1250 4372 
Q 763 4322 288 4278 
Q 244 4509 163 4753 
Q 1769 4891 2731 5034 
L 2856 4559 
Q 2281 4491 1719 4422 
L 1719 3459 
L 2925 3459 
L 2925 3003 
L 1719 3003 
L 1719 2091 
L 2000 2366 
Q 2488 1953 2919 1509 
L 2575 1153 
Q 2144 1603 1719 1997 
L 1719 -822 
L 1250 -822 
L 1250 1928 
Q 881 1003 313 341 
Q 206 597 75 872 
z
M 5031 2847 
Q 5719 1116 6319 -516 
L 5806 -747 
Q 5694 -403 5581 -66 
Q 4269 -184 3388 -284 
Q 3100 -328 2869 -409 
L 2663 116 
Q 2900 278 3056 741 
Q 3769 2772 4344 5178 
L 4913 5078 
Q 4094 2022 3369 222 
Q 4269 278 5425 378 
Q 5013 1578 4581 2672 
L 5031 2847 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-4fdd" d="M 2263 5003 
L 5750 5003 
L 5750 2647 
L 5275 2647 
L 5275 2884 
L 4225 2884 
L 4225 2041 
L 6288 2041 
L 6288 1616 
L 4588 1616 
Q 5163 659 6338 109 
Q 6144 -159 6025 -353 
Q 4850 359 4225 1572 
L 4225 -847 
L 3769 -847 
L 3769 1597 
Q 3013 234 1875 -478 
Q 1731 -303 1525 -97 
Q 2638 516 3331 1616 
L 1694 1616 
L 1694 2041 
L 3769 2041 
L 3769 2884 
L 2738 2884 
L 2738 2603 
L 2263 2603 
L 2263 5003 
z
M 69 2247 
Q 906 3497 1369 5278 
L 1831 5141 
Q 1575 4316 1275 3597 
L 1275 -816 
L 825 -816 
L 825 2653 
Q 556 2159 263 1734 
Q 175 1997 69 2247 
z
M 5275 4584 
L 2738 4584 
L 2738 3303 
L 5275 3303 
L 5275 4584 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-62a4" d="M 3719 5078 
L 4131 5341 
Q 4388 5041 4694 4616 
L 4291 4353 
L 5988 4353 
L 5988 1572 
L 5506 1572 
L 5506 1872 
L 3100 1872 
Q 3016 222 2206 -847 
Q 2050 -684 1800 -491 
Q 2625 522 2625 2409 
L 2625 4353 
L 4222 4353 
Q 3953 4763 3719 5078 
z
M 88 1997 
Q 528 2113 1013 2250 
L 1013 3691 
L 175 3691 
L 175 4122 
L 1013 4122 
L 1013 5297 
L 1494 5297 
L 1494 4122 
L 2288 4122 
L 2288 3691 
L 1494 3691 
L 1494 2388 
Q 1875 2500 2281 2622 
Q 2288 2309 2294 2128 
Q 1900 2016 1494 1894 
L 1494 -41 
Q 1494 -716 838 -722 
Q 625 -728 263 -722 
Q 225 -453 175 -203 
Q 544 -234 744 -234 
Q 1013 -234 1013 59 
L 1013 1744 
Q 588 1609 150 1466 
L 88 1997 
z
M 3113 3903 
L 3113 2322 
L 5506 2322 
L 5506 3903 
L 3113 3903 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-7279"/>
     <use xlink:href="#MicrosoftYaHei-5f81" x="100"/>
     <use xlink:href="#MicrosoftYaHei-9690" x="200"/>
     <use xlink:href="#MicrosoftYaHei-79c1" x="300"/>
     <use xlink:href="#MicrosoftYaHei-4fdd" x="400"/>
     <use xlink:href="#MicrosoftYaHei-62a4" x="500"/>
    </g>
   </g>
   <g id="text_12">
    <!-- 系统功能扩展与完善 -->
    <g style="fill: #ffffff" transform="translate(343.291888 668.013947) scale(0.14 -0.14)">
     <defs>
      <path id="MicrosoftYaHei-Bold-7cfb" d="M 344 4916 
Q 3406 4903 5900 5159 
L 6025 4472 
Q 4769 4353 3397 4278 
Q 2747 3884 2019 3528 
L 4084 3622 
Q 4550 3884 5038 4178 
L 5625 3591 
Q 3875 2622 1900 1878 
Q 3247 1938 4613 2034 
Q 4372 2272 4156 2466 
L 4738 2934 
Q 5219 2516 6263 1466 
L 5669 922 
Q 5447 1169 5241 1388 
L 3731 1328 
L 3731 -141 
Q 3731 -456 3547 -622 
Q 3363 -788 2984 -802 
Q 2606 -816 1975 -816 
Q 1919 -359 1813 34 
Q 2119 9 2466 9 
Q 2719 9 2800 65 
Q 2881 122 2881 278 
L 2881 1288 
Q 1481 1216 738 1128 
L 519 1903 
Q 1575 2303 2803 2931 
Q 1563 2856 888 2759 
L 656 3522 
Q 1338 3775 2075 4213 
L 481 4166 
Q 438 4522 344 4916 
z
M 4738 1153 
Q 5431 678 6294 -3 
L 5719 -603 
Q 5050 -28 4213 603 
L 4738 1153 
z
M 1569 1091 
L 2200 534 
Q 1494 -128 656 -678 
Q 369 -309 88 -9 
Q 894 447 1569 1091 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-7edf" d="M 5569 1084 
Q 5944 897 6319 772 
Q 6234 -184 6023 -453 
Q 5813 -722 5356 -722 
L 4988 -722 
Q 4269 -722 4263 72 
L 4263 2066 
L 3756 2028 
Q 3722 656 3337 29 
Q 2953 -597 2406 -872 
Q 2144 -575 1788 -219 
Q 950 -303 213 -416 
L 144 416 
Q 763 459 2275 641 
Q 2259 406 2256 138 
Q 2938 591 2966 1950 
Q 2616 1909 2406 1866 
L 2181 2628 
Q 2675 2941 3250 3953 
L 2194 3953 
L 2194 4666 
L 3578 4666 
Q 3475 4916 3344 5191 
L 4275 5359 
Q 4444 5019 4597 4666 
L 6169 4666 
L 6169 3953 
L 4169 3953 
Q 3759 3316 3225 2697 
Q 4009 2725 4956 2781 
Q 4744 3103 4506 3434 
L 5131 3791 
Q 5794 2916 6269 2159 
L 5588 1747 
Q 5481 1938 5366 2131 
L 5063 2116 
L 5063 316 
Q 5063 72 5225 72 
L 5281 72 
Q 5428 72 5475 225 
Q 5522 378 5569 1084 
z
M 1256 1678 
L 2181 1784 
Q 2131 1397 2138 1072 
Q 1125 1022 431 897 
L 238 1591 
Q 597 1950 1113 2722 
Q 625 2688 306 2641 
L 81 3347 
Q 675 4159 1156 5359 
L 1975 5034 
Q 1519 4178 988 3422 
Q 1247 3425 1578 3444 
L 1863 3916 
L 2513 3566 
Q 1863 2541 1256 1678 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-529f" d="M 4038 -753 
Q 3925 -191 3813 184 
Q 4253 166 4625 166 
Q 5056 166 5095 556 
Q 5134 947 5163 3441 
L 4181 3441 
Q 4141 1638 3672 658 
Q 3203 -322 2450 -884 
Q 2119 -534 1756 -203 
Q 3350 803 3353 3441 
L 2650 3441 
L 2650 4266 
L 3375 4266 
Q 3384 4750 3381 5322 
L 4213 5322 
L 4200 4266 
L 6038 4266 
L 5969 713 
Q 5950 72 5887 -129 
Q 5825 -331 5664 -478 
Q 5503 -625 5264 -684 
Q 5025 -744 4038 -753 
z
M 306 41 
L 94 972 
L 950 1194 
L 950 3916 
L 169 3916 
L 169 4772 
L 2525 4772 
L 2525 3916 
L 1806 3916 
L 1806 1428 
L 2650 1672 
Q 2634 1444 2634 1234 
Q 2634 984 2656 759 
Q 1369 397 306 41 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-80fd" d="M 2644 4797 
Q 3100 4172 3569 3447 
Q 3188 3222 2906 3066 
L 2731 3369 
L 319 3197 
L 100 3897 
Q 519 4116 1331 5353 
L 2125 5053 
L 1213 3947 
L 2353 3994 
Q 2200 4238 2044 4472 
L 2644 4797 
z
M 5513 3916 
Q 5981 3697 6288 3597 
Q 6200 3228 6114 2942 
Q 6028 2656 5798 2520 
Q 5569 2384 5200 2384 
L 4556 2384 
Q 3594 2384 3588 3322 
L 3588 5291 
L 4381 5291 
L 4381 4347 
Q 5153 4700 5681 5003 
L 6231 4366 
Q 5553 4056 4381 3616 
L 4381 3422 
Q 4381 3072 4800 3072 
L 5000 3072 
Q 5341 3072 5392 3269 
Q 5444 3466 5513 3916 
z
M 1213 -797 
L 431 -797 
L 431 2978 
L 3106 2978 
L 3106 41 
Q 3106 -325 2972 -525 
Q 2838 -725 2566 -759 
Q 2294 -794 1681 -784 
Q 1594 -422 1481 -41 
Q 1791 -59 1969 -59 
Q 2213 -59 2269 28 
Q 2325 116 2325 228 
L 2325 378 
L 1213 378 
L 1213 -797 
z
M 2325 2303 
L 1213 2303 
L 1213 1947 
L 2325 1947 
L 2325 2303 
z
M 1213 984 
L 2325 984 
L 2325 1341 
L 1213 1341 
L 1213 984 
z
M 5531 953 
Q 5913 797 6313 647 
Q 6194 -78 6081 -287 
Q 5969 -497 5750 -605 
Q 5531 -713 5206 -716 
L 4550 -716 
Q 3588 -716 3588 228 
L 3588 2216 
L 4381 2216 
L 4381 1438 
Q 5072 1781 5675 2147 
L 6225 1491 
Q 5372 1084 4381 672 
L 4381 353 
Q 4381 -28 4738 -28 
L 5000 -28 
Q 5275 -28 5365 90 
Q 5456 209 5531 953 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-6269" d="M 3863 5122 
L 4769 5328 
Q 4906 4994 5097 4491 
L 6225 4491 
L 6225 3722 
L 3388 3722 
L 3388 2322 
Q 3388 372 2606 -878 
Q 2338 -616 1850 -278 
Q 2538 897 2538 2341 
L 2538 4491 
L 4119 4491 
Q 3991 4825 3863 5122 
z
M 150 4222 
L 819 4222 
L 819 5266 
L 1669 5266 
L 1669 4222 
L 2300 4222 
L 2300 3409 
L 1669 3409 
L 1669 2491 
L 2306 2666 
Q 2313 2147 2325 1878 
L 1669 1672 
L 1669 3 
Q 1669 -344 1483 -578 
Q 1297 -813 331 -822 
Q 275 -509 119 34 
Q 375 16 553 16 
Q 709 16 764 84 
Q 819 153 819 316 
L 819 1406 
L 225 1222 
L 94 2072 
L 819 2259 
L 819 3409 
L 150 3409 
L 150 4222 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-5c55" d="M 5988 684 
L 5069 263 
Q 5588 56 6238 -9 
Q 6175 -91 5700 -797 
Q 4450 -456 3703 325 
Q 3688 -28 3694 -422 
Q 2481 -722 2056 -897 
L 1763 -234 
Q 1906 -91 1894 447 
L 1894 1091 
L 1397 1091 
Q 1275 -72 800 -909 
Q 488 -466 169 -116 
Q 606 797 613 3072 
L 613 5066 
L 5913 5066 
L 5913 3278 
L 4900 3278 
L 4900 2803 
L 6000 2803 
L 6000 2191 
L 4900 2191 
L 4900 1716 
L 6175 1716 
L 6175 1091 
L 5697 1091 
L 5988 684 
z
M 5075 3891 
L 5075 4378 
L 1475 4378 
L 1475 3891 
L 5075 3891 
z
M 3138 2803 
L 4100 2803 
L 4100 3278 
L 3138 3278 
L 3138 2803 
z
M 1472 2803 
L 2338 2803 
L 2338 3278 
L 1475 3278 
L 1472 2803 
z
M 3138 1716 
L 4100 1716 
L 4100 2191 
L 3138 2191 
L 3138 1716 
z
M 1441 1716 
L 2338 1716 
L 2338 2191 
L 1459 2191 
Q 1453 1938 1441 1716 
z
M 3147 1091 
L 2731 1091 
L 2731 84 
Q 3128 200 3678 350 
Q 3369 681 3147 1091 
z
M 4388 634 
L 5178 1091 
L 3891 1091 
Q 4113 834 4388 634 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-5b8c" d="M 331 3378 
L 331 4622 
L 2756 4622 
Q 2644 4922 2544 5178 
L 3550 5347 
Q 3675 4997 3794 4622 
L 6069 4622 
L 6069 3378 
L 5163 3378 
L 5163 3828 
L 1238 3828 
L 1238 3378 
L 331 3378 
z
M 5400 941 
Q 5781 809 6275 672 
Q 6206 234 6138 -84 
Q 6006 -753 5081 -753 
L 4331 -753 
Q 3450 -753 3450 116 
L 3450 1178 
L 2713 1178 
Q 2625 516 2350 153 
Q 1956 -397 756 -884 
Q 363 -284 169 -59 
Q 1763 378 1800 1178 
L 275 1178 
L 275 1966 
L 6156 1966 
L 6156 1178 
L 4356 1178 
L 4356 316 
Q 4356 34 4619 34 
L 4981 34 
Q 5319 34 5363 459 
Q 5381 616 5400 941 
z
M 1000 3216 
L 5519 3216 
L 5519 2428 
L 1000 2428 
L 1000 3216 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-5584" d="M 113 1941 
L 1219 1941 
Q 1100 2116 963 2303 
L 1363 2453 
L 188 2453 
L 188 3016 
L 2756 3016 
L 2756 3334 
L 588 3334 
L 588 3847 
L 2756 3847 
L 2756 4159 
L 300 4159 
L 300 4722 
L 1691 4722 
Q 1572 4897 1431 5084 
L 2256 5384 
Q 2472 5069 2681 4722 
L 3791 4722 
Q 4078 5163 4219 5403 
L 5063 5091 
Q 4975 4972 4781 4722 
L 6113 4722 
L 6113 4159 
L 3644 4159 
L 3644 3847 
L 5813 3847 
L 5813 3334 
L 3644 3334 
L 3644 3016 
L 6225 3016 
L 6225 2453 
L 5016 2453 
L 5425 2278 
L 5175 1941 
L 6288 1941 
L 6288 1378 
L 113 1378 
L 113 1941 
z
M 4534 2453 
L 3644 2453 
L 3644 1941 
L 4209 1941 
L 4534 2453 
z
M 2188 1941 
L 2756 1941 
L 2756 2453 
L 1863 2453 
Q 2025 2206 2188 1941 
z
M 713 -834 
L 713 1053 
L 5688 1053 
L 5688 -834 
L 4813 -834 
L 4813 -578 
L 1588 -578 
L 1588 -834 
L 713 -834 
z
M 4813 466 
L 1588 466 
L 1588 9 
L 4813 9 
L 4813 466 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-Bold-7cfb"/>
     <use xlink:href="#MicrosoftYaHei-Bold-7edf" x="100"/>
     <use xlink:href="#MicrosoftYaHei-Bold-529f" x="200"/>
     <use xlink:href="#MicrosoftYaHei-Bold-80fd" x="300"/>
     <use xlink:href="#MicrosoftYaHei-Bold-6269" x="400"/>
     <use xlink:href="#MicrosoftYaHei-Bold-5c55" x="500"/>
     <use xlink:href="#MicrosoftYaHei-Bold-4e0e" x="600"/>
     <use xlink:href="#MicrosoftYaHei-Bold-5b8c" x="700"/>
     <use xlink:href="#MicrosoftYaHei-Bold-5584" x="800"/>
    </g>
   </g>
   <g id="text_13">
    <!-- 分布式协同检测 -->
    <g transform="translate(202.936921 735.685794) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-5206" d="M 900 2559 
L 5056 2559 
Q 5025 1497 4963 484 
Q 4888 -684 3856 -678 
Q 3350 -684 2788 -647 
Q 2750 -384 2700 -116 
Q 3181 -184 3775 -191 
Q 4400 -203 4463 541 
Q 4513 1216 4538 2097 
L 2616 2097 
Q 2541 1053 2181 416 
Q 1706 -422 588 -878 
Q 425 -666 231 -459 
Q 1269 -72 1725 666 
Q 2053 1225 2103 2097 
L 900 2097 
L 900 2559 
z
M 3488 5159 
L 3938 5353 
Q 4625 3678 6350 2847 
Q 6069 2553 5944 2372 
Q 4225 3366 3488 5159 
z
M 50 2766 
Q 1338 3678 2250 5253 
L 2694 5009 
Q 1744 3347 413 2341 
Q 281 2534 50 2766 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-5e03" d="M 3250 3666 
L 3750 3666 
L 3750 2703 
L 5681 2703 
L 5681 541 
Q 5681 -197 5050 -197 
Q 4888 -197 4119 -191 
Q 4081 16 4019 316 
Q 4644 278 4769 278 
Q 5188 266 5188 666 
L 5188 2253 
L 3750 2253 
L 3750 -828 
L 3250 -828 
L 3250 2253 
L 1844 2253 
L 1844 -353 
L 1350 -353 
L 1350 2113 
Q 928 1553 450 1028 
Q 294 1253 94 1447 
Q 1241 2647 1969 3984 
L 200 3984 
L 200 4434 
L 2200 4434 
Q 2419 4884 2594 5353 
L 3088 5153 
Q 2934 4788 2766 4434 
L 6250 4434 
L 6250 3984 
L 2538 3984 
Q 2188 3325 1772 2703 
L 3250 2703 
L 3250 3666 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-5f0f" d="M 94 4122 
L 3569 4122 
Q 3544 4700 3531 5347 
L 4063 5347 
Q 4059 4728 4081 4122 
L 6231 4122 
L 6231 3653 
L 4103 3653 
Q 4225 503 5156 -172 
Q 5494 -447 5631 -22 
Q 5725 303 5800 941 
Q 6094 834 6306 778 
Q 6188 47 6050 -403 
Q 5900 -834 5475 -834 
Q 5044 -834 4619 -397 
Q 3725 553 3597 3653 
L 94 3653 
L 94 4122 
z
M 119 159 
Q 825 244 1569 344 
L 1569 2097 
L 350 2097 
L 350 2566 
L 3288 2566 
L 3288 2097 
L 2081 2097 
L 2081 416 
Q 2809 519 3575 634 
Q 3563 391 3563 128 
Q 1681 -141 244 -366 
L 119 159 
z
M 4544 4978 
L 4844 5297 
Q 5294 4972 5756 4559 
L 5400 4191 
Q 5000 4597 4544 4978 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-534f" d="M 2206 4022 
L 3291 4022 
Q 3313 4659 3313 5278 
L 3800 5278 
Q 3794 4697 3772 4022 
L 5294 4022 
Q 5231 1716 5150 172 
Q 5113 -691 4281 -691 
Q 4075 -691 3594 -678 
Q 3550 -391 3488 -134 
Q 3894 -191 4200 -191 
Q 4650 -197 4675 291 
Q 4750 1784 4794 3534 
L 3756 3534 
Q 3722 2138 3538 1322 
Q 3219 84 2225 -778 
Q 2069 -603 1863 -409 
Q 2756 378 3056 1453 
Q 3250 2184 3278 3534 
L 2206 3534 
L 2206 4022 
z
M 94 3484 
L 775 3484 
L 775 5272 
L 1256 5272 
L 1256 3484 
L 1863 3484 
L 1863 3028 
L 1256 3028 
L 1256 -803 
L 775 -803 
L 775 3028 
L 94 3028 
L 94 3484 
z
M 2394 2916 
L 2813 2759 
Q 2500 1934 2088 997 
Q 1894 1103 1669 1191 
Q 2075 1978 2394 2916 
z
M 5331 2791 
L 5750 2922 
Q 6063 2047 6319 1278 
L 5863 1122 
Q 5600 2053 5331 2791 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-540c" d="M 1013 -772 
L 531 -772 
L 531 4959 
L 5856 4959 
L 5856 -47 
Q 5856 -741 5125 -741 
Q 4669 -741 4088 -716 
Q 4056 -497 4000 -216 
Q 4619 -272 5025 -272 
Q 5375 -272 5375 78 
L 5375 4497 
L 1013 4497 
L 1013 -772 
z
M 1875 2566 
L 4506 2566 
L 4506 597 
L 2338 597 
L 2338 272 
L 1875 272 
L 1875 2566 
z
M 4044 2116 
L 2338 2116 
L 2338 1047 
L 4044 1047 
L 4044 2116 
z
M 1363 3734 
L 5025 3734 
L 5025 3284 
L 1363 3284 
L 1363 3734 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-68c0" d="M 81 1378 
Q 675 2328 988 3597 
L 144 3597 
L 144 4009 
L 1044 4009 
L 1044 5266 
L 1481 5266 
L 1481 4009 
L 2319 4009 
L 2319 3597 
L 1481 3597 
L 1481 2672 
L 1756 2872 
Q 2094 2459 2350 2097 
L 2006 1828 
Q 1788 2197 1481 2597 
L 1481 -841 
L 1044 -841 
L 1044 2647 
Q 725 1534 281 828 
Q 206 1103 81 1378 
z
M 2163 -122 
L 4544 -122 
Q 4881 791 5244 2203 
L 5750 2059 
Q 5331 741 5013 -122 
L 6188 -122 
L 6188 -566 
L 2163 -566 
L 2163 -122 
z
M 4538 5216 
L 4456 5091 
Q 5119 3891 6344 3166 
Q 6175 2978 6000 2741 
Q 4781 3603 4181 4672 
Q 3438 3466 2475 2666 
Q 2325 2866 2119 3059 
Q 3281 3941 3944 5216 
L 4538 5216 
z
M 2900 2972 
L 5463 2972 
L 5463 2534 
L 2900 2534 
L 2900 2972 
z
M 3656 2197 
L 4094 2303 
Q 4338 1441 4500 578 
L 4031 459 
Q 3875 1441 3656 2197 
z
M 2469 1947 
L 2900 2072 
Q 3138 1378 3388 378 
L 2925 241 
Q 2719 1172 2469 1947 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-6d4b" d="M 875 1753 
Q 1175 1597 1369 1522 
Q 950 -41 763 -784 
L 250 -547 
Q 619 497 875 1753 
z
M 638 5166 
Q 1019 4847 1588 4303 
Q 1381 4103 1213 3928 
Q 756 4434 313 4853 
L 638 5166 
z
M 356 3559 
Q 881 3134 1350 2716 
Q 1188 2547 988 2322 
Q 425 2878 50 3216 
L 356 3559 
z
M 5575 5284 
L 6031 5284 
L 6031 -34 
Q 6031 -753 5338 -753 
Q 5038 -753 4625 -741 
Q 4600 -509 4538 -228 
Q 4938 -272 5219 -272 
Q 5575 -272 5575 84 
L 5575 5284 
z
M 5038 634 
L 4600 634 
L 4600 4778 
L 5038 4778 
L 5038 634 
z
M 1706 4941 
L 4063 4941 
L 4063 991 
L 3613 991 
L 3613 4503 
L 2156 4503 
L 2156 991 
L 1706 991 
L 1706 4941 
z
M 2681 3966 
L 3138 3966 
L 3138 2416 
Q 3138 1466 2963 891 
Q 3775 266 4375 -234 
L 4038 -616 
Q 3538 -141 2800 472 
Q 2413 -297 1406 -884 
Q 1269 -684 1100 -484 
Q 2031 53 2375 709 
Q 2681 1278 2681 2403 
L 2681 3966 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-5206"/>
     <use xlink:href="#MicrosoftYaHei-5e03" x="100"/>
     <use xlink:href="#MicrosoftYaHei-5f0f" x="200"/>
     <use xlink:href="#MicrosoftYaHei-534f" x="300"/>
     <use xlink:href="#MicrosoftYaHei-540c" x="400"/>
     <use xlink:href="#MicrosoftYaHei-68c0" x="500"/>
     <use xlink:href="#MicrosoftYaHei-6d4b" x="600"/>
    </g>
   </g>
   <g id="text_14">
    <!-- 自适应响应机制 -->
    <g transform="translate(237.238495 783.979394) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-9002" d="M 2425 2441 
L 3825 2441 
L 3825 3222 
L 1875 3222 
L 1875 3672 
L 3825 3672 
L 3825 4491 
Q 3050 4444 2225 4397 
Q 2194 4597 2138 4841 
Q 4288 4934 5844 5097 
L 5938 4641 
Q 5153 4581 4313 4525 
L 4313 3672 
L 6269 3672 
L 6269 3222 
L 4313 3222 
L 4313 2441 
L 5725 2441 
L 5725 216 
L 5250 216 
L 5250 503 
L 2900 503 
L 2900 216 
L 2425 216 
L 2425 2441 
z
M 2663 -203 
L 4444 -228 
L 6288 -178 
Q 6194 -434 6113 -672 
L 4344 -672 
L 2581 -647 
Q 1513 -634 1069 84 
Q 813 -153 319 -753 
L 63 -234 
Q 631 313 875 541 
L 875 2641 
L 131 2641 
L 131 3103 
L 1356 3103 
L 1356 403 
Q 1744 -191 2663 -203 
z
M 5250 2016 
L 2900 2016 
L 2900 928 
L 5250 928 
L 5250 2016 
z
M 856 5203 
Q 1325 4603 1731 4022 
L 1288 3709 
Q 894 4334 456 4934 
L 856 5203 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-5e94" d="M 2969 5134 
L 3425 5378 
Q 3694 5034 3938 4634 
L 3634 4459 
L 6169 4459 
L 6169 3984 
L 1300 3984 
L 1300 2453 
Q 1300 241 388 -853 
Q 238 -647 25 -422 
Q 775 416 775 2441 
L 775 4459 
L 3419 4459 
Q 3216 4803 2969 5134 
z
M 1263 -9 
L 4153 -9 
Q 4641 1238 5188 3403 
L 5731 3253 
Q 5147 1191 4694 -9 
L 6238 -9 
L 6238 -484 
L 1263 -484 
L 1263 -9 
z
M 1600 3059 
L 2075 3222 
Q 2463 2084 2825 672 
L 2294 509 
Q 2006 1747 1600 3059 
z
M 3106 3372 
L 3588 3509 
Q 3913 2384 4163 991 
L 3613 847 
Q 3431 2084 3106 3372 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-54cd" d="M 2425 4441 
L 3525 4441 
Q 3700 4884 3838 5316 
L 4344 5197 
Q 4181 4803 4019 4441 
L 5875 4441 
L 5875 -59 
Q 5875 -766 5225 -772 
Q 4900 -778 4444 -772 
Q 4413 -547 4356 -284 
Q 4781 -322 5100 -322 
Q 5438 -322 5438 72 
L 5438 4016 
L 2863 4016 
L 2863 -859 
L 2425 -859 
L 2425 4441 
z
M 413 4484 
L 1925 4484 
L 1925 166 
L 1488 166 
L 1488 641 
L 850 641 
L 850 72 
L 413 72 
L 413 4484 
z
M 3325 3191 
L 4975 3191 
L 4975 741 
L 3750 741 
L 3750 466 
L 3325 466 
L 3325 3191 
z
M 1488 4053 
L 850 4053 
L 850 1072 
L 1488 1072 
L 1488 4053 
z
M 4550 2803 
L 3750 2803 
L 3750 1128 
L 4550 1128 
L 4550 2803 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-673a" d="M 3375 4434 
L 3375 2366 
Q 3375 128 2256 -878 
Q 2081 -666 1888 -453 
Q 2875 416 2875 2253 
L 2875 4909 
L 5200 4909 
L 5200 209 
Q 5200 -103 5419 -103 
L 5563 -103 
Q 5763 -103 5819 247 
Q 5881 728 5906 1284 
Q 6125 1178 6381 1084 
Q 6325 466 6269 72 
Q 6169 -591 5631 -591 
L 5300 -591 
Q 4700 -591 4700 122 
L 4700 4434 
L 3375 4434 
z
M 81 1403 
Q 781 2447 1078 3653 
L 144 3653 
L 144 4078 
L 1119 4078 
L 1119 5309 
L 1569 5309 
L 1569 4078 
L 2444 4078 
L 2444 3653 
L 1569 3653 
L 1569 2616 
L 1819 2866 
Q 2300 2428 2575 2153 
L 2238 1809 
Q 1853 2234 1569 2516 
L 1569 -784 
L 1119 -784 
L 1119 2566 
Q 784 1506 288 853 
Q 188 1134 81 1403 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-5236" d="M 1906 5266 
L 2394 5266 
L 2394 4309 
L 3750 4309 
L 3750 3872 
L 2394 3872 
L 2394 3034 
L 4025 3034 
L 4025 2597 
L 2394 2597 
L 2394 1897 
L 3763 1897 
L 3763 409 
Q 3763 -266 3150 -272 
Q 2956 -278 2631 -272 
Q 2600 -72 2538 178 
Q 2844 159 3038 159 
Q 3300 159 3300 472 
L 3300 1472 
L 2394 1472 
L 2394 -847 
L 1906 -847 
L 1906 1472 
L 1044 1472 
L 1044 -378 
L 581 -378 
L 581 1897 
L 1906 1897 
L 1906 2597 
L 113 2597 
L 113 3034 
L 1906 3034 
L 1906 3872 
L 981 3872 
Q 822 3513 619 3191 
Q 413 3309 194 3428 
Q 688 4172 881 5041 
L 1344 4922 
Q 1263 4603 1159 4309 
L 1906 4309 
L 1906 5266 
z
M 5469 5272 
L 5969 5272 
L 5969 122 
Q 5969 -722 5225 -722 
Q 4981 -722 4388 -716 
Q 4350 -472 4288 -172 
Q 4813 -222 5075 -222 
Q 5469 -222 5469 222 
L 5469 5272 
z
M 4750 822 
L 4269 822 
L 4269 4672 
L 4750 4672 
L 4750 822 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-81ea"/>
     <use xlink:href="#MicrosoftYaHei-9002" x="100"/>
     <use xlink:href="#MicrosoftYaHei-5e94" x="200"/>
     <use xlink:href="#MicrosoftYaHei-54cd" x="300"/>
     <use xlink:href="#MicrosoftYaHei-5e94" x="400"/>
     <use xlink:href="#MicrosoftYaHei-673a" x="500"/>
     <use xlink:href="#MicrosoftYaHei-5236" x="600"/>
    </g>
   </g>
   <g id="text_15">
    <!-- 多层次安全分析 -->
    <g transform="translate(286.864584 818.219418) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-5c42" d="M 394 -847 
Q 213 -603 50 -409 
Q 825 678 825 2709 
L 825 5022 
L 5963 5022 
L 5963 3053 
L 5488 3053 
L 5488 3284 
L 1300 3284 
L 1300 2609 
Q 1300 497 394 -847 
z
M 4294 559 
L 4631 853 
Q 5556 53 6100 -528 
L 5713 -872 
Q 5522 -656 5328 -453 
Q 3425 -463 2219 -553 
Q 2044 -566 1775 -634 
L 1563 -172 
Q 1938 -28 2144 147 
Q 2500 497 2906 1034 
L 1375 1034 
L 1375 1472 
L 6288 1472 
L 6288 1034 
L 3550 1034 
Q 3022 388 2569 -66 
Q 3741 -59 4922 -34 
Q 4613 272 4294 559 
z
M 1300 4584 
L 1300 3722 
L 5488 3722 
L 5488 4584 
L 1300 4584 
z
M 1819 2603 
L 5731 2603 
L 5731 2166 
L 1819 2166 
L 1819 2603 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-6b21" d="M 4119 3472 
Q 4113 3159 4091 2856 
Q 4509 728 6319 -284 
Q 6063 -559 5913 -772 
Q 4344 331 3900 1944 
Q 3491 200 1650 -866 
Q 1525 -672 1306 -422 
Q 3225 591 3525 2428 
Q 3613 2878 3619 3472 
L 4119 3472 
z
M 4944 2647 
Q 5225 3166 5450 3766 
L 2991 3766 
Q 2719 3144 2388 2572 
Q 2163 2734 1938 2841 
Q 2669 4034 3000 5309 
L 3506 5166 
Q 3356 4675 3175 4209 
L 5988 4209 
L 5988 3766 
Q 5731 3072 5425 2466 
Q 5213 2572 4944 2647 
z
M 1388 2622 
Q 1675 2403 1875 2284 
Q 1075 859 575 -178 
L 69 153 
Q 713 1128 1388 2622 
z
M 644 4916 
Q 1150 4409 1781 3672 
L 1375 3297 
Q 769 4097 281 4591 
L 644 4916 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-5b89" d="M 325 -322 
Q 1894 -81 2888 478 
Q 2072 859 1163 1266 
Q 1459 1709 1747 2191 
L 106 2191 
L 106 2653 
L 2019 2653 
Q 2334 3206 2644 3803 
L 3138 3597 
Q 2863 3113 2597 2653 
L 6306 2653 
L 6306 2191 
L 4966 2191 
Q 4653 1244 3888 572 
Q 4872 128 5850 -328 
L 5544 -828 
Q 4591 -328 3434 219 
Q 2347 -513 588 -834 
Q 475 -597 325 -322 
z
M 413 4397 
L 3228 4397 
Q 2991 4741 2681 5103 
L 3100 5384 
Q 3481 4972 3744 4591 
L 3456 4397 
L 6013 4397 
L 6013 3209 
L 5506 3209 
L 5506 3934 
L 913 3934 
L 913 3209 
L 413 3209 
L 413 4397 
z
M 1881 1453 
Q 2631 1131 3375 800 
Q 4122 1369 4422 2191 
L 2325 2191 
Q 2100 1813 1881 1453 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-5168" d="M 369 -103 
L 2950 -103 
L 2950 984 
L 925 984 
L 925 1441 
L 2950 1441 
L 2950 2403 
L 1156 2403 
L 1156 2859 
L 5269 2859 
L 5269 2403 
L 3469 2403 
L 3469 1441 
L 5500 1441 
L 5500 984 
L 3469 984 
L 3469 -103 
L 6044 -103 
L 6044 -566 
L 369 -566 
L 369 -103 
z
M 3656 5234 
L 3531 5078 
Q 4581 3703 6319 3009 
Q 6125 2734 5988 2509 
Q 4338 3322 3219 4691 
Q 2263 3453 394 2428 
Q 269 2641 81 2897 
Q 2013 3866 3000 5234 
L 3656 5234 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-6790" d="M 2775 4797 
Q 4319 4822 5863 5066 
L 6031 4616 
Q 4713 4422 3256 4359 
L 3256 2966 
L 6306 2966 
L 6306 2509 
L 5131 2509 
L 5131 -816 
L 4650 -816 
L 4650 2509 
L 3256 2509 
L 3256 2272 
Q 3256 241 2225 -853 
Q 2069 -628 1900 -441 
Q 2775 497 2775 2278 
L 2775 4797 
z
M 88 1391 
Q 738 2341 1069 3584 
L 175 3584 
L 175 4016 
L 1081 4016 
L 1081 5259 
L 1538 5259 
L 1538 4016 
L 2400 4016 
L 2400 3584 
L 1538 3584 
L 1538 2872 
L 1769 3053 
Q 2338 2384 2594 1984 
L 2206 1697 
Q 1913 2159 1538 2659 
L 1538 -816 
L 1081 -816 
L 1081 2397 
Q 763 1459 306 834 
Q 231 1053 88 1391 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-591a"/>
     <use xlink:href="#MicrosoftYaHei-5c42" x="100"/>
     <use xlink:href="#MicrosoftYaHei-6b21" x="200"/>
     <use xlink:href="#MicrosoftYaHei-5b89" x="300"/>
     <use xlink:href="#MicrosoftYaHei-5168" x="400"/>
     <use xlink:href="#MicrosoftYaHei-5206" x="500"/>
     <use xlink:href="#MicrosoftYaHei-6790" x="600"/>
    </g>
   </g>
   <g id="text_16">
    <!-- 安全知识图谱 -->
    <g transform="translate(351.829548 834.276013) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-77e5" d="M 3375 4641 
L 6038 4641 
L 6038 -684 
L 5544 -684 
L 5544 -53 
L 3869 -53 
L 3869 -684 
L 3375 -684 
L 3375 4641 
z
M 138 2528 
L 1419 2528 
Q 1456 3103 1463 3978 
L 931 3978 
Q 744 3447 544 3028 
Q 263 3147 69 3216 
Q 556 4147 844 5341 
L 1331 5228 
Q 1213 4803 1088 4428 
L 2994 4428 
L 2994 3978 
L 1963 3978 
Q 1956 3116 1925 2528 
L 3119 2528 
L 3119 2078 
L 1888 2078 
Q 1869 1916 1850 1772 
Q 2644 872 3206 153 
L 2781 -166 
Q 2181 634 1713 1184 
Q 1356 -16 381 -834 
Q 219 -616 31 -403 
Q 1206 547 1381 2078 
L 138 2078 
L 138 2528 
z
M 5544 4178 
L 3869 4178 
L 3869 397 
L 5544 397 
L 5544 4178 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-8bc6" d="M 2431 4884 
L 5875 4884 
L 5875 1597 
L 5394 1597 
L 5394 1972 
L 2913 1972 
L 2913 1597 
L 2431 1597 
L 2431 4884 
z
M 5394 4447 
L 2913 4447 
L 2913 2409 
L 5394 2409 
L 5394 4447 
z
M 2313 1309 
Q 2344 1047 2394 791 
Q 1831 372 1325 -41 
Q 1200 -153 1050 -328 
L 681 59 
Q 881 259 881 609 
L 881 2772 
L 88 2772 
L 88 3222 
L 1363 3222 
L 1363 566 
Q 1875 947 2313 1309 
z
M 4469 997 
L 4875 1297 
Q 5619 503 6381 -422 
L 5931 -759 
Q 5225 153 4469 997 
z
M 3306 1322 
L 3725 1022 
Q 2981 116 2063 -784 
Q 1906 -634 1681 -447 
Q 2544 334 3306 1322 
z
M 1025 5203 
Q 1638 4578 2000 4172 
L 1569 3816 
Q 1088 4434 656 4891 
L 1025 5203 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-56fe" d="M 431 5072 
L 5944 5072 
L 5944 -809 
L 5475 -809 
L 5475 -509 
L 900 -509 
L 900 -809 
L 431 -809 
L 431 5072 
z
M 5475 4622 
L 900 4622 
L 900 -59 
L 5475 -59 
L 5475 4622 
z
M 1113 3091 
Q 1969 3709 2519 4547 
L 2931 4353 
Q 2816 4181 2681 4016 
L 4894 4016 
L 4894 3647 
Q 4434 3091 3722 2644 
Q 4381 2422 5338 2297 
Q 5194 2016 5131 1828 
Q 4022 2081 3256 2400 
Q 2491 2056 1225 1728 
Q 1138 1934 1013 2128 
Q 2109 2353 2825 2644 
Q 2384 2947 2066 3341 
Q 1769 3056 1419 2784 
Q 1294 2916 1113 3091 
z
M 1563 672 
L 1694 1103 
Q 3256 897 4700 622 
L 4531 147 
Q 3150 453 1563 672 
z
M 4275 3628 
L 2347 3628 
Q 2338 3616 2328 3606 
Q 2691 3141 3244 2841 
Q 3891 3188 4275 3628 
z
M 2219 1503 
L 2344 1922 
Q 3263 1784 4275 1609 
L 4144 1172 
Q 3119 1378 2219 1503 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-8c31" d="M 2300 1891 
L 5600 1891 
L 5600 -866 
L 5150 -866 
L 5150 -559 
L 2750 -559 
L 2750 -866 
L 2300 -866 
L 2300 1891 
z
M 5369 5066 
Q 5100 4722 4863 4466 
L 6069 4466 
L 6069 4066 
L 4763 4066 
L 4763 2778 
L 6300 2778 
L 6300 2378 
L 1625 2378 
L 1625 2778 
L 3150 2778 
L 3150 4066 
L 1881 4066 
L 1881 4466 
L 3069 4466 
Q 2838 4803 2581 5078 
L 2919 5328 
Q 3231 5003 3469 4672 
L 3194 4466 
L 4713 4466 
L 4475 4653 
Q 4781 4984 5006 5309 
L 5369 5066 
z
M 1944 916 
Q 1950 766 2038 347 
Q 1425 -197 956 -697 
L 588 -272 
Q 794 -34 794 291 
L 794 2691 
L 100 2691 
L 100 3141 
L 1263 3141 
L 1263 253 
Q 1538 503 1944 916 
z
M 2750 -159 
L 5150 -159 
L 5150 478 
L 2750 478 
L 2750 -159 
z
M 5150 1491 
L 2750 1491 
L 2750 866 
L 5150 866 
L 5150 1491 
z
M 3575 2778 
L 4338 2778 
L 4338 4066 
L 3575 4066 
L 3575 2778 
z
M 656 5191 
Q 1100 4753 1544 4253 
L 1150 3916 
Q 763 4434 313 4897 
L 656 5191 
z
M 5975 3634 
Q 5619 3234 5294 2903 
L 4988 3203 
Q 5331 3528 5631 3903 
L 5975 3634 
z
M 1988 3634 
L 2306 3903 
Q 2606 3603 2950 3203 
L 2606 2916 
Q 2288 3322 1988 3634 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-5b89"/>
     <use xlink:href="#MicrosoftYaHei-5168" x="100"/>
     <use xlink:href="#MicrosoftYaHei-77e5" x="200"/>
     <use xlink:href="#MicrosoftYaHei-8bc6" x="300"/>
     <use xlink:href="#MicrosoftYaHei-56fe" x="400"/>
     <use xlink:href="#MicrosoftYaHei-8c31" x="500"/>
    </g>
   </g>
   <g id="text_17">
    <!-- 跨领域应用探索 -->
    <g style="fill: #ffffff" transform="translate(689.508112 668.013947) scale(0.14 -0.14)">
     <defs>
      <path id="MicrosoftYaHei-Bold-8de8" d="M 194 -659 
L 106 222 
Q 206 234 300 244 
L 300 2416 
L 888 2416 
L 888 316 
L 1219 356 
L 1219 2816 
L 369 2816 
L 369 4966 
L 2419 4966 
L 2419 3153 
Q 2919 3519 3266 3991 
L 2600 3991 
L 2600 4691 
L 3669 4691 
Q 3806 4997 3894 5341 
L 4719 5278 
Q 4631 4972 4519 4691 
L 6169 4691 
L 6169 3991 
L 5181 3991 
Q 5647 3406 6344 3116 
Q 6150 2766 6000 2434 
Q 5622 2628 5313 2869 
L 5313 2641 
L 3113 2641 
L 3113 2753 
Q 2853 2541 2556 2359 
Q 2422 2641 2319 2816 
L 1950 2816 
L 1950 2066 
L 2413 2066 
L 2413 1341 
L 1950 1341 
L 1950 450 
Q 2406 509 2638 547 
Q 2625 391 2613 -291 
Q 1756 -403 194 -659 
z
M 1100 3516 
L 1688 3516 
L 1688 4166 
L 1100 4166 
L 1100 3516 
z
M 4388 3991 
L 4175 3991 
Q 3956 3616 3675 3297 
L 4859 3297 
Q 4578 3613 4388 3991 
z
M 2944 578 
L 3194 1641 
L 2494 1641 
L 2494 2297 
L 6094 2297 
L 6094 1641 
L 3984 1641 
L 3888 1234 
L 5819 1234 
Q 5800 72 5728 -189 
Q 5656 -450 5414 -597 
Q 5172 -744 4819 -755 
Q 4466 -766 3513 -766 
Q 3413 -316 3325 -28 
Q 4050 -53 4478 -53 
Q 4766 -53 4858 5 
Q 4950 63 4963 234 
Q 4969 259 4988 578 
L 2944 578 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-9886" d="M 2663 5016 
L 6188 5016 
L 6188 4241 
L 4813 4241 
L 4713 3791 
L 6075 3791 
L 6075 703 
L 5238 703 
L 5238 3041 
L 3700 3041 
L 3700 703 
L 2863 703 
L 2863 3478 
L 2388 3141 
Q 1988 3803 1563 4353 
Q 1325 3922 1056 3491 
L 1581 3784 
Q 1875 3316 2100 2884 
L 1438 2566 
Q 1275 2916 1000 3409 
Q 756 3016 463 2628 
Q 294 3047 81 3428 
Q 669 4153 1194 5228 
L 2013 5228 
L 1931 5047 
Q 2488 4384 2906 3791 
L 3944 3791 
L 4025 4241 
L 2663 4241 
L 2663 5016 
z
M 4069 2766 
L 4875 2766 
L 4875 2191 
Q 4875 1353 4775 866 
Q 5613 309 6331 -253 
L 5775 -897 
Q 5188 -372 4469 153 
Q 4063 -397 3063 -897 
Q 2663 -391 2488 -197 
Q 3581 247 3869 822 
Q 4069 1222 4069 2291 
L 4069 2766 
z
M 231 872 
L 738 1341 
L 1263 816 
Q 1500 1241 1688 1641 
L 263 1641 
L 263 2416 
L 2625 2416 
L 2625 1753 
Q 2231 966 1838 222 
Q 2106 -53 2388 -359 
L 1756 -891 
Q 713 341 231 872 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-57df" d="M 1938 4428 
L 3978 4428 
L 3956 5316 
L 4781 5316 
L 4806 4428 
L 5372 4428 
L 5000 4934 
L 5581 5284 
Q 5900 4916 6081 4678 
L 5669 4428 
L 6275 4428 
L 6275 3641 
L 4834 3641 
Q 4878 2544 4928 1866 
Q 5256 2534 5413 3278 
L 6113 3084 
Q 5784 1803 5094 734 
Q 5284 66 5463 66 
Q 5606 66 5619 1072 
Q 6056 941 6294 891 
Q 6294 -841 5475 -841 
Q 4856 -841 4506 -59 
Q 4181 -447 3800 -803 
Q 3594 -534 3294 -216 
Q 3297 -209 3303 -206 
L 1869 -416 
L 1819 -19 
Q 931 -147 113 -297 
L 88 541 
L 575 597 
L 575 2897 
L 138 2897 
L 138 3684 
L 575 3684 
L 575 5322 
L 1394 5322 
L 1394 3684 
L 1744 3684 
L 1744 2897 
L 1394 2897 
L 1394 709 
L 1844 778 
Q 1831 678 1834 419 
Q 2944 525 3856 641 
L 3875 347 
Q 4075 559 4253 784 
Q 4088 1681 4006 3641 
L 1938 3641 
L 1938 4428 
z
M 1950 3116 
L 3738 3116 
L 3738 947 
L 1950 947 
L 1950 3116 
z
M 3075 2528 
L 2613 2528 
L 2613 1534 
L 3075 1534 
L 3075 2528 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-5e94" d="M 1438 2491 
Q 1438 119 794 -866 
Q 506 -597 88 -266 
Q 619 622 613 2603 
L 613 4566 
L 3009 4566 
Q 2894 4853 2781 5116 
L 3788 5353 
Q 3928 5016 4103 4566 
L 6225 4566 
L 6225 3797 
L 1438 3797 
L 1438 2491 
z
M 3013 3309 
L 3794 3541 
Q 4125 2678 4513 1316 
L 3669 1084 
Q 3300 2416 3013 3309 
z
M 1325 316 
L 4300 316 
Q 4681 1541 5200 3453 
L 6069 3228 
Q 5744 2006 5184 316 
L 6250 316 
L 6250 -553 
L 1325 -553 
L 1325 316 
z
M 1475 2984 
L 2225 3303 
Q 2775 2103 3244 828 
L 2406 516 
Q 2006 1684 1475 2984 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-7528" d="M 5925 134 
Q 5925 -263 5744 -480 
Q 5563 -697 5189 -736 
Q 4816 -775 4206 -766 
Q 4100 -284 3956 141 
Q 4600 103 4656 103 
Q 5050 103 5050 484 
L 5050 1053 
L 3750 1053 
L 3750 -697 
L 2869 -697 
L 2869 1053 
L 1516 1053 
Q 1419 -325 988 -872 
Q 588 -584 181 -347 
Q 688 341 669 2097 
L 669 5041 
L 5925 5041 
L 5925 134 
z
M 1544 3441 
L 2869 3441 
L 2869 4216 
L 1544 4216 
L 1544 3441 
z
M 5050 4216 
L 3750 4216 
L 3750 3441 
L 5050 3441 
L 5050 4216 
z
M 1544 1859 
L 2869 1859 
L 2869 2634 
L 1544 2634 
L 1544 1859 
z
M 3750 1859 
L 5050 1859 
L 5050 2634 
L 3750 2634 
L 3750 1859 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-63a2" d="M 175 4147 
L 800 4147 
L 800 5303 
L 1625 5303 
L 1625 4147 
L 2075 4147 
L 2075 3347 
L 1625 3347 
L 1625 2413 
L 2025 2528 
Q 2016 2322 2016 2147 
Q 2016 1881 2038 1678 
Q 1828 1625 1625 1569 
L 1625 -3 
Q 1625 -297 1506 -486 
Q 1388 -675 1166 -742 
Q 944 -809 275 -809 
Q 219 -372 113 16 
Q 375 -3 569 -3 
Q 800 -3 800 291 
L 800 1341 
L 225 1172 
L 81 2059 
Q 438 2125 800 2206 
L 800 3347 
L 175 3347 
L 175 4147 
z
M 2213 3753 
L 2213 4997 
L 6131 4997 
L 6131 3753 
L 5294 3753 
L 5294 4234 
L 3050 4234 
L 3050 3753 
L 2213 3753 
z
M 4838 4072 
L 6175 3066 
L 5625 2453 
L 4338 3541 
L 4838 4072 
z
M 3456 4059 
L 4044 3534 
Q 3456 2991 2744 2409 
L 2231 2997 
Q 2950 3547 3456 4059 
z
M 2188 2328 
L 3756 2328 
L 3756 3041 
L 4563 3041 
L 4563 2328 
L 6256 2328 
L 6256 1584 
L 4900 1584 
Q 5563 672 6344 247 
Q 6088 -166 5900 -503 
Q 5097 113 4563 1063 
L 4563 -834 
L 3756 -834 
L 3756 1075 
Q 3138 150 2275 -547 
Q 1931 -147 1688 72 
Q 2666 688 3344 1584 
L 2188 1584 
L 2188 2328 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-7d22" d="M 281 2522 
L 281 3828 
L 2775 3828 
L 2775 4253 
L 425 4253 
L 425 4903 
L 2775 4903 
L 2775 5341 
L 3625 5341 
L 3625 4903 
L 5975 4903 
L 5975 4253 
L 3625 4253 
L 3625 3828 
L 6131 3828 
L 6131 2534 
L 5269 2534 
L 5269 3184 
L 3425 3184 
Q 2941 2906 2394 2628 
Q 3031 2628 3528 2638 
Q 3963 2878 4369 3147 
L 4981 2609 
Q 4050 2097 2150 1403 
L 4478 1500 
L 4038 1847 
L 4569 2303 
Q 5431 1691 6056 1197 
L 5500 647 
Q 5322 803 5144 953 
L 3681 897 
L 3681 -134 
Q 3681 -406 3543 -568 
Q 3406 -731 3167 -776 
Q 2928 -822 2019 -822 
Q 1950 -353 1856 -59 
Q 2200 -94 2438 -94 
Q 2653 -94 2742 -30 
Q 2831 34 2831 184 
L 2831 859 
Q 1400 797 875 747 
L 625 1422 
Q 1572 1713 2422 2091 
Q 1663 2063 1319 2022 
L 1141 2522 
L 281 2522 
z
M 1144 2600 
Q 1659 2809 2241 3184 
L 1144 3184 
L 1144 2600 
z
M 1681 797 
L 2181 222 
Q 1325 -378 506 -772 
Q 344 -484 81 -103 
Q 894 234 1681 797 
z
M 4475 791 
Q 5388 397 6313 -72 
L 5894 -759 
Q 4906 -216 4069 147 
L 4475 791 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-Bold-8de8"/>
     <use xlink:href="#MicrosoftYaHei-Bold-9886" x="100"/>
     <use xlink:href="#MicrosoftYaHei-Bold-57df" x="200"/>
     <use xlink:href="#MicrosoftYaHei-Bold-5e94" x="300"/>
     <use xlink:href="#MicrosoftYaHei-Bold-7528" x="400"/>
     <use xlink:href="#MicrosoftYaHei-Bold-63a2" x="500"/>
     <use xlink:href="#MicrosoftYaHei-Bold-7d22" x="600"/>
    </g>
   </g>
   <g id="text_18">
    <!-- 物联网安全 -->
    <g transform="translate(726.970452 834.276013) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-7269" d="M 2131 2866 
Q 2750 3816 3263 5341 
L 3719 5203 
Q 3541 4706 3363 4278 
L 6075 4278 
Q 5950 1759 5869 422 
Q 5800 -622 4869 -622 
Q 4519 -622 4044 -603 
Q 4013 -372 3956 -72 
Q 4500 -134 4819 -134 
Q 5325 -134 5375 441 
Q 5488 1759 5575 3834 
L 5097 3834 
Q 4934 2241 4569 1322 
Q 4069 134 2869 -716 
Q 2731 -522 2550 -334 
Q 3719 491 4169 1559 
Q 4531 2438 4634 3834 
L 4078 3834 
Q 3909 2641 3575 1916 
Q 3163 1097 2444 447 
Q 2288 647 2119 816 
Q 2831 1434 3175 2116 
Q 3509 2809 3619 3834 
L 3169 3834 
Q 2850 3125 2531 2628 
Q 2375 2734 2131 2866 
z
M 94 1584 
Q 669 1775 1288 1991 
L 1288 3466 
L 722 3466 
Q 597 2872 419 2378 
Q 219 2522 13 2653 
Q 363 3509 456 4709 
L 906 4634 
Q 863 4244 800 3884 
L 1288 3884 
L 1288 5291 
L 1756 5291 
L 1756 3884 
L 2375 3884 
L 2375 3466 
L 1756 3466 
L 1756 2156 
Q 2063 2269 2381 2384 
Q 2413 2109 2450 1909 
Q 2141 1797 1756 1653 
L 1756 -834 
L 1288 -834 
L 1288 1475 
Q 788 1288 188 1053 
L 94 1584 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-8054" d="M 81 553 
Q 319 591 556 631 
L 556 4428 
L 150 4428 
L 150 4866 
L 2850 4866 
L 2850 4428 
L 2381 4428 
L 2381 953 
Q 2666 1006 2950 1059 
Q 2963 803 2994 622 
Q 2697 563 2381 500 
L 2381 -822 
L 1925 -822 
L 1925 406 
Q 1122 244 200 53 
L 81 553 
z
M 2600 2397 
L 4044 2397 
Q 4069 2853 4066 3528 
L 2775 3528 
L 2775 3991 
L 4463 3991 
Q 4744 4441 5200 5309 
L 5688 5078 
Q 5300 4475 4966 3991 
L 6013 3991 
L 6013 3528 
L 4550 3528 
Q 4550 2850 4528 2397 
L 6225 2397 
L 6225 1934 
L 4591 1934 
Q 5069 441 6313 -284 
Q 6088 -509 5913 -753 
Q 4803 88 4350 1353 
Q 4006 13 2831 -816 
Q 2638 -572 2481 -409 
Q 3784 413 4006 1934 
L 2600 1934 
L 2600 2397 
z
M 1925 869 
L 1925 1753 
L 1013 1753 
L 1013 709 
Q 1472 788 1925 869 
z
M 1013 2178 
L 1925 2178 
L 1925 3091 
L 1013 3091 
L 1013 2178 
z
M 1013 3516 
L 1925 3516 
L 1925 4428 
L 1013 4428 
L 1013 3516 
z
M 3013 5053 
L 3375 5309 
Q 3750 4884 4081 4378 
L 3675 4097 
Q 3413 4528 3013 5053 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-7f51" d="M 1075 3641 
L 1444 3834 
Q 1816 3244 2184 2628 
Q 2488 3281 2706 3928 
L 3131 3772 
Q 2822 2931 2450 2175 
Q 2809 1563 3163 922 
L 2738 697 
Q 2478 1191 2203 1691 
Q 1819 969 1375 328 
L 1000 544 
L 1000 -759 
L 531 -759 
L 531 4953 
L 5856 4953 
L 5856 -59 
Q 5856 -728 5138 -728 
Q 4738 -728 4181 -716 
Q 4150 -516 4094 -228 
Q 4619 -272 4988 -272 
Q 5388 -272 5388 72 
L 5388 4503 
L 1000 4503 
L 1000 572 
Q 1538 1363 1947 2150 
Q 1531 2891 1075 3641 
z
M 3156 3341 
L 3519 3553 
Q 3831 3047 4166 2481 
Q 4525 3197 4775 3891 
L 5200 3728 
Q 4847 2838 4428 2028 
Q 4822 1353 5244 597 
L 4825 359 
Q 4513 953 4175 1559 
Q 3788 866 3350 241 
L 2969 484 
Q 3506 1263 3919 2016 
Q 3553 2672 3156 3341 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-7269"/>
     <use xlink:href="#MicrosoftYaHei-8054" x="100"/>
     <use xlink:href="#MicrosoftYaHei-7f51" x="200"/>
     <use xlink:href="#MicrosoftYaHei-5b89" x="300"/>
     <use xlink:href="#MicrosoftYaHei-5168" x="400"/>
    </g>
   </g>
   <g id="text_19">
    <!-- 工业控制系统安全 -->
    <g transform="translate(767.935416 818.219418) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-5de5" d="M 181 97 
L 2919 97 
L 2919 4266 
L 538 4266 
L 538 4766 
L 5863 4766 
L 5863 4266 
L 3469 4266 
L 3469 97 
L 6213 97 
L 6213 -391 
L 181 -391 
L 181 97 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-4e1a" d="M 119 47 
L 2125 47 
L 2125 5209 
L 2650 5209 
L 2650 47 
L 3744 47 
L 3744 5209 
L 4269 5209 
L 4269 47 
L 6294 47 
L 6294 -416 
L 119 -416 
L 119 47 
z
M 844 3984 
Q 1356 2778 1838 1422 
L 1325 1191 
Q 906 2509 388 3784 
L 844 3984 
z
M 4494 1366 
Q 5119 2691 5544 3928 
L 6050 3728 
Q 5469 2234 4944 1159 
L 4494 1366 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-63a7" d="M 88 1978 
Q 550 2091 975 2200 
L 975 3647 
L 119 3647 
L 119 4097 
L 975 4097 
L 975 5309 
L 1463 5309 
L 1463 4097 
L 2100 4097 
L 2100 3647 
L 1463 3647 
L 1463 2328 
Q 1788 2416 2088 2503 
Q 2100 2216 2131 2016 
Q 1784 1919 1463 1825 
L 1463 -103 
Q 1463 -766 869 -766 
Q 663 -766 225 -753 
Q 200 -497 144 -234 
Q 513 -278 694 -278 
Q 975 -278 975 16 
L 975 1684 
Q 534 1556 150 1441 
L 88 1978 
z
M 2425 1884 
L 5975 1884 
L 5975 1447 
L 4438 1447 
L 4438 -109 
L 6275 -109 
L 6275 -572 
L 2013 -572 
L 2013 -109 
L 3950 -109 
L 3950 1447 
L 2425 1447 
L 2425 1884 
z
M 2331 4372 
L 4125 4372 
Q 3894 4738 3613 5103 
L 4013 5359 
Q 4363 4928 4606 4578 
L 4297 4372 
L 6100 4372 
L 6100 3247 
L 5613 3247 
L 5613 3916 
L 2819 3916 
L 2819 3247 
L 2331 3247 
L 2331 4372 
z
M 4506 3166 
L 4838 3491 
Q 5669 2884 6244 2372 
L 5894 1978 
Q 5325 2541 4506 3166 
z
M 3919 3159 
Q 3188 2503 2531 1984 
L 2188 2359 
Q 2950 2909 3563 3503 
L 3919 3159 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-7cfb" d="M 4494 2747 
Q 5294 2103 6131 1334 
Q 5925 1134 5750 953 
Q 5491 1209 5225 1459 
Q 4391 1425 3519 1378 
L 3519 -28 
Q 3519 -741 2769 -741 
Q 2463 -741 1963 -728 
Q 1931 -497 1881 -216 
Q 2406 -272 2688 -272 
Q 3031 -272 3031 66 
L 3031 1353 
Q 2272 1313 1488 1266 
Q 1131 1247 781 1184 
L 575 1722 
Q 988 1797 1356 1966 
Q 2313 2434 3131 2909 
Q 2044 2825 1306 2759 
Q 981 2728 813 2691 
L 613 3216 
Q 913 3291 1175 3434 
Q 1959 3872 2747 4469 
Q 1675 4403 575 4353 
Q 531 4572 463 4791 
Q 3444 4903 5756 5134 
L 5850 4691 
Q 4494 4578 3097 4488 
L 3350 4309 
Q 2369 3653 1538 3203 
Q 2938 3281 3856 3353 
Q 4481 3753 5013 4159 
L 5419 3828 
Q 3850 2709 1775 1734 
Q 3019 1778 4791 1866 
Q 4478 2153 4163 2428 
L 4494 2747 
z
M 4125 697 
L 4444 1034 
Q 5231 509 6175 -222 
L 5806 -628 
Q 4931 97 4125 697 
z
M 1681 1028 
L 2088 709 
Q 1288 -9 406 -678 
Q 256 -497 63 -297 
Q 875 259 1681 1028 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-7edf" d="M 5100 3641 
Q 5625 2934 6175 2047 
Q 5956 1922 5713 1766 
Q 5556 2031 5413 2275 
Q 5122 2256 4813 2241 
L 4813 122 
Q 4813 -259 5094 -259 
L 5400 -259 
Q 5706 -259 5750 53 
Q 5800 428 5813 866 
Q 6019 778 6313 684 
Q 6263 141 6206 -159 
Q 6119 -703 5550 -703 
L 5013 -703 
Q 4331 -703 4331 78 
L 4331 2213 
Q 3981 2194 3606 2169 
Q 3556 809 3256 272 
Q 2900 -416 1925 -866 
Q 1788 -672 1606 -447 
Q 2513 -78 2819 522 
Q 3100 1000 3122 2138 
Q 2994 2128 2863 2122 
Q 2594 2097 2363 2041 
L 2150 2528 
Q 2419 2641 2581 2828 
Q 3050 3388 3463 4047 
L 2100 4047 
L 2100 4491 
L 4094 4491 
Q 3859 4850 3650 5128 
L 4063 5384 
Q 4300 5091 4594 4684 
L 4297 4491 
L 6200 4491 
L 6200 4047 
L 3663 4047 
L 4006 3897 
Q 3488 3197 2988 2609 
Q 4166 2650 5153 2694 
Q 4916 3075 4700 3391 
L 5100 3641 
z
M 894 1534 
Q 1394 1603 2175 1722 
Q 2138 1472 2113 1259 
Q 1331 1153 325 991 
L 225 1441 
Q 616 1841 1153 2744 
Q 503 2700 219 2678 
L 100 3103 
Q 650 3828 1288 5316 
L 1763 5128 
Q 1156 3941 656 3141 
Q 1066 3147 1394 3159 
Q 1581 3494 1788 3884 
L 2256 3678 
Q 1544 2434 894 1534 
z
M 81 222 
Q 1119 422 2225 672 
Q 2200 347 2200 222 
Q 1069 -28 225 -253 
L 81 222 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-5de5"/>
     <use xlink:href="#MicrosoftYaHei-4e1a" x="100"/>
     <use xlink:href="#MicrosoftYaHei-63a7" x="200"/>
     <use xlink:href="#MicrosoftYaHei-5236" x="300"/>
     <use xlink:href="#MicrosoftYaHei-7cfb" x="400"/>
     <use xlink:href="#MicrosoftYaHei-7edf" x="500"/>
     <use xlink:href="#MicrosoftYaHei-5b89" x="600"/>
     <use xlink:href="#MicrosoftYaHei-5168" x="700"/>
    </g>
   </g>
   <g id="text_20">
    <!-- 云原生环境安全 -->
    <g transform="translate(823.561505 783.979394) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-4e91" d="M 4181 1884 
Q 5106 853 6019 -303 
L 5550 -653 
Q 5325 -344 5100 -50 
Q 2856 -128 1313 -228 
Q 1044 -259 806 -328 
L 556 247 
Q 931 428 1181 728 
Q 1853 1541 2422 2491 
L 169 2491 
L 169 2953 
L 6281 2953 
L 6281 2491 
L 3100 2491 
Q 2163 1094 1481 284 
Q 3613 338 4766 384 
Q 4269 1019 3769 1609 
L 4181 1884 
z
M 750 4872 
L 5694 4872 
L 5694 4409 
L 750 4409 
L 750 4872 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-539f" d="M 1950 3853 
L 3081 3853 
Q 3200 4153 3306 4472 
L 1256 4472 
L 1256 2772 
Q 1256 1322 1088 634 
Q 919 -103 394 -866 
Q 238 -653 44 -434 
Q 475 209 613 834 
Q 756 1428 756 2659 
L 756 4928 
L 6219 4928 
L 6219 4472 
L 3856 4472 
Q 3716 4134 3584 3853 
L 5719 3853 
L 5719 1291 
L 5250 1291 
L 5250 1416 
L 4094 1416 
L 4094 16 
Q 4094 -772 3381 -766 
Q 3163 -766 2594 -753 
Q 2569 -516 2513 -209 
Q 3000 -272 3281 -272 
Q 3594 -272 3594 91 
L 3594 1416 
L 2419 1416 
L 2419 1291 
L 1950 1291 
L 1950 3853 
z
M 2419 1834 
L 5250 1834 
L 5250 2434 
L 2419 2434 
L 2419 1834 
z
M 5250 3434 
L 2419 3434 
L 2419 2841 
L 5250 2841 
L 5250 3434 
z
M 2488 1172 
L 2919 891 
Q 2313 -9 1613 -703 
Q 1450 -566 1206 -397 
Q 1838 184 2488 1172 
z
M 4925 1159 
Q 5738 391 6313 -266 
L 5894 -641 
Q 5294 122 4556 859 
L 4925 1159 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-751f" d="M 125 16 
L 3025 16 
L 3025 1609 
L 813 1609 
L 813 2091 
L 3025 2091 
L 3025 3534 
L 1375 3534 
Q 959 2784 419 2178 
Q 288 2384 81 2616 
Q 963 3603 1475 5028 
L 1988 4891 
Q 1822 4434 1622 4016 
L 3025 4016 
L 3025 5341 
L 3563 5341 
L 3563 4016 
L 5950 4016 
L 5950 3534 
L 3563 3534 
L 3563 2091 
L 5775 2091 
L 5775 1609 
L 3563 1609 
L 3563 16 
L 6288 16 
L 6288 -453 
L 125 -453 
L 125 16 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-73af" d="M 2606 4447 
L 2606 4916 
L 6050 4916 
L 6050 4447 
L 4441 4447 
Q 4228 3716 3959 3053 
L 4369 3053 
L 4369 -816 
L 3875 -816 
L 3875 2850 
Q 3247 1381 2338 259 
Q 2175 422 1944 566 
Q 3284 2200 3925 4447 
L 2606 4447 
z
M 94 922 
Q 544 1031 1050 1166 
L 1050 2641 
L 213 2641 
L 213 3091 
L 1050 3091 
L 1050 4241 
L 169 4241 
L 169 4691 
L 2375 4691 
L 2375 4241 
L 1544 4241 
L 1544 3091 
L 2369 3091 
L 2369 2641 
L 1544 2641 
L 1544 1297 
Q 1922 1400 2331 1516 
Q 2313 1284 2325 1084 
Q 1081 691 175 391 
L 94 922 
z
M 4856 2909 
Q 5813 1628 6313 834 
L 5888 509 
Q 5225 1622 4469 2653 
L 4856 2909 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-5883" d="M 2338 2722 
L 5756 2722 
L 5756 591 
L 5325 591 
L 5325 734 
L 4738 734 
L 4738 59 
Q 4738 -284 5063 -284 
L 5456 -284 
Q 5763 -284 5806 28 
Q 5844 272 5856 603 
Q 6094 497 6319 416 
Q 6275 28 6225 -172 
Q 6119 -703 5519 -703 
L 4950 -703 
Q 4294 -703 4294 -78 
L 4294 734 
L 3709 734 
Q 3628 50 3175 -284 
Q 2794 -591 1838 -866 
Q 1706 -653 1550 -434 
Q 2525 -228 2888 66 
Q 3188 300 3250 734 
L 2769 734 
L 2769 591 
L 2338 591 
L 2338 2722 
z
M 106 678 
Q 441 756 881 869 
L 881 3259 
L 169 3259 
L 169 3703 
L 881 3703 
L 881 5266 
L 1338 5266 
L 1338 3703 
L 1981 3703 
L 1981 3259 
L 1338 3259 
L 1338 991 
Q 1688 1084 2094 1197 
Q 2100 928 2113 728 
Q 1150 466 200 159 
L 106 678 
z
M 2113 3522 
L 3238 3522 
Q 3066 3794 2856 4066 
L 3225 4297 
Q 3481 3984 3700 3678 
L 3466 3522 
L 4409 3522 
Q 4663 3909 4881 4303 
L 5331 4053 
Q 5119 3781 4909 3522 
L 6200 3522 
L 6200 3147 
L 2113 3147 
L 2113 3522 
z
M 2263 4703 
L 3913 4703 
Q 3800 4928 3663 5159 
L 4100 5341 
Q 4291 5006 4425 4703 
L 5988 4703 
L 5988 4328 
L 2263 4328 
L 2263 4703 
z
M 2769 1097 
L 5325 1097 
L 5325 1559 
L 2769 1559 
L 2769 1097 
z
M 5325 2359 
L 2769 2359 
L 2769 1909 
L 5325 1909 
L 5325 2359 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-4e91"/>
     <use xlink:href="#MicrosoftYaHei-539f" x="100"/>
     <use xlink:href="#MicrosoftYaHei-751f" x="200"/>
     <use xlink:href="#MicrosoftYaHei-73af" x="300"/>
     <use xlink:href="#MicrosoftYaHei-5883" x="400"/>
     <use xlink:href="#MicrosoftYaHei-5b89" x="500"/>
     <use xlink:href="#MicrosoftYaHei-5168" x="600"/>
    </g>
   </g>
   <g id="text_21">
    <!-- 5G/6G网络安全 -->
    <g transform="translate(857.339954 735.685794) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-35" d="M 3294 1494 
Q 3294 784 2823 350 
Q 2353 -84 1559 -84 
Q 878 -84 544 119 
L 544 744 
Q 1034 425 1544 425 
Q 2050 425 2361 704 
Q 2672 984 2672 1447 
Q 2672 1909 2348 2167 
Q 2025 2425 1434 2425 
Q 978 2425 613 2384 
L 791 4841 
L 3053 4841 
L 3053 4303 
L 1306 4303 
L 1206 2931 
L 1647 2950 
Q 2416 2950 2855 2567 
Q 3294 2184 3294 1494 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-47" d="M 4284 331 
Q 3559 -81 2647 -81 
Q 1597 -81 955 583 
Q 313 1247 313 2356 
Q 313 3478 1022 4200 
Q 1731 4922 2841 4922 
Q 3625 4922 4169 4672 
L 4169 3997 
Q 3588 4363 2784 4363 
Q 1994 4363 1481 3822 
Q 969 3281 969 2397 
Q 969 1488 1441 981 
Q 1913 475 2716 475 
Q 3269 475 3656 688 
L 3656 1997 
L 2606 1997 
L 2606 2550 
L 4284 2550 
L 4284 331 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-2f" d="M 2791 4841 
L 469 -803 
L -103 -803 
L 2219 4841 
L 2791 4841 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-36" d="M 3181 4219 
Q 2803 4413 2391 4413 
Q 1763 4413 1381 3866 
Q 1000 3319 994 2372 
L 1009 2372 
Q 1353 3038 2106 3038 
Q 2734 3038 3109 2628 
Q 3484 2219 3484 1541 
Q 3484 831 3051 375 
Q 2619 -81 1947 -81 
Q 1209 -81 789 495 
Q 369 1072 369 2131 
Q 369 3413 920 4167 
Q 1472 4922 2372 4922 
Q 2888 4922 3181 4781 
L 3181 4219 
z
M 1031 1584 
Q 1031 1106 1286 765 
Q 1541 425 1963 425 
Q 2363 425 2613 719 
Q 2863 1013 2863 1475 
Q 2863 1975 2627 2253 
Q 2391 2531 1956 2531 
Q 1544 2531 1287 2250 
Q 1031 1969 1031 1584 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-7edc" d="M 2244 3241 
Q 3013 4303 3394 5366 
L 3863 5209 
Q 3741 4922 3609 4653 
L 5844 4653 
L 5844 4209 
Q 5391 3431 4694 2831 
Q 5297 2463 6313 2166 
Q 6188 1947 6063 1628 
Q 5028 1988 4294 2528 
Q 3453 1941 2431 1553 
Q 2300 1766 2131 1991 
Q 3091 2316 3906 2853 
Q 3441 3291 3166 3813 
Q 2881 3328 2569 2909 
Q 2450 3047 2244 3241 
z
M 2725 1584 
L 5813 1584 
L 5813 -847 
L 5331 -847 
L 5331 -534 
L 3206 -534 
L 3206 -847 
L 2725 -847 
L 2725 1584 
z
M 900 1616 
Q 1550 1678 2131 1759 
Q 2106 1478 2088 1334 
Q 981 1197 350 1103 
L 250 1559 
Q 609 1822 1256 2941 
Q 659 2900 288 2866 
L 163 3278 
Q 763 4197 1181 5297 
L 1669 5122 
Q 1250 4228 694 3316 
Q 941 3316 1475 3328 
Q 1703 3741 1963 4247 
L 2425 4047 
Q 1675 2716 900 1616 
z
M 5331 1134 
L 3206 1134 
L 3206 -84 
L 5331 -84 
L 5331 1134 
z
M 5275 4228 
L 3419 4228 
Q 3756 3584 4284 3125 
Q 4888 3609 5275 4228 
z
M 163 153 
Q 1200 353 2325 616 
Q 2300 316 2300 141 
Q 1300 -84 300 -341 
L 163 153 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-35"/>
     <use xlink:href="#MicrosoftYaHei-47" x="58.642578"/>
     <use xlink:href="#MicrosoftYaHei-2f" x="133.007812"/>
     <use xlink:href="#MicrosoftYaHei-36" x="175.732422"/>
     <use xlink:href="#MicrosoftYaHei-47" x="234.375"/>
     <use xlink:href="#MicrosoftYaHei-7f51" x="308.740234"/>
     <use xlink:href="#MicrosoftYaHei-7edc" x="408.740234"/>
     <use xlink:href="#MicrosoftYaHei-5b89" x="508.740234"/>
     <use xlink:href="#MicrosoftYaHei-5168" x="608.740234"/>
    </g>
   </g>
   <g id="text_22">
    <!-- 理论与实践结合 -->
    <g style="fill: #ffffff" transform="translate(792.168572 367.966363) scale(0.14 -0.14)">
     <defs>
      <path id="MicrosoftYaHei-Bold-7406" d="M 256 -503 
L 81 384 
L 825 553 
L 825 2091 
L 225 2091 
L 225 2866 
L 825 2866 
L 825 4178 
L 200 4178 
L 200 4966 
L 2250 4966 
L 2250 4178 
L 1625 4178 
L 1625 2866 
L 2206 2866 
L 2206 2091 
L 1625 2091 
L 1625 756 
Q 1959 847 2275 941 
Q 2250 497 2269 84 
L 3819 84 
L 3819 628 
L 2394 628 
L 2394 1341 
L 3819 1341 
L 3819 1791 
L 2425 1791 
L 2425 5034 
L 6013 5034 
L 6013 1791 
L 4619 1791 
L 4619 1341 
L 6044 1341 
L 6044 628 
L 4619 628 
L 4619 84 
L 6319 84 
L 6319 -628 
L 2031 -628 
L 2031 31 
Q 1206 -197 256 -503 
z
M 5231 4334 
L 4619 4334 
L 4619 3741 
L 5231 3741 
L 5231 4334 
z
M 3206 3741 
L 3819 3741 
L 3819 4334 
L 3206 4334 
L 3206 3741 
z
M 5231 2491 
L 5231 3091 
L 4619 3091 
L 4619 2491 
L 5231 2491 
z
M 3206 2491 
L 3819 2491 
L 3819 3091 
L 3206 3091 
L 3206 2491 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-8bba" d="M 6331 3391 
Q 6044 2941 5869 2541 
Q 4719 3328 4088 4384 
Q 3513 3528 2756 2878 
L 3450 2878 
L 3450 1928 
Q 4275 2272 5056 2809 
L 5613 2116 
Q 4550 1497 3450 1091 
L 3450 516 
Q 3444 91 3875 91 
L 4619 91 
Q 5056 91 5119 497 
Q 5181 853 5225 1347 
Q 5625 1203 6144 1059 
Q 6013 272 5913 -53 
Q 5744 -697 4856 -697 
L 3469 -697 
Q 2575 -697 2575 309 
L 2575 2722 
Q 2413 2597 2250 2484 
Q 2088 2841 1788 3284 
Q 3006 4084 3675 5259 
L 4713 5259 
L 4594 5072 
Q 5338 3897 6331 3391 
z
M 2244 1247 
Q 2313 653 2388 309 
Q 1463 -366 1044 -791 
L 594 -172 
Q 731 -3 738 341 
L 738 2391 
L 125 2391 
L 125 3234 
L 1594 3234 
L 1594 741 
Q 1781 878 2244 1247 
z
M 1213 5278 
Q 1619 4753 2050 4128 
Q 1781 3953 1238 3622 
Q 856 4297 481 4847 
L 1213 5278 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-5b9e" d="M 4219 3716 
Q 4181 2422 4056 1653 
L 6238 1653 
L 6238 878 
L 3863 878 
Q 3850 841 3813 766 
Q 5019 372 6206 -66 
L 5881 -847 
Q 4619 -316 3369 153 
Q 2550 -616 863 -916 
Q 556 -384 331 -78 
Q 2338 209 2894 878 
L 169 878 
L 169 1653 
L 2144 1653 
Q 1481 2053 931 2328 
L 1319 2934 
Q 1894 2659 2638 2247 
L 2244 1653 
L 3175 1653 
Q 3331 2453 3350 3716 
L 4219 3716 
z
M 300 3078 
L 300 4703 
L 2819 4703 
Q 2700 5009 2606 5228 
L 3675 5341 
Q 3769 5084 3888 4703 
L 6094 4703 
L 6094 3078 
L 5225 3078 
L 5225 3953 
L 1169 3953 
L 1169 3078 
L 300 3078 
z
M 1813 3772 
Q 2469 3484 3163 3134 
L 2769 2497 
Q 2031 2909 1450 3184 
L 1813 3772 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-8df5" d="M 244 -747 
L 106 103 
L 306 128 
L 306 2428 
L 925 2428 
L 925 219 
L 1300 275 
L 1300 2816 
L 381 2816 
L 381 5041 
L 2625 5041 
L 2625 2816 
L 2056 2816 
L 2056 2103 
L 2700 2103 
L 2700 1378 
L 2056 1378 
L 2056 397 
L 2706 509 
Q 2703 291 2703 106 
Q 3363 331 3966 722 
Q 3822 1203 3728 1841 
L 2919 1709 
L 2800 2447 
L 3641 2581 
Q 3613 2884 3591 3238 
L 2838 3147 
L 2713 3891 
L 3547 3988 
L 3481 5322 
L 4288 5322 
Q 4303 4769 4338 4081 
L 5300 4194 
Q 4872 4553 4481 4828 
L 4875 5322 
Q 5350 5034 5794 4703 
Q 5550 4425 5378 4203 
L 6056 4284 
L 6144 3553 
L 4381 3334 
L 4428 2706 
L 6213 2991 
L 6306 2266 
L 4519 1972 
Q 4581 1572 4666 1244 
Q 5106 1622 5513 2097 
L 6050 1628 
Q 5506 1000 4919 513 
Q 5191 -16 5359 -16 
Q 5450 -16 5497 100 
Q 5544 216 5719 984 
Q 5806 934 6325 678 
Q 6081 -378 5911 -615 
Q 5741 -853 5400 -853 
Q 4688 -853 4244 3 
Q 3575 -450 2856 -734 
Q 2738 -522 2609 -319 
Q 1425 -519 244 -747 
z
M 1138 3516 
L 1863 3516 
L 1863 4241 
L 1138 4241 
L 1138 3516 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-7ed3" d="M 2538 3128 
L 3850 3128 
L 3850 3809 
L 2472 3809 
L 2681 3709 
Q 1906 2528 1350 1784 
Q 1800 1841 2313 1891 
Q 2275 1466 2263 1116 
Q 894 966 400 834 
L 156 1534 
Q 553 1884 1100 2697 
Q 631 2663 288 2616 
L 56 3328 
Q 550 3934 1138 5366 
L 1963 5059 
Q 1469 4134 988 3397 
L 1563 3425 
L 1938 4066 
L 2350 3869 
L 2350 4553 
L 3850 4553 
L 3850 5341 
L 4725 5341 
L 4725 4553 
L 6288 4553 
L 6288 3809 
L 4725 3809 
L 4725 3128 
L 6100 3128 
L 6100 2384 
L 2538 2384 
L 2538 3128 
z
M 2594 -841 
L 2594 1847 
L 6025 1847 
L 6025 -841 
L 5188 -841 
L 5188 -472 
L 3431 -472 
L 3431 -841 
L 2594 -841 
z
M 5188 1084 
L 3431 1084 
L 3431 291 
L 5188 291 
L 5188 1084 
z
M 231 -516 
L 94 359 
Q 1175 497 2406 684 
Q 2369 159 2375 -184 
Q 1263 -341 231 -516 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-Bold-5408" d="M 875 -872 
L 875 2028 
L 5525 2028 
L 5525 -872 
L 4675 -872 
L 4675 -584 
L 1725 -584 
L 1725 -872 
L 875 -872 
z
M 3988 5316 
L 3763 5041 
Q 4619 3947 6306 3228 
Q 5975 2809 5738 2484 
Q 5338 2672 4975 2897 
L 4975 2553 
L 1425 2553 
L 1425 2922 
Q 1063 2684 644 2441 
Q 388 2828 88 3178 
Q 2031 4178 2800 5316 
L 3988 5316 
z
M 4675 1216 
L 1725 1216 
L 1725 228 
L 4675 228 
L 4675 1216 
z
M 3194 4409 
Q 2756 3891 2031 3353 
L 4313 3353 
Q 3694 3822 3194 4409 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-Bold-7406"/>
     <use xlink:href="#MicrosoftYaHei-Bold-8bba" x="100"/>
     <use xlink:href="#MicrosoftYaHei-Bold-4e0e" x="200"/>
     <use xlink:href="#MicrosoftYaHei-Bold-5b9e" x="300"/>
     <use xlink:href="#MicrosoftYaHei-Bold-8df5" x="400"/>
     <use xlink:href="#MicrosoftYaHei-Bold-7ed3" x="500"/>
     <use xlink:href="#MicrosoftYaHei-Bold-5408" x="600"/>
    </g>
   </g>
   <g id="text_23">
    <!-- 安全度量与评估 -->
    <g transform="translate(971.933891 402.288963) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-5ea6" d="M 1550 1216 
L 1550 1628 
L 5469 1628 
L 5469 1222 
Q 4903 613 4147 163 
Q 5050 -200 6263 -309 
L 6019 -797 
Q 4625 -603 3644 -109 
Q 2600 -619 1250 -866 
Q 1150 -666 994 -416 
Q 2231 -222 3166 166 
Q 2497 600 2066 1216 
L 1550 1216 
z
M 1363 3541 
L 2294 3541 
L 2294 4066 
L 2769 4066 
L 2769 3541 
L 4538 3541 
L 4538 4072 
L 5013 4072 
L 5013 3541 
L 6150 3541 
L 6150 3128 
L 5013 3128 
L 5013 1928 
L 4538 1928 
L 4538 2147 
L 2769 2147 
L 2769 1922 
L 2294 1922 
L 2294 3128 
L 1363 3128 
L 1363 3541 
z
M 3025 5172 
L 3481 5391 
Q 3694 5056 3909 4634 
L 6213 4634 
L 6213 4197 
L 1225 4197 
L 1225 2316 
Q 1225 178 394 -872 
Q 238 -666 38 -447 
Q 725 397 738 2347 
L 738 4634 
L 3331 4634 
Q 3188 4909 3025 5172 
z
M 2769 2547 
L 4538 2547 
L 4538 3128 
L 2769 3128 
L 2769 2547 
z
M 4819 1216 
L 2588 1216 
Q 3031 728 3656 391 
Q 4334 741 4819 1216 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-4e0e" d="M 1163 2384 
L 1163 2859 
L 1413 5328 
L 1931 5328 
L 1834 4334 
L 6269 4334 
L 6269 3859 
L 1791 3859 
L 1694 2859 
L 5813 2859 
Q 5738 1547 5644 416 
Q 5556 -247 5263 -478 
Q 4981 -703 4322 -703 
Q 3706 -697 2900 -678 
Q 2863 -466 2794 -134 
Q 3744 -203 4213 -209 
Q 5050 -266 5125 447 
Q 5206 1278 5263 2384 
L 1163 2384 
z
M 106 1409 
L 4738 1409 
L 4738 934 
L 106 934 
L 106 1409 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-8bc4" d="M 1750 2028 
L 3775 2028 
L 3775 4447 
L 2088 4447 
L 2088 4916 
L 5975 4916 
L 5975 4447 
L 4281 4447 
L 4281 2028 
L 6319 2028 
L 6319 1553 
L 4281 1553 
L 4281 -797 
L 3775 -797 
L 3775 1553 
L 1750 1553 
L 1750 2028 
z
M 2219 866 
Q 2238 553 2263 328 
Q 1456 -228 963 -628 
L 669 -209 
Q 881 -28 881 303 
L 881 2716 
L 100 2716 
L 100 3166 
L 1363 3166 
L 1363 241 
Q 1775 534 2219 866 
z
M 4725 2697 
Q 5175 3309 5563 4028 
L 6019 3753 
Q 5613 3091 5125 2403 
L 4725 2697 
z
M 2056 3791 
L 2450 4028 
Q 2919 3353 3288 2716 
L 2850 2434 
Q 2431 3209 2056 3791 
z
M 738 5222 
Q 1419 4522 1725 4184 
L 1288 3816 
Q 894 4334 356 4891 
L 738 5222 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-4f30" d="M 2250 2284 
L 3781 2284 
L 3781 3553 
L 1800 3553 
L 1800 4016 
L 3781 4016 
L 3781 5316 
L 4294 5316 
L 4294 4016 
L 6300 4016 
L 6300 3553 
L 4294 3553 
L 4294 2284 
L 5831 2284 
L 5831 -841 
L 5319 -841 
L 5319 -353 
L 2763 -353 
L 2763 -841 
L 2250 -841 
L 2250 2284 
z
M 5319 1822 
L 2763 1822 
L 2763 97 
L 5319 97 
L 5319 1822 
z
M 44 2259 
Q 919 3559 1431 5347 
L 1944 5197 
Q 1713 4494 1456 3878 
L 1456 -853 
L 963 -853 
L 963 2834 
Q 634 2213 269 1716 
Q 175 1984 44 2259 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-5b89"/>
     <use xlink:href="#MicrosoftYaHei-5168" x="100"/>
     <use xlink:href="#MicrosoftYaHei-5ea6" x="200"/>
     <use xlink:href="#MicrosoftYaHei-91cf" x="300"/>
     <use xlink:href="#MicrosoftYaHei-4e0e" x="400"/>
     <use xlink:href="#MicrosoftYaHei-8bc4" x="500"/>
     <use xlink:href="#MicrosoftYaHei-4f30" x="600"/>
    </g>
   </g>
   <g id="text_24">
    <!-- 安全与隐私平衡 -->
    <g transform="translate(974.074669 344.071842) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-5e73" d="M 169 1934 
L 2938 1934 
L 2938 4547 
L 450 4547 
L 450 5028 
L 5975 5028 
L 5975 4547 
L 3463 4547 
L 3463 1934 
L 6250 1934 
L 6250 1453 
L 3463 1453 
L 3463 -847 
L 2938 -847 
L 2938 1453 
L 169 1453 
L 169 1934 
z
M 3950 2559 
Q 4638 3284 5188 4159 
L 5656 3859 
Q 4950 2891 4338 2234 
L 3950 2559 
z
M 769 3859 
L 1163 4134 
Q 1863 3316 2369 2553 
L 1925 2222 
Q 1444 3003 769 3859 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-8861" d="M 1456 3784 
Q 2081 4434 2538 5241 
L 2925 5091 
Q 2847 4959 2766 4828 
L 4063 4828 
L 4063 4491 
L 3703 3966 
L 4375 3966 
L 4375 1491 
L 3988 1491 
L 3988 1647 
L 2238 1647 
L 2238 1453 
L 1850 1453 
L 1850 3603 
Q 1800 3547 1750 3491 
Q 1606 3647 1456 3784 
z
M 1644 1153 
L 2869 1153 
Q 2894 1334 2894 1553 
L 3319 1553 
Q 3319 1338 3300 1153 
L 4450 1153 
L 4450 753 
L 3228 753 
Q 3216 706 3200 659 
Q 3784 309 4300 -9 
L 4038 -391 
Q 3606 -84 3022 306 
Q 2678 -191 1725 -653 
Q 1638 -522 1469 -266 
Q 2563 222 2772 753 
L 1644 753 
L 1644 1153 
z
M 4519 3016 
L 6300 3016 
L 6300 2559 
L 5650 2559 
L 5650 -97 
Q 5650 -778 5006 -791 
Q 4744 -797 4350 -791 
Q 4325 -641 4256 -328 
Q 4613 -366 4881 -366 
Q 5225 -366 5225 -41 
L 5225 2559 
L 4519 2559 
L 4519 3016 
z
M 50 1659 
Q 694 2378 1263 3659 
L 1650 3434 
Q 1409 2953 1181 2553 
L 1181 -834 
L 756 -834 
L 756 1850 
Q 494 1447 250 1159 
Q 163 1403 50 1659 
z
M 44 3616 
Q 788 4341 1275 5341 
L 1675 5084 
Q 1031 3928 275 3166 
Q 175 3391 44 3616 
z
M 4688 4841 
L 6125 4841 
L 6125 4391 
L 4688 4391 
L 4688 4841 
z
M 3513 4428 
L 2494 4428 
Q 2331 4200 2147 3966 
L 3219 3966 
L 3513 4428 
z
M 3988 3572 
L 3294 3572 
L 3294 2984 
L 3988 2984 
L 3988 3572 
z
M 2238 2984 
L 2931 2984 
L 2931 3572 
L 2238 3572 
L 2238 2984 
z
M 2238 2041 
L 2931 2041 
L 2931 2622 
L 2238 2622 
L 2238 2041 
z
M 3294 2041 
L 3988 2041 
L 3988 2622 
L 3294 2622 
L 3294 2041 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-5b89"/>
     <use xlink:href="#MicrosoftYaHei-5168" x="100"/>
     <use xlink:href="#MicrosoftYaHei-4e0e" x="200"/>
     <use xlink:href="#MicrosoftYaHei-9690" x="300"/>
     <use xlink:href="#MicrosoftYaHei-79c1" x="400"/>
     <use xlink:href="#MicrosoftYaHei-5e73" x="500"/>
     <use xlink:href="#MicrosoftYaHei-8861" x="600"/>
    </g>
   </g>
   <g id="text_25">
    <!-- 理论基础研究 -->
    <g transform="translate(961.11919 288.67032) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-7406" d="M 2031 -166 
L 3988 -166 
L 3988 734 
L 2394 734 
L 2394 1172 
L 3988 1172 
L 3988 1997 
L 2988 1997 
L 2988 1766 
L 2525 1766 
L 2525 4991 
L 5913 4991 
L 5913 1766 
L 5450 1766 
L 5450 1997 
L 4450 1997 
L 4450 1172 
L 6044 1172 
L 6044 734 
L 4450 734 
L 4450 -166 
L 6319 -166 
L 6319 -603 
L 2031 -603 
L 2031 -166 
z
M 44 203 
Q 525 319 975 434 
L 975 2328 
L 194 2328 
L 194 2766 
L 975 2766 
L 975 4328 
L 131 4328 
L 131 4766 
L 2281 4766 
L 2281 4328 
L 1438 4328 
L 1438 2766 
L 2231 2766 
L 2231 2328 
L 1438 2328 
L 1438 559 
Q 1850 675 2238 791 
Q 2269 509 2306 303 
Q 1238 16 213 -303 
L 44 203 
z
M 4450 2422 
L 5450 2422 
L 5450 3291 
L 4450 3291 
L 4450 2422 
z
M 2988 2422 
L 3988 2422 
L 3988 3291 
L 2988 3291 
L 2988 2422 
z
M 5450 4566 
L 4450 4566 
L 4450 3703 
L 5450 3703 
L 5450 4566 
z
M 2988 3703 
L 3988 3703 
L 3988 4566 
L 2988 4566 
L 2988 3703 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-8bba" d="M 2725 2866 
L 3225 2866 
L 3225 1553 
Q 4425 2072 5188 2753 
L 5538 2341 
Q 4569 1578 3225 1034 
L 3225 216 
Q 3225 -197 3600 -197 
L 4844 -197 
Q 5294 -197 5363 203 
Q 5444 659 5475 1191 
Q 5675 1103 5981 1009 
Q 5925 497 5844 53 
Q 5719 -672 4869 -672 
L 3450 -672 
Q 2725 -672 2725 122 
L 2725 2866 
z
M 4413 5253 
L 4331 5109 
Q 5075 3791 6344 3166 
Q 6163 2947 5981 2697 
Q 4744 3516 4056 4666 
Q 3388 3559 2106 2678 
Q 1925 2941 1763 3109 
Q 3181 3947 3831 5253 
L 4413 5253 
z
M 2206 691 
Q 2225 366 2250 153 
Q 1550 -316 994 -747 
L 700 -328 
Q 913 -153 913 209 
L 913 2678 
L 100 2678 
L 100 3141 
L 1400 3141 
L 1400 122 
Q 1769 372 2206 691 
z
M 856 5197 
Q 1538 4497 1844 4159 
L 1406 3791 
Q 1013 4309 475 4866 
L 856 5197 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-57fa" d="M 6319 697 
Q 6113 441 5981 247 
Q 5406 519 4925 897 
L 4925 553 
L 3431 553 
L 3431 -97 
L 5875 -97 
L 5875 -503 
L 519 -503 
L 519 -97 
L 2963 -97 
L 2963 553 
L 1450 553 
L 1450 894 
Q 953 478 413 172 
Q 294 341 88 566 
Q 1050 1034 1753 1753 
L 156 1753 
L 156 2159 
L 1400 2159 
L 1400 4253 
L 350 4253 
L 350 4659 
L 1400 4659 
L 1400 5334 
L 1869 5334 
L 1869 4659 
L 4550 4659 
L 4550 5334 
L 5019 5334 
L 5019 4659 
L 6069 4659 
L 6069 4253 
L 5019 4253 
L 5019 2159 
L 6244 2159 
L 6244 1753 
L 4600 1753 
Q 5303 1053 6319 697 
z
M 2963 1553 
L 3431 1553 
L 3431 947 
L 4863 947 
Q 4416 1306 4047 1753 
L 2325 1753 
Q 1934 1309 1513 947 
L 2963 947 
L 2963 1553 
z
M 1869 2159 
L 4550 2159 
L 4550 2603 
L 1869 2603 
L 1869 2159 
z
M 1869 2991 
L 4550 2991 
L 4550 3428 
L 1869 3428 
L 1869 2991 
z
M 1869 3816 
L 4550 3816 
L 4550 4253 
L 1869 4253 
L 1869 3816 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-7840" d="M 5500 2222 
L 5500 2516 
L 4594 2516 
L 4594 3 
L 5613 3 
L 5613 1803 
L 6075 1803 
L 6075 -728 
L 5613 -728 
L 5613 -447 
L 2663 -447 
L 2663 1803 
L 3125 1803 
L 3125 3 
L 4131 3 
L 4131 2516 
L 2775 2516 
L 2775 4622 
L 3238 4622 
L 3238 2966 
L 4131 2966 
L 4131 5272 
L 4594 5272 
L 4594 2966 
L 5500 2966 
L 5500 4616 
L 5963 4616 
L 5963 2222 
L 5500 2222 
z
M 19 1784 
Q 725 2956 1038 4366 
L 225 4366 
L 225 4778 
L 2444 4778 
L 2444 4366 
L 1453 4366 
Q 1294 3641 1059 2991 
L 2200 2991 
L 2200 -197 
L 1788 -197 
L 1788 222 
L 1100 222 
L 1100 -372 
L 688 -372 
L 688 2106 
Q 522 1763 331 1447 
Q 200 1616 19 1784 
z
M 1788 2591 
L 1100 2591 
L 1100 622 
L 1788 622 
L 1788 2591 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-7814" d="M 2506 2616 
L 3259 2616 
Q 3263 2763 3263 4478 
L 2756 4478 
L 2756 4941 
L 6063 4941 
L 6063 4478 
L 5431 4478 
L 5431 2616 
L 6288 2616 
L 6288 2153 
L 5431 2153 
L 5431 -803 
L 4950 -803 
L 4950 2153 
L 3728 2153 
Q 3713 303 2525 -853 
Q 2331 -659 2113 -491 
Q 3225 553 3241 2153 
L 2506 2153 
L 2506 2616 
z
M 238 4916 
L 2519 4916 
L 2519 4478 
L 1613 4478 
Q 1459 3725 1209 3059 
L 2263 3059 
L 2263 128 
L 1088 128 
L 1088 -391 
L 650 -391 
L 650 1875 
Q 522 1656 381 1453 
Q 238 1678 81 1841 
Q 872 3022 1147 4478 
L 238 4478 
L 238 4916 
z
M 3741 2616 
L 4950 2616 
L 4950 4478 
L 3744 4478 
Q 3744 2859 3741 2616 
z
M 1825 559 
L 1825 2628 
L 1088 2628 
L 1088 559 
L 1825 559 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-7a76" d="M 4613 2053 
L 4613 266 
Q 4613 -147 4944 -147 
L 5394 -147 
Q 5694 -147 5731 159 
Q 5775 466 5806 997 
Q 6019 897 6294 809 
Q 6269 478 6200 9 
Q 6125 -616 5525 -616 
L 4806 -616 
Q 4106 -616 4106 222 
L 4106 1603 
L 2700 1603 
Q 2466 -219 456 -834 
Q 269 -528 156 -391 
Q 2000 156 2200 1603 
L 606 1603 
L 606 2053 
L 2250 2053 
Q 2281 2409 2306 2934 
L 2800 2934 
Q 2778 2431 2747 2053 
L 4613 2053 
z
M 388 4528 
L 3028 4528 
Q 2841 4884 2688 5128 
L 3131 5359 
Q 3338 5047 3563 4659 
L 3288 4528 
L 6031 4528 
L 6031 3291 
L 5531 3291 
L 5531 4066 
L 888 4066 
L 888 3291 
L 388 3291 
L 388 4528 
z
M 3938 3834 
Q 4950 3241 5988 2559 
L 5663 2134 
Q 4656 2866 3638 3484 
L 3938 3834 
z
M 2244 3847 
L 2588 3509 
Q 1600 2734 588 2172 
Q 406 2428 281 2572 
Q 1400 3128 2244 3847 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-7406"/>
     <use xlink:href="#MicrosoftYaHei-8bba" x="100"/>
     <use xlink:href="#MicrosoftYaHei-57fa" x="200"/>
     <use xlink:href="#MicrosoftYaHei-7840" x="300"/>
     <use xlink:href="#MicrosoftYaHei-7814" x="400"/>
     <use xlink:href="#MicrosoftYaHei-7a76" x="500"/>
    </g>
   </g>
   <g id="text_26">
    <!-- 标准化与生态建设 -->
    <g transform="translate(911.353765 242.766638) scale(0.12 -0.12)">
     <defs>
      <path id="MicrosoftYaHei-6807" d="M 75 1409 
Q 769 2478 1025 3647 
L 119 3647 
L 119 4072 
L 1069 4072 
L 1069 5297 
L 1506 5297 
L 1506 4072 
L 2356 4072 
L 2356 3647 
L 1506 3647 
L 1506 2541 
L 1775 2822 
Q 2175 2478 2481 2159 
L 2150 1816 
Q 1850 2156 1506 2503 
L 1506 -797 
L 1069 -797 
L 1069 2588 
Q 738 1513 281 859 
Q 194 1128 75 1409 
z
M 2350 3097 
L 6250 3097 
L 6250 2659 
L 4525 2659 
L 4525 47 
Q 4525 -734 3806 -734 
Q 3469 -741 2975 -728 
Q 2944 -503 2888 -209 
Q 3319 -259 3675 -259 
Q 4050 -259 4050 203 
L 4050 2659 
L 2350 2659 
L 2350 3097 
z
M 2638 4928 
L 5963 4928 
L 5963 4491 
L 2638 4491 
L 2638 4928 
z
M 4938 1853 
L 5381 2066 
Q 5975 928 6369 72 
L 5869 -147 
Q 5463 822 4938 1853 
z
M 2931 2066 
L 3400 1878 
Q 2888 734 2381 -153 
Q 2188 -41 1925 66 
Q 2425 872 2931 2066 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-51c6" d="M 1413 2766 
Q 2313 3866 2869 5334 
L 3331 5172 
Q 3128 4694 2906 4253 
L 6194 4253 
L 6194 3822 
L 4675 3822 
L 4675 2891 
L 6025 2891 
L 6025 2459 
L 4675 2459 
L 4675 1528 
L 6038 1528 
L 6038 1097 
L 4675 1097 
L 4675 78 
L 6275 78 
L 6275 -341 
L 2769 -341 
L 2769 -841 
L 2281 -841 
L 2281 3131 
Q 2034 2738 1769 2384 
Q 1631 2578 1413 2766 
z
M 2769 78 
L 4188 78 
L 4188 1097 
L 2769 1097 
L 2769 78 
z
M 1081 2422 
Q 1325 2291 1556 2209 
Q 1113 916 681 -372 
L 169 -141 
Q 650 1003 1081 2422 
z
M 2769 1528 
L 4188 1528 
L 4188 2459 
L 2769 2459 
L 2769 1528 
z
M 2769 2891 
L 4188 2891 
L 4188 3822 
L 2769 3822 
L 2769 2891 
z
M 531 4991 
Q 1050 4384 1581 3709 
Q 1538 3678 1150 3341 
Q 681 3997 138 4672 
L 531 4991 
z
M 3919 5116 
L 4325 5372 
Q 4569 5072 4881 4622 
L 4450 4334 
Q 4156 4784 3919 5116 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-5316" d="M 1844 1453 
Q 2663 1834 3375 2331 
L 3375 5197 
L 3913 5197 
L 3913 2734 
Q 4869 3506 5613 4516 
L 6069 4184 
Q 5159 2975 3913 2069 
L 3913 303 
Q 3913 -122 4363 -122 
L 5156 -122 
Q 5581 -122 5650 284 
Q 5738 709 5763 1266 
Q 6025 1166 6313 1084 
Q 6263 559 6181 122 
Q 6063 -597 5281 -597 
L 4225 -597 
Q 3375 -597 3375 241 
L 3375 1703 
Q 2791 1328 2144 1016 
Q 2013 1222 1844 1453 
z
M 88 2222 
Q 1175 3666 1688 5291 
L 2231 5134 
Q 1934 4359 1588 3675 
L 1588 -797 
L 1063 -797 
L 1063 2734 
Q 716 2172 325 1678 
Q 219 1947 88 2222 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-6807"/>
     <use xlink:href="#MicrosoftYaHei-51c6" x="100"/>
     <use xlink:href="#MicrosoftYaHei-5316" x="200"/>
     <use xlink:href="#MicrosoftYaHei-4e0e" x="300"/>
     <use xlink:href="#MicrosoftYaHei-751f" x="400"/>
     <use xlink:href="#MicrosoftYaHei-6001" x="500"/>
     <use xlink:href="#MicrosoftYaHei-5efa" x="600"/>
     <use xlink:href="#MicrosoftYaHei-8bbe" x="700"/>
    </g>
   </g>
   <g id="text_27">
    <!-- 图5-2 未来研究方向框架图 -->
    <g transform="translate(453.385938 24.485931) scale(0.2 -0.2)">
     <defs>
      <path id="MicrosoftYaHei-2d" d="M 2328 1681 
L 488 1681 
L 488 2153 
L 2328 2153 
L 2328 1681 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-32" d="M 2666 3572 
Q 2666 3963 2419 4186 
Q 2172 4409 1747 4409 
Q 1447 4409 1140 4256 
Q 834 4103 569 3819 
L 569 4413 
Q 806 4659 1107 4790 
Q 1409 4922 1816 4922 
Q 2472 4922 2872 4570 
Q 3272 4219 3272 3619 
Q 3272 3084 3026 2693 
Q 2781 2303 2188 1894 
Q 1581 1475 1376 1294 
Q 1172 1113 1090 947 
Q 1009 781 1009 544 
L 3416 544 
L 3416 0 
L 372 0 
L 372 241 
Q 372 659 486 951 
Q 600 1244 862 1520 
Q 1125 1797 1681 2184 
Q 2250 2584 2458 2886 
Q 2666 3188 2666 3572 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-20" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-672a" d="M 6325 128 
Q 6150 -84 5975 -334 
Q 4328 488 3444 1791 
L 3444 -822 
L 2956 -822 
L 2956 1784 
Q 2006 525 419 -409 
Q 300 -241 81 9 
Q 1778 900 2763 2153 
L 181 2153 
L 181 2616 
L 2956 2616 
L 2956 3791 
L 550 3791 
L 550 4253 
L 2956 4253 
L 2956 5322 
L 3444 5322 
L 3444 4253 
L 5875 4253 
L 5875 3791 
L 3444 3791 
L 3444 2616 
L 6219 2616 
L 6219 2153 
L 3619 2153 
Q 4625 809 6325 128 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-6765" d="M 6325 141 
Q 6119 -122 5975 -334 
Q 4344 403 3438 1550 
L 3438 -841 
L 2963 -841 
L 2963 1584 
Q 2050 516 413 -409 
Q 281 -222 75 22 
Q 1706 841 2806 1922 
L 181 1922 
L 181 2359 
L 2963 2359 
L 2963 4003 
L 550 4003 
L 550 4441 
L 2963 4441 
L 2963 5341 
L 3438 5341 
L 3438 4441 
L 5900 4441 
L 5900 4003 
L 3438 4003 
L 3438 2359 
L 6225 2359 
L 6225 1922 
L 3566 1922 
Q 4594 778 6325 141 
z
M 4056 2766 
Q 4631 3378 4950 3828 
L 5381 3497 
Q 4913 2941 4419 2453 
L 4056 2766 
z
M 1075 3509 
L 1438 3803 
Q 1900 3347 2313 2828 
L 1900 2484 
Q 1581 2922 1075 3509 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-65b9" d="M 194 4241 
L 3194 4241 
Q 2878 4841 2700 5116 
L 3188 5347 
Q 3363 5078 3713 4472 
L 3256 4241 
L 6219 4241 
L 6219 3766 
L 2428 3766 
Q 2406 3197 2381 2797 
L 5400 2797 
Q 5325 1441 5238 441 
Q 5169 -691 4138 -691 
Q 3663 -691 2869 -641 
Q 2838 -403 2775 -66 
Q 3569 -172 4063 -172 
Q 4638 -172 4706 509 
Q 4781 1253 4838 2328 
L 2347 2328 
Q 2172 266 438 -872 
Q 269 -672 56 -447 
Q 1706 641 1825 2466 
Q 1869 2959 1897 3766 
L 194 3766 
L 194 4241 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-5411" d="M 1038 -772 
L 550 -772 
L 550 4359 
L 2394 4359 
Q 2550 4822 2694 5328 
L 3231 5178 
Q 3069 4741 2913 4359 
L 5838 4359 
L 5838 -34 
Q 5838 -741 5131 -741 
Q 4738 -741 4181 -728 
Q 4150 -509 4094 -241 
Q 4631 -284 5006 -284 
Q 5350 -284 5350 78 
L 5350 3897 
L 1038 3897 
L 1038 -772 
z
M 1894 2997 
L 4481 2997 
L 4481 803 
L 2369 803 
L 2369 372 
L 1894 372 
L 1894 2997 
z
M 4006 2547 
L 2369 2547 
L 2369 1253 
L 4006 1253 
L 4006 2547 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-6846" d="M 2475 4941 
L 6163 4941 
L 6163 4491 
L 2944 4491 
L 2944 -122 
L 6250 -122 
L 6250 -572 
L 2475 -572 
L 2475 4941 
z
M 88 1403 
Q 747 2447 1028 3666 
L 131 3666 
L 131 4103 
L 1031 4103 
L 1031 5322 
L 1494 5322 
L 1494 4103 
L 2275 4103 
L 2275 3666 
L 1494 3666 
L 1494 2669 
L 1769 2922 
Q 2038 2647 2419 2203 
L 2075 1884 
Q 1819 2228 1494 2603 
L 1494 -841 
L 1031 -841 
L 1031 2506 
Q 709 1456 288 853 
Q 206 1122 88 1403 
z
M 3156 1078 
L 4425 1078 
L 4425 2041 
L 3425 2041 
L 3425 2466 
L 4425 2466 
L 4425 3366 
L 3313 3366 
L 3313 3791 
L 6025 3791 
L 6025 3366 
L 4888 3366 
L 4888 2466 
L 5888 2466 
L 5888 2041 
L 4888 2041 
L 4888 1078 
L 6156 1078 
L 6156 653 
L 3156 653 
L 3156 1078 
z
" transform="scale(0.015625)"/>
      <path id="MicrosoftYaHei-67b6" d="M 6313 -91 
Q 6081 -347 5925 -566 
Q 4369 -34 3438 1094 
L 3438 -859 
L 2963 -859 
L 2963 1103 
Q 1913 -41 450 -641 
Q 306 -453 88 -216 
Q 1509 269 2606 1209 
L 200 1209 
L 200 1647 
L 2963 1647 
L 2963 2347 
L 3438 2347 
L 3438 1647 
L 6231 1647 
L 6231 1209 
L 3772 1209 
Q 4781 231 6313 -91 
z
M 3588 4797 
L 5863 4797 
L 5863 2334 
L 5400 2334 
L 5400 2653 
L 4050 2653 
L 4050 2316 
L 3588 2316 
L 3588 4797 
z
M 281 4628 
L 1181 4628 
Q 1200 4953 1200 5347 
L 1663 5347 
Q 1663 4956 1647 4628 
L 3050 4628 
Q 3019 3766 2981 3234 
Q 2944 2666 2763 2472 
Q 2600 2278 2138 2278 
Q 1950 2278 1650 2284 
Q 1606 2534 1544 2753 
Q 1913 2716 2113 2716 
Q 2475 2716 2506 3109 
Q 2556 3728 2575 4203 
L 1616 4203 
Q 1563 2891 444 1972 
Q 300 2141 81 2316 
Q 1050 3041 1144 4203 
L 281 4203 
L 281 4628 
z
M 5400 4372 
L 4050 4372 
L 4050 3078 
L 5400 3078 
L 5400 4372 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#MicrosoftYaHei-56fe"/>
     <use xlink:href="#MicrosoftYaHei-35" x="100"/>
     <use xlink:href="#MicrosoftYaHei-2d" x="158.642578"/>
     <use xlink:href="#MicrosoftYaHei-32" x="201.904297"/>
     <use xlink:href="#MicrosoftYaHei-20" x="260.546875"/>
     <use xlink:href="#MicrosoftYaHei-672a" x="290.136719"/>
     <use xlink:href="#MicrosoftYaHei-6765" x="390.136719"/>
     <use xlink:href="#MicrosoftYaHei-7814" x="490.136719"/>
     <use xlink:href="#MicrosoftYaHei-7a76" x="590.136719"/>
     <use xlink:href="#MicrosoftYaHei-65b9" x="690.136719"/>
     <use xlink:href="#MicrosoftYaHei-5411" x="790.136719"/>
     <use xlink:href="#MicrosoftYaHei-6846" x="890.136719"/>
     <use xlink:href="#MicrosoftYaHei-67b6" x="990.136719"/>
     <use xlink:href="#MicrosoftYaHei-56fe" x="1090.136719"/>
    </g>
   </g>
  </g>
  <g id="text_28">
   <!-- 注: 图中展示了加密流量异常检测技术的五个主要未来研究方向，每个方向包含四个具体研究课题 -->
   <g transform="translate(317.179688 851.758084) scale(0.12 -0.12)">
    <defs>
     <path id="MicrosoftYaHei-6ce8" d="M 1625 16 
L 3725 16 
L 3725 1791 
L 2100 1791 
L 2100 2266 
L 3725 2266 
L 3725 3778 
L 1925 3778 
L 1925 4253 
L 3919 4253 
Q 3625 4719 3319 5103 
L 3731 5366 
Q 4150 4878 4394 4503 
L 4003 4253 
L 6100 4253 
L 6100 3778 
L 4250 3778 
L 4250 2266 
L 5875 2266 
L 5875 1791 
L 4250 1791 
L 4250 16 
L 6275 16 
L 6275 -459 
L 1625 -459 
L 1625 16 
z
M 956 1816 
Q 1213 1703 1450 1609 
Q 1106 509 731 -791 
L 194 -603 
Q 588 466 956 1816 
z
M 631 5234 
Q 1238 4778 1713 4322 
Q 1500 4109 1338 3941 
Q 1000 4297 313 4891 
L 631 5234 
z
M 394 3616 
Q 844 3303 1500 2766 
Q 1269 2503 1131 2359 
Q 475 2959 81 3259 
L 394 3616 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-3a" d="M 372 3134 
Q 372 3303 490 3415 
Q 609 3528 775 3528 
Q 944 3528 1062 3414 
Q 1181 3300 1181 3134 
Q 1181 2969 1065 2853 
Q 950 2738 772 2738 
Q 603 2738 487 2853 
Q 372 2969 372 3134 
z
M 372 322 
Q 372 491 487 606 
Q 603 722 775 722 
Q 950 722 1065 605 
Q 1181 488 1181 322 
Q 1181 159 1065 42 
Q 950 -75 772 -75 
Q 600 -75 486 42 
Q 372 159 372 322 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-4e2d" d="M 525 3972 
L 2931 3972 
L 2931 5334 
L 3469 5334 
L 3469 3972 
L 5900 3972 
L 5900 1122 
L 5388 1122 
L 5388 1459 
L 3469 1459 
L 3469 -866 
L 2931 -866 
L 2931 1459 
L 1038 1459 
L 1038 1122 
L 525 1122 
L 525 3972 
z
M 5388 3509 
L 3469 3509 
L 3469 1922 
L 5388 1922 
L 5388 3509 
z
M 1038 1922 
L 2931 1922 
L 2931 3509 
L 1038 3509 
L 1038 1922 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-5c55" d="M 1463 2897 
L 2488 2897 
L 2488 3441 
L 2950 3441 
L 2950 2897 
L 4244 2897 
L 4244 3441 
L 4706 3441 
L 4706 2897 
L 5875 2897 
L 5875 2484 
L 4706 2484 
L 4706 1797 
L 6169 1797 
L 6169 1384 
L 3534 1384 
Q 3819 922 4228 588 
Q 4716 822 5494 1247 
L 5756 791 
Q 5097 516 4622 316 
Q 5322 -94 6300 -216 
Q 6106 -466 5950 -691 
Q 3900 -266 3047 1384 
L 2381 1384 
L 2381 -184 
Q 2463 -159 3625 209 
Q 3613 -28 3625 -291 
Q 3056 -453 2494 -653 
Q 2350 -709 2044 -859 
L 1769 -478 
Q 1919 -378 1919 -47 
L 1919 1384 
L 1178 1384 
Q 1125 103 456 -847 
Q 300 -634 100 -403 
Q 750 441 750 2341 
L 750 5028 
L 5850 5028 
L 5850 3384 
L 5375 3384 
L 5375 3597 
L 1225 3597 
L 1225 2766 
Q 1225 2234 1203 1797 
L 2488 1797 
L 2488 2484 
L 1463 2484 
L 1463 2897 
z
M 5375 4616 
L 1225 4616 
L 1225 4009 
L 5375 4009 
L 5375 4616 
z
M 2950 1797 
L 4244 1797 
L 4244 2484 
L 2950 2484 
L 2950 1797 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-793a" d="M 100 3072 
L 6300 3072 
L 6300 2603 
L 3619 2603 
L 3619 78 
Q 3619 -703 2800 -709 
Q 2366 -709 1825 -703 
Q 1788 -447 1731 -128 
Q 2225 -178 2663 -178 
Q 3088 -178 3088 241 
L 3088 2603 
L 100 2603 
L 100 3072 
z
M 675 4916 
L 5744 4916 
L 5744 4447 
L 675 4447 
L 675 4916 
z
M 4413 1847 
L 4831 2128 
Q 5600 1184 6319 159 
L 5863 -141 
Q 5088 991 4413 1847 
z
M 1481 2141 
L 1938 1884 
Q 1281 747 538 -278 
Q 344 -134 125 16 
Q 825 922 1481 2141 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-4e86" d="M 3038 2991 
Q 4106 3647 5025 4334 
L 331 4334 
L 331 4853 
L 5888 4853 
L 5888 4234 
Q 4769 3466 3600 2734 
L 3600 434 
Q 3600 -184 3363 -422 
Q 3131 -672 2506 -678 
Q 2163 -678 1488 -659 
Q 1450 -347 1388 -53 
Q 2013 -122 2463 -128 
Q 3038 -134 3038 441 
L 3038 2991 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-52a0" d="M 3663 4484 
L 6038 4484 
L 6038 -666 
L 5544 -666 
L 5544 -34 
L 4156 -34 
L 4156 -666 
L 3663 -666 
L 3663 4484 
z
M 231 4178 
L 1103 4178 
Q 1109 4681 1106 5297 
L 1600 5297 
Q 1600 4675 1594 4178 
L 3050 4178 
Q 3019 1778 2969 409 
Q 2944 -716 2119 -672 
Q 1875 -672 1400 -653 
Q 1356 -391 1300 -109 
Q 1738 -166 2013 -166 
Q 2431 -178 2469 428 
Q 2538 1909 2550 3703 
L 1588 3703 
Q 1581 3225 1569 2891 
Q 1544 447 394 -872 
Q 238 -678 25 -453 
Q 1063 684 1081 2941 
Q 1094 3269 1100 3703 
L 231 3703 
L 231 4178 
z
M 5544 4022 
L 4156 4022 
L 4156 416 
L 5544 416 
L 5544 4022 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-5bc6" d="M 63 1622 
Q 947 1872 1800 2228 
L 1800 3659 
L 2244 3659 
L 2244 2419 
Q 3594 3034 4850 3909 
L 5144 3534 
Q 3834 2656 2391 1997 
Q 2522 1872 2775 1872 
L 3975 1872 
Q 4350 1872 4425 2084 
Q 4506 2291 4550 2753 
Q 4756 2666 5025 2572 
Q 4931 2091 4888 1953 
Q 4756 1441 4094 1441 
L 2669 1441 
Q 2128 1441 1925 1794 
Q 1131 1459 300 1191 
Q 197 1419 63 1622 
z
M 5063 -809 
L 5063 -566 
L 850 -566 
L 850 1078 
L 1325 1078 
L 1325 -116 
L 2944 -116 
L 2944 1322 
L 3444 1322 
L 3444 -116 
L 5063 -116 
L 5063 1078 
L 5538 1078 
L 5538 -809 
L 5063 -809 
z
M 331 4672 
L 3009 4672 
Q 2894 4897 2750 5141 
L 3213 5359 
Q 3406 5041 3569 4672 
L 6075 4672 
L 6075 3559 
L 5569 3559 
L 5569 4222 
L 838 4222 
L 838 3559 
L 331 3559 
L 331 4672 
z
M 1063 3534 
L 1500 3366 
Q 1206 2684 863 2116 
Q 663 2216 406 2316 
Q 750 2809 1063 3534 
z
M 5075 3053 
L 5438 3322 
Q 5925 2791 6275 2322 
L 5863 2016 
Q 5531 2503 5075 3053 
z
M 2588 3847 
L 2913 4128 
Q 3331 3797 3656 3478 
L 3294 3141 
Q 2938 3528 2588 3847 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-6d41" d="M 5013 3741 
Q 5500 3178 6094 2372 
Q 5863 2228 5644 2078 
Q 5488 2306 5341 2516 
Q 3806 2447 2519 2378 
Q 2319 2372 2019 2303 
L 1806 2772 
Q 2038 2891 2244 3059 
Q 2728 3472 3206 4078 
L 1819 4078 
L 1819 4522 
L 3803 4522 
Q 3566 4891 3350 5166 
L 3763 5422 
Q 3994 5128 4288 4734 
L 3963 4522 
L 6088 4522 
L 6088 4078 
L 3325 4078 
L 3738 3903 
Q 3156 3278 2650 2853 
Q 3659 2884 5041 2934 
Q 4822 3228 4625 3478 
L 5013 3741 
z
M 4681 2109 
L 5150 2109 
L 5150 16 
Q 5150 -272 5375 -272 
L 5594 -272 
Q 5788 -272 5838 47 
Q 5881 384 5894 834 
Q 6113 734 6363 653 
Q 6319 128 6269 -153 
Q 6194 -709 5675 -709 
L 5288 -709 
Q 4681 -709 4681 22 
L 4681 2109 
z
M 2306 2084 
L 2781 2084 
Q 2781 716 2525 166 
Q 2269 -441 1488 -866 
Q 1344 -634 1169 -453 
Q 1881 -78 2075 409 
Q 2306 884 2306 2084 
z
M 275 -616 
Q 650 484 925 1809 
Q 1200 1691 1431 1616 
Q 1150 534 825 -784 
L 275 -616 
z
M 3538 2084 
L 4006 2084 
L 4006 -703 
L 3538 -703 
L 3538 2084 
z
M 81 3247 
L 394 3609 
Q 900 3259 1456 2803 
Q 1231 2541 1094 2397 
Q 444 2978 81 3247 
z
M 325 4878 
L 644 5209 
Q 1100 4847 1663 4347 
L 1294 3978 
Q 775 4503 325 4878 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-5f02" d="M 1731 2547 
L 5050 2547 
Q 5531 2547 5619 2878 
Q 5694 3141 5756 3572 
Q 5988 3472 6250 3378 
Q 6131 2866 6075 2684 
Q 5913 2109 5163 2109 
L 1619 2109 
Q 819 2109 819 2922 
L 819 5016 
L 5338 5016 
L 5338 3116 
L 4856 3116 
L 4856 3328 
L 1300 3328 
L 1300 2972 
Q 1300 2547 1731 2547 
z
M 4256 1922 
L 4750 1922 
L 4750 1178 
L 6250 1178 
L 6250 741 
L 4750 741 
L 4750 -841 
L 4256 -841 
L 4256 741 
L 2125 741 
Q 2050 -322 663 -866 
Q 513 -672 319 -441 
Q 1538 -22 1622 741 
L 144 741 
L 144 1178 
L 1678 1178 
Q 1703 1500 1700 1928 
L 2194 1928 
Q 2194 1509 2172 1178 
L 4256 1178 
L 4256 1922 
z
M 1300 4584 
L 1300 3759 
L 4856 3759 
L 4856 4584 
L 1300 4584 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-5e38" d="M 1213 -372 
L 725 -372 
L 725 1409 
L 2963 1409 
L 2963 1997 
L 1738 1997 
L 1738 1772 
L 1263 1772 
L 1263 3366 
L 5175 3366 
L 5175 1772 
L 4700 1772 
L 4700 1997 
L 3463 1997 
L 3463 1409 
L 5756 1409 
L 5756 341 
Q 5756 -378 5000 -378 
Q 4681 -378 4269 -372 
Q 4231 -153 4175 109 
Q 4556 72 4881 72 
Q 5269 72 5269 409 
L 5269 997 
L 3463 997 
L 3463 -853 
L 2963 -853 
L 2963 997 
L 1213 997 
L 1213 -372 
z
M 356 4216 
L 1828 4216 
Q 1409 4572 956 4903 
L 1244 5228 
Q 1713 4916 2188 4572 
L 1853 4216 
L 2956 4216 
L 2956 5309 
L 3456 5309 
L 3456 4216 
L 4488 4216 
L 4238 4528 
Q 4750 4866 5213 5247 
L 5575 4878 
Q 5128 4547 4631 4216 
L 6056 4216 
L 6056 3109 
L 5569 3109 
L 5569 3791 
L 844 3791 
L 844 3109 
L 356 3109 
L 356 4216 
z
M 4700 2966 
L 1738 2966 
L 1738 2397 
L 4700 2397 
L 4700 2966 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-6280" d="M 2394 2472 
L 2394 2884 
L 4000 2884 
L 4000 3841 
L 2369 3841 
L 2369 4297 
L 4000 4297 
L 4000 5303 
L 4500 5303 
L 4500 4297 
L 6200 4297 
L 6200 3841 
L 4500 3841 
L 4500 2884 
L 5831 2884 
L 5831 2509 
Q 5391 1259 4578 484 
Q 5281 -69 6319 -359 
Q 6081 -634 5919 -841 
Q 4903 -428 4197 163 
Q 3375 -456 2275 -891 
Q 2144 -691 1944 -447 
Q 3059 -84 3850 491 
Q 3156 1247 2809 2472 
L 2394 2472 
z
M 75 2009 
Q 547 2144 988 2275 
L 988 3622 
L 156 3622 
L 156 4053 
L 988 4053 
L 988 5266 
L 1469 5266 
L 1469 4053 
L 2200 4053 
L 2200 3622 
L 1469 3622 
L 1469 2425 
Q 1828 2541 2169 2653 
Q 2169 2353 2181 2166 
Q 1828 2053 1469 1938 
L 1469 -78 
Q 1469 -741 844 -747 
Q 594 -753 244 -747 
Q 206 -484 163 -216 
Q 531 -253 719 -253 
Q 988 -253 988 28 
L 988 1778 
Q 566 1638 138 1491 
L 75 2009 
z
M 5288 2472 
L 3291 2472 
Q 3572 1484 4228 803 
Q 4966 1503 5288 2472 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-672f" d="M 6350 409 
Q 6125 153 5950 -91 
Q 4391 1013 3456 3025 
L 3456 -841 
L 2956 -841 
L 2956 3038 
Q 2081 1163 400 -172 
Q 256 28 38 303 
Q 1716 1519 2713 3353 
L 238 3353 
L 238 3816 
L 2956 3816 
L 2956 5328 
L 3456 5328 
L 3456 3816 
L 6188 3816 
L 6188 3353 
L 3684 3353 
Q 4725 1316 6350 409 
z
M 4069 4884 
L 4388 5234 
Q 4931 4866 5463 4416 
L 5113 4028 
Q 4594 4491 4069 4884 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-7684" d="M 356 4284 
L 988 4284 
Q 1219 4853 1369 5322 
L 1894 5172 
Q 1697 4694 1513 4284 
L 2763 4284 
L 2763 -691 
L 2263 -691 
L 2263 -203 
L 856 -203 
L 856 -734 
L 356 -734 
L 356 4284 
z
M 2856 3003 
Q 3444 3934 3850 5322 
L 4375 5191 
Q 4216 4719 4050 4303 
L 6063 4303 
Q 5988 1091 5938 259 
Q 5894 -722 4906 -722 
Q 4338 -722 3750 -684 
Q 3713 -372 3650 -128 
L 3669 -128 
Q 4294 -216 4813 -216 
Q 5381 -216 5425 384 
Q 5494 1316 5525 3834 
L 3850 3834 
Q 3578 3231 3288 2772 
Q 3119 2891 2856 3003 
z
M 856 266 
L 2263 266 
L 2263 1847 
L 856 1847 
L 856 266 
z
M 2263 3816 
L 856 3816 
L 856 2297 
L 2263 2297 
L 2263 3816 
z
M 3406 2578 
L 3788 2859 
Q 4481 2041 4894 1441 
L 4431 1116 
Q 3988 1841 3406 2578 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-4e94" d="M 125 -34 
L 1581 -34 
L 1944 2416 
L 519 2416 
L 519 2891 
L 2019 2891 
L 2244 4409 
L 350 4409 
L 350 4884 
L 6013 4884 
L 6013 4409 
L 2769 4409 
L 2544 2891 
L 5294 2891 
L 5294 -34 
L 6306 -34 
L 6306 -503 
L 125 -503 
L 125 -34 
z
M 4769 2416 
L 2475 2416 
L 2106 -34 
L 4769 -34 
L 4769 2416 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-4e2a" d="M 2913 3028 
L 3481 3028 
L 3481 -841 
L 2913 -841 
L 2913 3028 
z
M 3600 5178 
L 3506 5034 
Q 4519 3366 6319 2647 
Q 6125 2341 5988 2084 
Q 4150 3047 3206 4566 
Q 2281 3122 400 1997 
Q 263 2222 81 2491 
Q 2031 3572 2981 5178 
L 3600 5178 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-4e3b" d="M 150 9 
L 2931 9 
L 2931 1809 
L 644 1809 
L 644 2284 
L 2931 2284 
L 2931 3709 
L 400 3709 
L 400 4184 
L 5975 4184 
L 5975 3709 
L 3469 3709 
L 3469 2284 
L 5756 2284 
L 5756 1809 
L 3469 1809 
L 3469 9 
L 6250 9 
L 6250 -453 
L 150 -453 
L 150 9 
z
M 2694 5109 
L 3156 5347 
Q 3538 4778 3706 4466 
L 3213 4203 
Q 2875 4828 2694 5109 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-8981" d="M 613 3978 
L 2181 3978 
L 2181 4659 
L 313 4659 
L 313 5084 
L 6088 5084 
L 6088 4659 
L 4213 4659 
L 4213 3978 
L 5781 3978 
L 5781 2116 
L 5319 2116 
L 5319 2353 
L 1075 2353 
L 1075 2116 
L 613 2116 
L 613 3978 
z
M 94 1741 
L 1959 1741 
Q 2150 2038 2319 2341 
L 2800 2147 
Q 2663 1941 2528 1741 
L 6300 1741 
L 6300 1316 
L 4856 1316 
Q 4497 681 3925 247 
Q 4853 -31 5875 -347 
L 5600 -834 
Q 4550 -469 3394 -81 
Q 2300 -650 531 -872 
Q 419 -641 288 -391 
Q 1809 -253 2750 134 
Q 1991 388 1188 653 
Q 1447 981 1675 1316 
L 94 1316 
L 94 1741 
z
M 1900 853 
Q 2584 650 3328 428 
Q 3938 797 4269 1316 
L 2231 1316 
Q 2066 1078 1900 853 
z
M 2644 2753 
L 3750 2753 
L 3750 3578 
L 2644 3578 
L 2644 2753 
z
M 5319 3578 
L 4213 3578 
L 4213 2753 
L 5319 2753 
L 5319 3578 
z
M 1075 2753 
L 2181 2753 
L 2181 3578 
L 1075 3578 
L 1075 2753 
z
M 2644 3978 
L 3750 3978 
L 3750 4659 
L 2644 4659 
L 2644 3978 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-ff0c" d="M 1931 775 
L 1403 -891 
L 966 -891 
L 1350 775 
L 1931 775 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-6bcf" d="M 125 2203 
L 1044 2203 
L 1325 3766 
L 5456 3766 
Q 5422 2928 5391 2203 
L 6300 2203 
L 6300 1753 
L 5372 1753 
Q 5344 1141 5319 616 
L 6075 616 
L 6075 178 
L 5284 178 
Q 5222 -278 5031 -484 
Q 4788 -753 4181 -753 
Q 3831 -753 3288 -728 
Q 3256 -516 3194 -222 
Q 3838 -278 4119 -278 
Q 4513 -278 4638 -128 
Q 4722 -41 4766 178 
L 763 178 
L 763 647 
L 963 1753 
L 125 1753 
L 125 2203 
z
M 4866 1753 
L 1472 1753 
L 1275 616 
L 4813 616 
Q 4844 1172 4866 1753 
z
M 4925 3328 
L 1750 3328 
L 1553 2203 
L 4888 2203 
Q 4906 2753 4925 3328 
z
M 100 3522 
Q 1025 4309 1550 5359 
L 2113 5359 
Q 1966 5069 1803 4803 
L 6156 4803 
L 6156 4353 
L 1509 4353 
Q 1022 3659 413 3134 
Q 281 3316 100 3522 
z
M 2619 2978 
L 2913 3303 
Q 3388 2934 3750 2622 
L 3413 2247 
Q 3025 2628 2619 2978 
z
M 2569 1391 
L 2863 1716 
Q 3338 1347 3700 1034 
L 3369 666 
Q 2888 1116 2569 1391 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-5305" d="M 1663 5334 
L 2175 5197 
Q 2038 4872 1888 4566 
L 5488 4566 
Q 5463 2334 5388 1478 
Q 5313 441 4356 447 
Q 4106 447 3469 472 
Q 3431 709 3369 991 
Q 3888 941 4288 941 
Q 4856 934 4900 1547 
Q 4969 2341 4988 4116 
L 1656 4116 
Q 1438 3716 1188 3347 
L 3788 3347 
L 3788 1522 
L 1525 1522 
L 1525 347 
Q 1525 -147 2044 -147 
L 4956 -147 
Q 5550 -147 5650 234 
Q 5744 578 5800 1153 
Q 6069 1047 6300 972 
Q 6225 416 6138 66 
Q 5981 -628 5075 -628 
L 1938 -628 
Q 1025 -628 1025 259 
L 1025 3103 
Q 719 2684 375 2309 
Q 231 2559 63 2747 
Q 1150 3928 1663 5334 
z
M 3300 2922 
L 1525 2922 
L 1525 1947 
L 3300 1947 
L 3300 2922 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-542b" d="M 975 1572 
L 5438 1572 
L 5438 -822 
L 4950 -822 
L 4950 -478 
L 1463 -478 
L 1463 -834 
L 975 -834 
L 975 1572 
z
M 1188 2622 
L 1188 3053 
L 4950 3053 
L 4950 2622 
Q 4550 2147 3981 1628 
Q 3800 1784 3594 1922 
Q 3981 2228 4350 2622 
L 1188 2622 
z
M 2631 3872 
L 2975 4134 
Q 3288 3809 3619 3422 
L 3244 3128 
Q 2975 3484 2631 3872 
z
M 3663 5291 
L 3519 5134 
Q 4525 3909 6313 3259 
Q 6100 2991 5988 2778 
Q 4094 3622 3206 4834 
Q 2050 3516 369 2672 
Q 244 2903 81 3109 
Q 1838 3934 2981 5291 
L 3663 5291 
z
M 4950 -34 
L 4950 1128 
L 1463 1128 
L 1463 -34 
L 4950 -34 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-56db" d="M 581 4859 
L 5963 4859 
L 5963 -803 
L 5438 -803 
L 5438 -428 
L 1106 -428 
L 1106 -803 
L 581 -803 
L 581 4859 
z
M 4625 1747 
Q 4919 1747 5356 1759 
L 5263 1247 
L 4544 1247 
Q 3713 1247 3713 2003 
L 3713 4359 
L 2819 4359 
Q 2809 2597 2613 2041 
Q 2406 1341 1513 766 
Q 1350 959 1125 1166 
Q 1969 1672 2131 2259 
Q 2297 2666 2306 4359 
L 1106 4359 
L 1106 59 
L 5438 59 
L 5438 4359 
L 4225 4359 
L 4225 2072 
Q 4225 1747 4625 1747 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-5177" d="M 94 1022 
L 956 1022 
L 956 5041 
L 5438 5041 
L 5438 1022 
L 6288 1022 
L 6288 572 
L 94 572 
L 94 1022 
z
M 1438 1022 
L 4956 1022 
L 4956 1609 
L 1438 1609 
L 1438 1022 
z
M 4956 4591 
L 1438 4591 
L 1438 4016 
L 4956 4016 
L 4956 4591 
z
M 1438 3022 
L 4956 3022 
L 4956 3597 
L 1438 3597 
L 1438 3022 
z
M 1438 2028 
L 4956 2028 
L 4956 2603 
L 1438 2603 
L 1438 2028 
z
M 3719 -9 
L 3981 403 
Q 5075 72 6200 -347 
L 5956 -841 
Q 5106 -466 3719 -9 
z
M 2319 441 
L 2619 28 
Q 1519 -478 400 -872 
Q 306 -666 169 -403 
Q 1131 -116 2319 441 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-4f53" d="M 6350 778 
Q 6138 522 5981 316 
Q 4731 1278 4144 3572 
L 4144 934 
L 5063 934 
L 5063 484 
L 4144 484 
L 4144 -822 
L 3681 -822 
L 3681 484 
L 2763 484 
L 2763 934 
L 3681 934 
L 3681 3572 
L 3666 3572 
Q 3084 1413 1750 216 
Q 1606 397 1419 609 
Q 2625 1713 3225 3572 
L 1756 3572 
L 1756 4022 
L 3681 4022 
L 3681 5297 
L 4144 5297 
L 4144 4022 
L 6138 4022 
L 6138 3572 
L 4547 3572 
Q 5144 1628 6350 778 
z
M 56 2297 
Q 963 3622 1406 5316 
L 1881 5178 
Q 1638 4381 1331 3681 
L 1331 -816 
L 869 -816 
L 869 2741 
Q 588 2231 269 1784 
Q 175 2041 56 2297 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-8bfe" d="M 6325 -3 
Q 6169 -241 6019 -472 
Q 4884 119 4188 1209 
L 4188 -816 
L 3738 -816 
L 3738 1191 
Q 2953 78 1975 -559 
Q 1794 -328 1613 -147 
Q 2766 469 3513 1409 
L 1819 1409 
L 1819 1822 
L 3738 1822 
L 3738 2503 
L 2575 2503 
L 2575 2303 
L 2138 2303 
L 2138 5053 
L 5788 5053 
L 5788 2303 
L 5350 2303 
L 5350 2503 
L 4188 2503 
L 4188 1822 
L 6150 1822 
L 6150 1409 
L 4413 1409 
Q 5184 425 6325 -3 
z
M 2088 747 
Q 1619 334 1275 -3 
Q 1100 -184 963 -353 
L 613 53 
Q 806 234 806 591 
L 806 2778 
L 113 2778 
L 113 3234 
L 1269 3234 
L 1269 634 
Q 1581 909 1969 1284 
Q 2019 1016 2088 747 
z
M 5350 4653 
L 4188 4653 
L 4188 3972 
L 5350 3972 
L 5350 4653 
z
M 4188 2903 
L 5350 2903 
L 5350 3584 
L 4188 3584 
L 4188 2903 
z
M 2575 3972 
L 3738 3972 
L 3738 4653 
L 2575 4653 
L 2575 3972 
z
M 2575 2903 
L 3738 2903 
L 3738 3584 
L 2575 3584 
L 2575 2903 
z
M 800 5178 
Q 1363 4634 1725 4184 
L 1338 3866 
Q 900 4428 469 4884 
L 800 5178 
z
" transform="scale(0.015625)"/>
     <path id="MicrosoftYaHei-9898" d="M 88 -291 
Q 544 328 581 1622 
L 1013 1622 
Q 994 1234 950 884 
Q 1181 466 1538 209 
L 1538 1916 
L 138 1916 
L 138 2322 
L 3025 2322 
L 3025 1916 
L 1950 1916 
L 1950 1253 
L 2956 1253 
L 2956 847 
L 1950 847 
L 1950 -16 
Q 2369 -184 2906 -197 
Q 4006 -266 6300 -166 
Q 6231 -372 6125 -659 
Q 3888 -716 2906 -634 
Q 1513 -597 850 347 
Q 688 -359 363 -878 
Q 238 -584 88 -291 
z
M 525 5041 
L 2725 5041 
L 2725 2634 
L 2313 2634 
L 2313 2828 
L 938 2828 
L 938 2634 
L 525 2634 
L 525 5041 
z
M 3263 3878 
L 4169 3878 
Q 4231 4247 4275 4591 
L 3031 4591 
L 3031 4997 
L 6169 4997 
L 6169 4591 
L 4738 4591 
Q 4681 4222 4613 3878 
L 5838 3878 
L 5838 1228 
L 5425 1228 
L 5425 3472 
L 3675 3472 
L 3675 1228 
L 3263 1228 
L 3263 3878 
z
M 4344 3203 
L 4756 3203 
L 4756 2422 
Q 4756 1809 4631 1372 
Q 5594 678 6050 284 
L 5713 -47 
Q 5213 422 4481 972 
Q 4444 903 4406 841 
Q 4031 241 3131 -109 
Q 2988 72 2794 259 
Q 3669 547 4006 1028 
Q 4344 1503 4344 2441 
L 4344 3203 
z
M 938 3203 
L 2313 3203 
L 2313 3753 
L 938 3753 
L 938 3203 
z
M 2313 4666 
L 938 4666 
L 938 4122 
L 2313 4122 
L 2313 4666 
z
" transform="scale(0.015625)"/>
    </defs>
    <use xlink:href="#MicrosoftYaHei-6ce8"/>
    <use xlink:href="#MicrosoftYaHei-3a" x="100"/>
    <use xlink:href="#MicrosoftYaHei-20" x="124.072266"/>
    <use xlink:href="#MicrosoftYaHei-56fe" x="153.662109"/>
    <use xlink:href="#MicrosoftYaHei-4e2d" x="253.662109"/>
    <use xlink:href="#MicrosoftYaHei-5c55" x="353.662109"/>
    <use xlink:href="#MicrosoftYaHei-793a" x="453.662109"/>
    <use xlink:href="#MicrosoftYaHei-4e86" x="553.662109"/>
    <use xlink:href="#MicrosoftYaHei-52a0" x="653.662109"/>
    <use xlink:href="#MicrosoftYaHei-5bc6" x="753.662109"/>
    <use xlink:href="#MicrosoftYaHei-6d41" x="853.662109"/>
    <use xlink:href="#MicrosoftYaHei-91cf" x="953.662109"/>
    <use xlink:href="#MicrosoftYaHei-5f02" x="1053.662109"/>
    <use xlink:href="#MicrosoftYaHei-5e38" x="1153.662109"/>
    <use xlink:href="#MicrosoftYaHei-68c0" x="1253.662109"/>
    <use xlink:href="#MicrosoftYaHei-6d4b" x="1353.662109"/>
    <use xlink:href="#MicrosoftYaHei-6280" x="1453.662109"/>
    <use xlink:href="#MicrosoftYaHei-672f" x="1553.662109"/>
    <use xlink:href="#MicrosoftYaHei-7684" x="1653.662109"/>
    <use xlink:href="#MicrosoftYaHei-4e94" x="1753.662109"/>
    <use xlink:href="#MicrosoftYaHei-4e2a" x="1853.662109"/>
    <use xlink:href="#MicrosoftYaHei-4e3b" x="1953.662109"/>
    <use xlink:href="#MicrosoftYaHei-8981" x="2053.662109"/>
    <use xlink:href="#MicrosoftYaHei-672a" x="2153.662109"/>
    <use xlink:href="#MicrosoftYaHei-6765" x="2253.662109"/>
    <use xlink:href="#MicrosoftYaHei-7814" x="2353.662109"/>
    <use xlink:href="#MicrosoftYaHei-7a76" x="2453.662109"/>
    <use xlink:href="#MicrosoftYaHei-65b9" x="2553.662109"/>
    <use xlink:href="#MicrosoftYaHei-5411" x="2653.662109"/>
    <use xlink:href="#MicrosoftYaHei-ff0c" x="2753.662109"/>
    <use xlink:href="#MicrosoftYaHei-6bcf" x="2853.662109"/>
    <use xlink:href="#MicrosoftYaHei-4e2a" x="2953.662109"/>
    <use xlink:href="#MicrosoftYaHei-65b9" x="3053.662109"/>
    <use xlink:href="#MicrosoftYaHei-5411" x="3153.662109"/>
    <use xlink:href="#MicrosoftYaHei-5305" x="3253.662109"/>
    <use xlink:href="#MicrosoftYaHei-542b" x="3353.662109"/>
    <use xlink:href="#MicrosoftYaHei-56db" x="3453.662109"/>
    <use xlink:href="#MicrosoftYaHei-4e2a" x="3553.662109"/>
    <use xlink:href="#MicrosoftYaHei-5177" x="3653.662109"/>
    <use xlink:href="#MicrosoftYaHei-4f53" x="3753.662109"/>
    <use xlink:href="#MicrosoftYaHei-7814" x="3853.662109"/>
    <use xlink:href="#MicrosoftYaHei-7a76" x="3953.662109"/>
    <use xlink:href="#MicrosoftYaHei-8bfe" x="4053.662109"/>
    <use xlink:href="#MicrosoftYaHei-9898" x="4153.662109"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p9e98fc1fd4">
   <rect x="7.2" y="44.485931" width="1130.4" height="805.112153"/>
  </clipPath>
 </defs>
</svg>
