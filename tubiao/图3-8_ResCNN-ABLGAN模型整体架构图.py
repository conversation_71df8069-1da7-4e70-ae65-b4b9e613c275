import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Rectangle, FancyArrowPatch, FancyBboxPatch
import matplotlib.font_manager as fm
from matplotlib.font_manager import FontProperties
import os

# 设置字体
plt.rcParams["font.family"] = ["SimSun", "Times New Roman"]  # 中文宋体，英文Times New Roman
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 创建图表
fig, ax = plt.subplots(figsize=(16, 12))

# 定义颜色
colors = {
    'input': '#3498db',
    'rescnn': '#e74c3c',
    'bilstm': '#9b59b6',
    'attention': '#2ecc71',
    'gan': '#f39c12',
    'output': '#1abc9c',
    'arrow': '#7f8c8d',
    'residual': '#e67e22'
}

# 定义模块位置和大小（调整输出层位置）
modules = {
    'input': {'x': 1, 'y': 10, 'width': 12, 'height': 1.5, 
              'label': '输入层\n[批次, 时间步长, 特征维度]\n(64, 100, 64)'},
    'rescnn': {'x': 1, 'y': 7.5, 'width': 12, 'height': 1.5, 
              'label': '残差CNN模块\n3层残差块, 卷积核:3×1, 滤波器:32→64→128\n激活:LeakyReLU, Dropout:0.25'},
    'bilstm': {'x': 1, 'y': 5, 'width': 12, 'height': 1.5, 
              'label': '双向LSTM\n隐藏单元:128×2, Dropout:0.25'},
    'attention': {'x': 1, 'y': 2.5, 'width': 12, 'height': 1.5, 
                 'label': '稀疏注意力机制\n头数:4, Top-K:10, 维度:64'},
    'gan_gen': {'x': 14, 'y': 8.5, 'width': 1.8, 'height': 1, 
               'label': 'GAN生成器\n输入:噪声向量(100)\n输出:伪造特征'},
    'gan_disc': {'x': 14, 'y': 6.5, 'width': 1.8, 'height': 1, 
                'label': 'GAN判别器\n输入:真实/伪造特征\n输出:真假概率'},
    'output1': {'x': 1, 'y': 0, 'width': 3, 'height': 1.5, 
               'label': '异常检测头\n二分类\n焦点损失(γ=2)'},
    'output2': {'x': 5, 'y': 0, 'width': 4, 'height': 1.5, 
               'label': '加密类型识别头\n六分类\n交叉熵损失'},
    'output3': {'x': 10, 'y': 0, 'width': 3, 'height': 1.5, 
               'label': '攻击类型分类头\n八分类\n加权交叉熵(β=5)'}
}

# 绘制模块
for name, module in modules.items():
    if 'output' in name:
        color = colors['output']
    elif 'rescnn' in name:
        color = colors['rescnn']
    elif 'gan' in name:
        color = colors['gan']
    else:
        color = colors[name]

    box = FancyBboxPatch(
        (module['x'], module['y']),
        module['width'], module['height'],
        boxstyle=f"round,pad=0.5,rounding_size=0.2",
        fc=color, ec='black', alpha=0.8, lw=2
    )
    ax.add_patch(box)

    # 添加标签（使用5号字体，约10.5磅）
    ax.text(
        module['x'] + module['width']/2,
        module['y'] + module['height']/2,
        module['label'],
        ha='center', va='center',
        color='white', fontsize=13, fontweight='bold'
    )

# 绘制连接箭头（修复输出层连接的计算）
arrows = [
    {'start': 'input', 'end': 'rescnn', 'label': ''},
    {'start': 'rescnn', 'end': 'bilstm', 'label': ''},
    {'start': 'bilstm', 'end': 'attention', 'label': ''},
    {'start': 'attention', 'end': 'output1', 'label': ''},
    {'start': 'attention', 'end': 'output2', 'label': ''},
    {'start': 'attention', 'end': 'output3', 'label': ''},
    {'start': 'rescnn', 'end': 'gan_gen', 'label': '特征向量', 'style': 'solid'},
    {'start': 'gan_gen', 'end': 'gan_disc', 'label': '伪造特征', 'style': 'dashed'},
    {'start': 'rescnn', 'end': 'gan_disc', 'label': '真实特征', 'style': 'solid'},
    {'start': 'gan_gen', 'end': 'bilstm', 'label': '增强特征', 'style': 'dashed'},
]

for arrow in arrows:
    start = modules[arrow['start']]
    end = modules[arrow['end']]

    # 计算起点和终点（统一输出层连接的计算方式）
    if 'rescnn' in arrow['start'] and 'gan' in arrow['end']:
        start_x = start['x'] + start['width']
        start_y = start['y'] + start['height']/2
        end_x = end['x']
        end_y = end['y'] + end['height']/2
    elif 'gan' in arrow['start'] and 'bilstm' in arrow['end']:
        start_x = end['x'] + 1.2
        start_y = start['y'] + 0.3
        end_x = end['x'] + 4.8
        end_y = end['y'] + end['height'] - 0.5
    elif 'attention' == arrow['start'] and 'output' in arrow['end']:
        output_idx = int(arrow['end'][-1]) - 1
        # 从注意力层底部中心均匀分布箭头
        start_x = start['x'] + start['width'] * (0.2 + output_idx * 0.3)
        start_y = start['y']
        end_x = end['x'] + end['width']/2
        end_y = end['y'] + end['height']
    else:
        start_x = start['x'] + start['width']/2
        start_y = start['y']
        end_x = end['x'] + end['width']/2
        end_y = end['y'] + end['height']

    # 绘制箭头
    style = arrow.get('style', 'solid')
    arrow_obj = FancyArrowPatch(
        (start_x, start_y),
        (end_x, end_y),
        connectionstyle=f"arc3,rad=0.0",
        arrowstyle="-|>",
        color=colors['arrow'],
        lw=2,
        linestyle=style
    )
    ax.add_patch(arrow_obj)

    # 添加标签（使用5号字体）
    if arrow['label']:
        mid_x = (start_x + end_x) / 2
        mid_y = (start_y + end_y) / 2
        ax.text(
            mid_x, mid_y,
            arrow['label'],
            ha='center', va='center',
            color='black', fontsize=13, fontweight='bold',
            bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=2)
        )

# 添加残差连接
res_arrow = FancyArrowPatch(
    (modules['input']['x'] + 1, modules['input']['y']),
    (modules['bilstm']['x'] + 1, modules['bilstm']['y'] + modules['bilstm']['height']),
    connectionstyle=f"arc3,rad=-0.3",
    arrowstyle="-|>",
    color=colors['residual'],
    lw=2,
    linestyle='dashed'
)
ax.add_patch(res_arrow)
ax.text(
    modules['input']['x'] - 0.5, modules['rescnn']['y'] + modules['rescnn']['height']/2,
    '残差连接',
    ha='center', va='center',
    color='black', fontsize=13, fontweight='bold', rotation=90,
    bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=2)
)

# 设置图表范围和标题（使用5号字体）
ax.set_xlim(0, 18)
ax.set_ylim(-2, 12)  # 调整y轴范围，确保输出层可见

# 隐藏坐标轴
ax.axis('off')

# 调整布局
plt.tight_layout()

# 保存图表（提高DPI以确保字体清晰）
plt.savefig('ResCNNABLGAN模型架构图.png', dpi=300, bbox_inches='tight')
plt.savefig('ResCNNABLGAN模型架构图.pdf', bbox_inches='tight')

# 显示图表
plt.show()    