%%{init: { 'themeVariables': { 'fontFamily': 'SimSun, Times New Roman', 'fontSize': '10.5pt', 'lineColor': '#000000', 'textColor': '#000000' } }}%%
flowchart TD
    绪论[第一章 绪论] --> 背景[研究背景与意义：加密流量普及、异常检测必要性、深度学习应用前景]
    绪论 --> 现状[国内外研究现状：传统检测技术、深度学习在网络安全进展]
    绪论 --> 内容[研究内容与创新点：研究目标、内容、技术路线]

    理论[第二章 加密流量异常检测基础理论] --> 加密[加密流量基础：加密通信原理、常见协议分析、特征与异常类型]
    理论 --> 学习[深度学习理论：神经网络原理、CNN与LSTM、注意力机制与残差网络、GAN]
    理论 --> 提取[特征提取方法]

    模型[第三章 基于深度学习的加密流量异常检测模型设计] --> 架构[系统总体架构：功能需求、架构设计、关键模块]
    模型 --> 预处理[数据预处理：流量捕获解析、清洗标准化、特征提取]
    模型 --> 设计[ResCNN-ABLGAN模型：整体架构、组件设计、多任务学习框架]
    模型 --> 训练[模型训练与优化：损失函数、优化算法、增量学习]

    测试[第四章 系统实现与测试] --> 环境[开发环境与技术栈]
    测试 --> 数据[数据采集与预处理实现]
    测试 --> 部署[模型实现与部署]
    测试 --> 功能[系统功能：实时监控、异常检测、加密流量分析]
    测试 --> 评估[实验结果分析：模型性能、系统实用价值]

    结论[第五章 结论与展望] --> 总结[研究成果总结]
    结论 --> 局限[存在问题与局限性]
    结论 --> 方向[未来研究方向：模型优化、特征工程、系统功能扩展]

    绪论 --> 理论 --> 模型 --> 测试 --> 结论