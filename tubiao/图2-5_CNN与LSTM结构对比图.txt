图2-5 CNN与LSTM结构对比图

[此处为图表内容]
- 图表类型：对比示意图
- 左侧：CNN结构
  * 输入层：一维时间序列数据
  * 卷积层：多个卷积核（不同大小）滑动提取局部特征
  * 池化层：最大池化或平均池化，降维
  * 全连接层：特征映射到输出
  * 特点：局部特征提取，平移不变性，参数共享
  
- 右侧：LSTM结构
  * 输入层：时间序列数据
  * LSTM单元：包含输入门、遗忘门、输出门和记忆单元
  * 隐藏状态：传递时序信息
  * 输出层：序列输出或最终状态输出
  * 特点：长期依赖建模，序列处理，门控机制

- 中间：对比说明
  * CNN优势：局部特征提取能力强，计算并行性好
  * LSTM优势：时序依赖建模能力强，记忆长期信息
  * 在加密流量分析中的应用场景对比

- 使用不同颜色和图形元素区分不同组件
- 添加数据流向箭头和简要说明
