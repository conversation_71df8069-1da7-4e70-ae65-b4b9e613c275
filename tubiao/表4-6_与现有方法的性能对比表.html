
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>表4-6 与现有方法的性能对比表</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
            }
            table {
                border-collapse: collapse;
                width: 100%;
                margin-top: 20px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 12px;
                text-align: center;
            }
            th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            tr:nth-child(even) {
                background-color: #f9f9f9;
            }
            tr:hover {
                background-color: #f5f5f5;
            }
            .highlight {
                background-color: #e8f4f8;
                font-weight: bold;
            }
            h1 {
                text-align: center;
            }
        </style>
    </head>
    <body>
        <h1>表4-6 与现有方法的性能对比表</h1>
        <table border="1" class="dataframe">
  <thead>
    <tr style="text-align: right;">
      <th>模型</th>
      <th>准确率 (%)</th>
      <th>精确率 (%)</th>
      <th>召回率 (%)</th>
      <th>F1分数</th>
      <th>AUC</th>
      <th>训练时间 (相对)</th>
      <th>推理速度 (相对)</th>
      <th>参数量 (百万)</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>CNN</td>
      <td>92.3</td>
      <td>91.5</td>
      <td>91.9</td>
      <td>0.918</td>
      <td>0.957</td>
      <td>1.0×</td>
      <td>1.0×</td>
      <td>2.5</td>
    </tr>
    <tr>
      <td>LSTM</td>
      <td>93.5</td>
      <td>92.7</td>
      <td>93.1</td>
      <td>0.929</td>
      <td>0.963</td>
      <td>1.2×</td>
      <td>0.9×</td>
      <td>3.8</td>
    </tr>
    <tr>
      <td>CNN-LSTM</td>
      <td>95.1</td>
      <td>94.3</td>
      <td>94.8</td>
      <td>0.947</td>
      <td>0.975</td>
      <td>1.5×</td>
      <td>0.8×</td>
      <td>5.2</td>
    </tr>
    <tr>
      <td>Attention-LSTM</td>
      <td>96.2</td>
      <td>95.5</td>
      <td>96.0</td>
      <td>0.958</td>
      <td>0.981</td>
      <td>1.7×</td>
      <td>0.7×</td>
      <td>6.5</td>
    </tr>
    <tr>
      <td>ResNet</td>
      <td>94.8</td>
      <td>94.0</td>
      <td>94.5</td>
      <td>0.942</td>
      <td>0.973</td>
      <td>1.3×</td>
      <td>0.9×</td>
      <td>4.7</td>
    </tr>
    <tr>
      <td>GAN-based</td>
      <td>95.5</td>
      <td>94.8</td>
      <td>95.2</td>
      <td>0.950</td>
      <td>0.978</td>
      <td>2.2×</td>
      <td>0.6×</td>
      <td>7.8</td>
    </tr>
    <tr>
      <td><span class="highlight">ResCNN-ABLGAN (ours)</span></td>
      <td>98.7</td>
      <td>97.9</td>
      <td>98.3</td>
      <td>0.981</td>
      <td>0.995</td>
      <td>2.0×</td>
      <td>0.6×</td>
      <td>8.5</td>
    </tr>
    <tr>
      <td><span class="highlight">ResCNN-ABLGAN-Light (ours)</span></td>
      <td>97.5</td>
      <td>96.8</td>
      <td>97.2</td>
      <td>0.970</td>
      <td>0.989</td>
      <td>1.6×</td>
      <td>0.8×</td>
      <td>4.2</td>
    </tr>
  </tbody>
</table>
    </body>
    </html>
    