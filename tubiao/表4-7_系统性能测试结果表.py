import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import matplotlib.font_manager as fm
from matplotlib.font_manager import FontProperties
import os
import seaborn as sns

# 检查是否有支持中文的字体
font_path = None
for font in fm.findSystemFonts(fontpaths=None, fontext='ttf'):
    if os.path.basename(font) == 'SimHei.ttf' or os.path.basename(font) == 'simhei.ttf':
        font_path = font
        break
    elif os.path.basename(font) == 'Microsoft YaHei.ttf' or 'msyh.ttf' in os.path.basename(font).lower():
        font_path = font
        break

if font_path:
    font_prop = FontProperties(fname=font_path)
    plt.rcParams['font.family'] = font_prop.get_name()
else:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
    
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 创建数据
data = {
    '测试环境': ['高性能服务器', '标准服务器', '边缘设备', '虚拟化环境', '容器化部署', '集群部署 (3节点)'],
    '处理速度': ['10 Gbps', '2.5 Gbps', '100 Mbps', '1.5 Gbps', '2.2 Gbps', '25 Gbps'],
    '内存使用': ['12 GB', '8 GB', '2 GB', '6 GB', '5 GB', '30 GB'],
    'CPU利用率': ['45%', '65%', '80%', '70%', '68%', '55%'],
    'GPU利用率': ['60%', '75%', 'N/A', '65%', '70%', '65%'],
    '延迟 (ms)': [8.6, 19.3, 67.4, 25.7, 22.1, 12.3],
    '最大并发会话': [500000, 120000, 5000, 80000, 100000, 1200000],
    '稳定性 (%)': [99.99, 99.95, 99.90, 99.93, 99.94, 99.98]
}

# 创建DataFrame
df = pd.DataFrame(data)

# 保存为CSV
df.to_csv('tubiao/表4-7_系统性能测试结果表.csv', index=False, encoding='utf-8-sig')

# 创建延迟详细分布数据
delay_data = {
    '处理阶段': ['数据捕获', '特征提取', '模型推理', '总延迟'],
    '高性能服务器 (ms)': [0.5, 2.3, 5.8, 8.6],
    '标准服务器 (ms)': [1.2, 5.7, 12.4, 19.3],
    '边缘设备 (ms)': [3.5, 18.2, 45.7, 67.4]
}

# 创建延迟DataFrame
df_delay = pd.DataFrame(delay_data)

# 保存为CSV
df_delay.to_csv('tubiao/表4-7_系统性能测试结果表_延迟详细.csv', index=False, encoding='utf-8-sig')

# 创建表格可视化
fig, ax = plt.subplots(figsize=(16, 10))
ax.axis('tight')
ax.axis('off')

# 创建表格
table = ax.table(cellText=df.values, colLabels=df.columns, loc='center', 
                cellLoc='center', rowLoc='center')

# 设置表格样式
table.auto_set_font_size(False)
table.set_fontsize(12)
table.scale(1, 1.5)  # 调整表格大小

# 设置标题
plt.title('表4-7 系统性能测试结果表', fontsize=20, pad=20)

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig('tubiao/表4-7_系统性能测试结果表.png', dpi=300, bbox_inches='tight')
plt.savefig('tubiao/表4-7_系统性能测试结果表.pdf', bbox_inches='tight')

# 显示图表
plt.show()

# 创建延迟表格可视化
fig, ax = plt.subplots(figsize=(14, 8))
ax.axis('tight')
ax.axis('off')

# 创建表格
delay_table = ax.table(cellText=df_delay.values, colLabels=df_delay.columns, loc='center', 
                      cellLoc='center', rowLoc='center')

# 设置表格样式
delay_table.auto_set_font_size(False)
delay_table.set_fontsize(12)
delay_table.scale(1, 1.5)  # 调整表格大小

# 设置标题
plt.title('表4-7 系统性能测试结果表 - 延迟详细分布', fontsize=20, pad=20)

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig('tubiao/表4-7_系统性能测试结果表_延迟详细.png', dpi=300, bbox_inches='tight')
plt.savefig('tubiao/表4-7_系统性能测试结果表_延迟详细.pdf', bbox_inches='tight')

# 显示图表
plt.show()

# 创建延迟柱状图
fig, ax = plt.subplots(figsize=(14, 8))

# 准备数据
stages = df_delay['处理阶段']
high_perf = df_delay['高性能服务器 (ms)']
standard = df_delay['标准服务器 (ms)']
edge = df_delay['边缘设备 (ms)']

# 设置柱状图的宽度和位置
bar_width = 0.25
x = np.arange(len(stages))

# 绘制柱状图
bars1 = ax.bar(x - bar_width, high_perf, bar_width, label='高性能服务器', color='#3498db', edgecolor='black', linewidth=1.5)
bars2 = ax.bar(x, standard, bar_width, label='标准服务器', color='#2ecc71', edgecolor='black', linewidth=1.5)
bars3 = ax.bar(x + bar_width, edge, bar_width, label='边缘设备', color='#e74c3c', edgecolor='black', linewidth=1.5)

# 添加数据标签
def add_labels(bars):
    for bar in bars:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2, height + 0.5,
                f'{height:.1f}',
                ha='center', va='bottom', fontsize=10)

add_labels(bars1)
add_labels(bars2)
add_labels(bars3)

# 添加标题和标签
ax.set_title('图4-7 不同环境下系统处理延迟对比', fontsize=20, pad=20)
ax.set_xlabel('处理阶段', fontsize=16, labelpad=10)
ax.set_ylabel('延迟 (ms)', fontsize=16, labelpad=10)

# 设置x轴刻度
ax.set_xticks(x)
ax.set_xticklabels(stages, fontsize=14)

# 添加网格线
ax.grid(True, linestyle='--', alpha=0.7, axis='y')

# 添加图例
ax.legend(fontsize=14, loc='upper left')

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig('tubiao/图4-7_不同环境下系统处理延迟对比.png', dpi=300, bbox_inches='tight')
plt.savefig('tubiao/图4-7_不同环境下系统处理延迟对比.pdf', bbox_inches='tight')

# 显示图表
plt.show()

# 另外，生成一个更美观的HTML表格
html_table = df.to_html(index=False)
with open('tubiao/表4-7_系统性能测试结果表.html', 'w', encoding='utf-8') as f:
    f.write(f'''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>表4-7 系统性能测试结果表</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 20px;
            }}
            table {{
                border-collapse: collapse;
                width: 100%;
                margin-top: 20px;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 12px;
                text-align: center;
            }}
            th {{
                background-color: #f2f2f2;
                font-weight: bold;
            }}
            tr:nth-child(even) {{
                background-color: #f9f9f9;
            }}
            tr:hover {{
                background-color: #f5f5f5;
            }}
            .highlight {{
                background-color: #e8f4f8;
                font-weight: bold;
            }}
            h1 {{
                text-align: center;
            }}
        </style>
    </head>
    <body>
        <h1>表4-7 系统性能测试结果表</h1>
        {html_table}
        <h2>延迟详细分布</h2>
        {df_delay.to_html(index=False)}
    </body>
    </html>
    ''')
