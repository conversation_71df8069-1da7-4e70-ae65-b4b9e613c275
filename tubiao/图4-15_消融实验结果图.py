import matplotlib.pyplot as plt
import numpy as np
import matplotlib.font_manager as fm
from matplotlib.font_manager import FontProperties
import os

# 检查是否有支持中文的字体
font_path = None
for font in fm.findSystemFonts(fontpaths=None, fontext='ttf'):
    if os.path.basename(font) == 'SimHei.ttf' or os.path.basename(font) == 'simhei.ttf':
        font_path = font
        break
    elif os.path.basename(font) == 'Microsoft YaHei.ttf' or 'msyh.ttf' in os.path.basename(font).lower():
        font_path = font
        break

if font_path:
    font_prop = FontProperties(fname=font_path)
    plt.rcParams['font.family'] = font_prop.get_name()
else:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']

plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 创建数据
models = [
    'CNN-LSTM',
    '+残差链接',
    '+注意力机制',
    '+ GAN (完整模型)'
]

accuracy = np.array([95.2, 95.8, 96.7, 97.5])
f1_score = np.array([95.2, 95.9, 96.3, 97.0])
auc = np.array([0.951, 0.962, 0.979, 0.991]) * 100  # 转换为百分比便于显示

# 计算性能提升百分比
accuracy_improvement = [0]
f1_improvement = [0]
auc_improvement = [0]

for i in range(1, len(accuracy)):
    accuracy_improvement.append(accuracy[i] - accuracy[i-1])
    f1_improvement.append(f1_score[i] - f1_score[i-1])
    auc_improvement.append(auc[i] - auc[i-1])

# 创建图表
fig, ax = plt.subplots(figsize=(14, 10))

# 设置柱状图的宽度和位置
bar_width = 0.25
x = np.arange(len(models))

# 绘制柱状图
bars1 = ax.bar(x - bar_width, accuracy, bar_width, label='准确率 (%)', color='#3498db', edgecolor='black', linewidth=1.5)
bars2 = ax.bar(x, f1_score, bar_width, label='F1分数 (%)', color='#2ecc71', edgecolor='black', linewidth=1.5)
bars3 = ax.bar(x + bar_width, auc, bar_width, label='AUC (%)', color='#e74c3c', edgecolor='black', linewidth=1.5)

# 添加数据标签
def add_labels(bars, improvements):
    for i, bar in enumerate(bars):
        height = bar.get_height()
        if i == 0:
            ax.text(bar.get_x() + bar.get_width()/2, height + 0.3,
                    f'{height:.1f}%',
                    ha='center', va='bottom', fontsize=12)
        else:
            ax.text(bar.get_x() + bar.get_width()/2, height + 0.3,
                    f'{height:.1f}%\n(+{improvements[i]:.1f}%)',
                    ha='center', va='bottom', fontsize=12)

add_labels(bars1, accuracy_improvement)
add_labels(bars2, f1_improvement)
add_labels(bars3, auc_improvement)

# 添加标题和标签
ax.set_ylabel('性能指标 (%)', fontsize=16, labelpad=10)

# 设置x轴刻度
ax.set_xticks(x)
ax.set_xticklabels(models, fontsize=14, rotation=15)

# 设置y轴范围
ax.set_ylim(94, 100)

# 添加网格线
ax.grid(True, linestyle='--', alpha=0.7, axis='y')

# 添加图例
ax.legend(fontsize=14, loc='lower right')

# 添加注释
plt.figtext(0.5, 0.01,
            '注: 展示了各组件对模型性能的累积贡献，从CNN-LSTM基础模型开始逐步添加新组件',
            ha='center', fontsize=12, style='italic')

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig('tubiao\\图4-15_消融实验结果图.png', dpi=300, bbox_inches='tight')

# 显示图表
plt.show()

# 另外，创建一个折线图版本
fig, ax = plt.subplots(figsize=(14, 10))

# 绘制折线图
ax.plot(x, accuracy, 'o-', linewidth=3, markersize=10, label='准确率 (%)', color='#3498db')
ax.plot(x, f1_score, 's-', linewidth=3, markersize=10, label='F1分数 (%)', color='#2ecc71')
ax.plot(x, auc, '^-', linewidth=3, markersize=10, label='AUC (%)', color='#e74c3c')

# 添加数据标签
for i in range(len(models)):
    ax.text(i, accuracy[i] + 0.3, f'{accuracy[i]:.1f}%', ha='center', va='bottom', fontsize=12)
    ax.text(i, f1_score[i] - 0.3, f'{f1_score[i]:.1f}%', ha='center', va='top', fontsize=12)
    ax.text(i, auc[i] + 0.3, f'{auc[i]:.1f}%', ha='center', va='bottom', fontsize=12)

ax.set_ylabel('性能指标 (%)', fontsize=16, labelpad=10)

# 设置x轴刻度
ax.set_xticks(x)
ax.set_xticklabels(models, fontsize=14, rotation=15)

# 设置y轴范围
ax.set_ylim(94, 101)

# 添加网格线
ax.grid(True, linestyle='--', alpha=0.7)

# 添加图例
ax.legend(fontsize=14, loc='lower right')

# 添加注释
plt.figtext(0.5, 0.01,
            '注: 图表展示了各组件对模型性能的累积贡献，从基础CNN-LSTM模型开始，逐步添加新组件',
            ha='center', fontsize=12, style='italic')

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig('tubiao\\图4-15_消融实验结果图_折线版.png', dpi=300, bbox_inches='tight')

# 打印确认信息
print("消融实验结果图已更新，包含以下模型变体:")
for i, model in enumerate(models):
    print(f"{model}: AC={accuracy[i]}%, F1={f1_score[i]}%, AUC={auc[i]/100:.3f}")

# 显示图表
plt.show()
