

from PIL import Image


def replace_color(image_path, old_color, new_color):
    image = Image.open(image_path).convert('RGB')
    pixels = image.load()
    width, height = image.size
    for x in range(width):
        for y in range(height):
            if pixels[x, y] == old_color:  # 精确匹配颜色
                pixels[x, y] = new_color
    image.save('卷积神经网络基本结构图new.png')


# 示例：将红色(255,0,0)替换为蓝色(0,0,255)
replace_color('卷积神经网络基本结构图.png', (133, 181, 37), (0, 0, 255))