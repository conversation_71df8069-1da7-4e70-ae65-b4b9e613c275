import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import matplotlib.font_manager as fm
from matplotlib.font_manager import FontProperties
import os

# 检查是否有支持中文的字体
font_path = None
for font in fm.findSystemFonts(fontpaths=None, fontext='ttf'):
    if os.path.basename(font) == 'SimHei.ttf' or os.path.basename(font) == 'simhei.ttf':
        font_path = font
        break
    elif os.path.basename(font) == 'Microsoft YaHei.ttf' or 'msyh.ttf' in os.path.basename(font).lower():
        font_path = font
        break

if font_path:
    font_prop = FontProperties(fname=font_path)
    plt.rcParams['font.family'] = font_prop.get_name()
else:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
    
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 创建数据
data = {
    '检测方法类别': ['基于解密的检测方法', '基于解密的检测方法', '基于协议特征的检测方法', 
                 '基于协议特征的检测方法', '基于流量统计特征的检测方法', '基于流量统计特征的检测方法',
                 '基于机器学习的检测方法', '基于机器学习的检测方法'],
    '代表技术': ['中间人解密', '密钥共享', 'TLS握手分析', '协议异常检测', 
              '流量元数据分析', '流量行为模式分析', '决策树与随机森林', '支持向量机'],
    '优点': ['可检查加密内容，准确率高', '不中断加密通道，准确率高', '无需解密，协议兼容性好', 
           '可检测协议实现缺陷，低误报', '通用性强，隐私影响小', '可检测复杂行为模式，适应性强',
           '训练快速，可解释性强', '处理高维特征能力强'],
    '缺点': ['破坏端到端加密，隐私问题，TLS 1.3支持有限', '仅适用于企业控制的服务器，密钥管理复杂', 
           '仅限于握手阶段信息，深度检测能力有限', '仅针对协议异常，难以检测合规恶意流量',
           '精度有限，易受网络条件影响', '误报率较高，计算复杂度大', 
           '特征工程依赖性强，泛化能力有限', '计算复杂度高，参数调优困难'],
    '适用场景': ['企业内部网络', '企业自有服务', '通用网络环境', '安全合规检查',
              '大规模网络监控', '高级威胁检测', '已知类型分类', '二分类问题'],
    '检测准确率': ['95-99%', '95-99%', '75-85%', '70-80%', '60-75%', '65-80%', '85-95%', '80-90%']
}

# 创建DataFrame
df = pd.DataFrame(data)

# 保存为CSV
df.to_csv('tubiao/表1-1_传统加密流量检测方法比较表.csv', index=False, encoding='utf-8-sig')

# 创建表格可视化
fig, ax = plt.subplots(figsize=(16, 10))
ax.axis('tight')
ax.axis('off')

# 创建表格
table = ax.table(cellText=df.values, colLabels=df.columns, loc='center', 
                cellLoc='center', rowLoc='center')

# 设置表格样式
table.auto_set_font_size(False)
table.set_fontsize(12)
table.scale(1, 2)  # 调整表格大小

# 设置标题
plt.title('表1-1 传统加密流量检测方法比较表', fontsize=20, pad=20)

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig('tubiao/表1-1_传统加密流量检测方法比较表.png', dpi=300, bbox_inches='tight')
plt.savefig('tubiao/表1-1_传统加密流量检测方法比较表.pdf', bbox_inches='tight')

# 显示图表
plt.show()

# 另外，生成一个更美观的HTML表格
html_table = df.to_html(index=False)
with open('tubiao/表1-1_传统加密流量检测方法比较表.html', 'w', encoding='utf-8') as f:
    f.write(f'''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>表1-1 传统加密流量检测方法比较表</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 20px;
            }}
            table {{
                border-collapse: collapse;
                width: 100%;
                margin-top: 20px;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 12px;
                text-align: left;
            }}
            th {{
                background-color: #f2f2f2;
                font-weight: bold;
            }}
            tr:nth-child(even) {{
                background-color: #f9f9f9;
            }}
            h1 {{
                text-align: center;
            }}
        </style>
    </head>
    <body>
        <h1>表1-1 传统加密流量检测方法比较表</h1>
        {html_table}
    </body>
    </html>
    ''')
