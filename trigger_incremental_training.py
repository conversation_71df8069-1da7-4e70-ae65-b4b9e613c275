"""
触发模型增量训练的客户端脚本

此脚本提供了一个简单的命令行界面，用于触发模型的增量训练。
"""

import requests
import argparse
import json
import time

# API端点
BASE_URL = "http://localhost:5000/api"
ADD_DATA_URL = f"{BASE_URL}/add-training-data"
STATUS_URL = f"{BASE_URL}/model-training-status"
TRIGGER_URL = f"{BASE_URL}/trigger-model-training"

def print_json(data):
    """美化打印JSON数据"""
    print(json.dumps(data, indent=2, ensure_ascii=False))

def check_model_status(model_name):
    """检查模型状态"""
    print(f"\n正在检查模型 {model_name} 的状态...")
    try:
        response = requests.get(f"{STATUS_URL}?model_name={model_name}")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                status = data['status']
                print(f"训练队列大小: {status['queue_size']}")
                print(f"训练中: {status['training_in_progress']}")
                print(f"已触发训练: {status['training_triggered']}")
                
                # 打印最近的训练日志
                if 'training_log' in status and status['training_log']:
                    print("\n最近的训练记录:")
                    for log in status['training_log'][-3:]:  # 只显示最近3条
                        print(f"- {log['timestamp']}: {'成功' if log['success'] else '失败'} - {log['message']}")
                return status
            else:
                print(f"获取状态失败: {data.get('error')}")
        else:
            print(f"请求失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"请求出错: {str(e)}")
    return None

def trigger_training(model_name):
    """触发模型增量训练"""
    print(f"\n正在触发模型 {model_name} 的增量训练...")
    try:
        response = requests.post(TRIGGER_URL, json={'model_name': model_name})
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"成功触发训练: {data.get('message')}")
                return True
            else:
                print(f"触发训练失败: {data.get('error')}")
        else:
            print(f"请求失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"请求出错: {str(e)}")
    return False

def monitor_training(model_name, max_wait=300, interval=5):
    """监控训练进度"""
    print(f"\n开始监控训练进度 (最多等待 {max_wait} 秒)...")
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(f"{STATUS_URL}?model_name={model_name}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    status = data['status']
                    
                    # 如果训练已完成
                    if not status['training_in_progress'] and not status['training_triggered']:
                        print("\n训练已完成!")
                        
                        # 打印最近的训练记录
                        if 'training_log' in status and status['training_log']:
                            print("\n最近的训练记录:")
                            for log in status['training_log'][-1:]:  # 只显示最近一条
                                print(f"- {log['timestamp']}: {'成功' if log['success'] else '失败'} - {log['message']}")
                        
                        return True
                    else:
                        print(f"训练中... (已等待 {int(time.time() - start_time)} 秒)")
        except Exception as e:
            print(f"监控出错: {str(e)}")
        
        time.sleep(interval)
    
    print(f"\n等待超时 ({max_wait} 秒)，训练可能仍在进行中")
    return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='触发模型增量训练')
    parser.add_argument('--model', type=str, default='anomaly_detector',
                        choices=['anomaly_detector', 'encryption_detector'],
                        help='要训练的模型名称 (默认: anomaly_detector)')
    parser.add_argument('--monitor', action='store_true',
                        help='是否监控训练进度')
    parser.add_argument('--timeout', type=int, default=300,
                        help='监控训练的最大等待时间（秒）')
    
    args = parser.parse_args()
    
    print(f"===== 触发模型 {args.model} 的增量训练 =====")
    
    # 1. 检查初始状态
    status = check_model_status(args.model)
    if status is None:
        print("无法获取模型状态，请确保服务器正在运行")
        return
    
    # 2. 检查训练队列是否有足够的数据
    if status['queue_size'] < 50:
        print(f"警告: 训练队列中只有 {status['queue_size']} 个样本，建议至少有50个样本")
        proceed = input("是否继续触发训练? (y/n): ")
        if proceed.lower() != 'y':
            print("已取消训练")
            return
    
    # 3. 触发训练
    if not trigger_training(args.model):
        print("触发训练失败，退出")
        return
    
    # 4. 监控训练进度（如果需要）
    if args.monitor:
        monitor_training(args.model, max_wait=args.timeout)
        
        # 5. 检查最终状态
        check_model_status(args.model)
    
    print("\n===== 操作完成 =====")

if __name__ == "__main__":
    main()
