# 网络流量分析系统
基于Python和Flask的网络流量分析系统，提供实时流量捕获、异常检测和加密流量识别等功能。系统集成了先进的深度学习模型，包括最新的ResCNN-ABLGAN模型，可实现高精度的网络流量分析。

## 功能特点
- 🔍 **实时网络流量捕获与分析**：通过系统可实时获取网络流量数据，对捕获的数据包进行解析和分析，展示流量的实时状态。
- 📊 **PCAP文件解析与可视化**：支持对PCAP文件的解析，将解析后的数据以可视化的方式呈现，便于用户直观了解网络流量的详细信息。
- 🚨 **异常流量检测（SYN洪水、端口扫描等）**：利用基于规则和深度学习模型的方法，自动检测网络流量中的异常行为，如SYN洪水攻击、端口扫描行为、UDP洪水攻击、ICMP洪水攻击等，保障网络安全。
- 🔐 **加密流量识别**：不仅能识别常见加密协议（如HTTPS (443)、SSH (22)、FTPS (989/990)、IMAPS (993)、POP3S (995)）的流量，还能通过深度学习模型和高级加密分析器对加密流量进行更深入的分析，如计算JA3/JA3S指纹、分析SSL/TLS证书、解析ALPN协议等。
- 📈 **流量统计与趋势分析**：对捕获的流量数据进行统计，包括数据包数量、流量大小等，并通过图表展示流量的趋势，帮助用户掌握网络流量的变化规律。
- 🌐 **Web界面实时监控**：提供简洁易用的Web界面，方便用户实时监控网络流量的各项指标，操作便捷，无需复杂的命令行操作。
- 🧠 **多种深度学习模型支持**：系统支持三种不同复杂度的深度学习模型：
  - **基础LSTM模型**：轻量级模型，适合资源受限环境，处理速度快
  - **标准CNN-LSTM模型**：平衡性能和精度的中等复杂度模型
  - **ResCNN-ABLGAN模型**：集成了残差连接、双向LSTM、注意力机制、多任务学习和GAN的高级模型，精度极高

## 系统原理
- **实时流量捕获**：利用`psutil`库获取系统网络接口信息，使用`scapy`库的`sniff`函数在指定网络接口上进行数据包捕获。捕获到的数据包存储在内存中，并实时更新流量统计信息。

- **深度学习模型**：系统提供三种不同复杂度的深度学习模型，用户可根据需求选择：
  - **基础LSTM模型**：使用简单的LSTM结构，资源消耗小，处理速度快，适合资源受限的环境。
  - **标准CNN-LSTM模型**：结合卷积神经网络和LSTM，平衡性能和精度，适合一般应用场景。
  - **ResCNN-ABLGAN模型**：集成了残差连接（Residual）、双向LSTM（Bidirectional）、注意力机制（Attention）、多任务学习（Multi-task）和生成对抗网络（GAN），精度极高，适合高要求安全场景。

- **异常检测**：结合基于规则和深度学习的双重检测方法：
  - **基于规则**：检测SYN标志位、端口号范围等特征来识别常见攻击。
  - **基于深度学习**：使用选定的深度学习模型对数据包特征进行分析，可检测复杂的异常模式。

- **加密检测**：多层次的加密流量识别方法：
  - **基于端口**：识别常见加密协议端口（如443、22、989/990等）。
  - **基于TLS分析**：检测TLS层信息，分析证书、计算JA3/JA3S指纹、解析ALPN协议等。
  - **基于深度学习**：使用选定的深度学习模型识别非标准端口的加密流量。

## 系统要求
- **软件**：Python 3.6+、Windows/Linux/MacOS
- **权限**：管理员权限（用于网络抓包）

## 安装步骤
1. 克隆仓库：
```bash
git clone https://github.com/yourusername/network-traffic-analyzer.git
cd network-traffic-analyzer
```
2. 创建虚拟环境（推荐）：
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/MacOS
source venv/bin/activate
```
3. 安装依赖：
```bash
pip install -r requirements.txt
```

## 启动系统
1. 确保已激活虚拟环境。
2. 启动Flask应用：
```bash
# Windows
python run.py
# Linux/MacOS（需要sudo权限进行抓包）
sudo python run.py
```
3. 访问Web界面：
   - 打开浏览器访问 http://localhost:5000

## 使用指南
### 1. 实时流量捕获
1. 在主页面的网络接口下拉菜单中，选择要捕获流量的网络接口。该菜单中的接口信息由系统自动获取，确保选择的接口处于正常工作状态。
2. 点击“开始捕获”按钮，系统将立即开始在选定的网络接口上捕获数据包。此时，按钮状态会切换，“停止捕获”按钮变为可用，同时页面上的实时流量统计信息开始更新。
3. 实时查看：
   - **流量统计**：页面实时显示总数据包数、总流量大小等统计数据，这些数据会随着新数据包的捕获而动态更新。
   - **协议分布**：以直观的方式展示不同协议的数据包数量占比，帮助用户快速了解网络流量中各种协议的使用情况。
   - **异常检测结果**：一旦检测到异常流量，系统会在相应位置标记并显示异常类型，如“SYN洪水攻击”“端口扫描”等。
4. 完成捕获后，点击“停止捕获”按钮，系统将停止数据包的捕获，并停止更新实时统计信息。

### 2. PCAP文件分析
1. 进入“异常检测”或“加密检测”页面，这两个页面分别用于对PCAP文件进行不同类型的分析。

2. 选择深度学习模型：
   - **基础LSTM模型**：当计算资源有限或需要快速分析时选择。
   - **标准CNN-LSTM模型**：平衡速度和精度的选项。
   - **ResCNN-ABLGAN模型**：当需要高精度分析或处理复杂安全场景时选择，提供最高的检测精度。

3. 上传PCAP文件：
   - 支持.pcap和.pcapng格式的文件上传。确保上传的文件格式正确，否则系统将提示错误。
   - 点击“选择文件”按钮，在本地文件系统中选择要上传的PCAP文件。
   - 选择文件后，点击“上传并分析”按钮，系统将上传文件并进行相应的分析操作。上传过程中，页面会显示进度条，方便用户了解上传状态。

4. 查看分析结果：
   - **数据包列表**：以表格形式展示PCAP文件中的数据包详细信息，包括编号、时间、源地址、目标地址、协议、长度等。
   - **统计信息**：显示PCAP文件中的总数据包数、异常数据包数（在异常检测页面）或加密数据包数（在加密检测页面）等统计数据。
   - **异常标记**：在异常检测页面，异常数据包会被特殊标记，如改变行颜色或添加图标，同时显示异常类型；在加密检测页面，加密数据包会标记加密状态和加密类型。
   - **模型信息**：分析结果中会显示所使用的深度学习模型类型，帮助用户了解分析的精度和性能特点。

### 3. 异常检测
系统自动检测以下异常：
- **SYN洪水攻击**：当检测到大量仅包含SYN标志位的TCP数据包时，判定为SYN洪水攻击。
- **端口扫描行为**：若发现TCP数据包的标志位为SYN且目标端口在1 - 1024范围内，视为端口扫描行为。
- **UDP洪水攻击**：检测到UDP数据包长度大于1000时，可能存在UDP洪水攻击。
- **ICMP洪水攻击**：当出现大量ICMP数据包时，判断为ICMP洪水攻击。

### 4. 加密流量识别
自动识别常见加密协议：
- **HTTPS (443)**：通过检测目标端口为443的TCP流量，结合TLS层信息和深度学习模型预测，判断是否为HTTPS加密流量。
- **SSH (22)**：对目标端口为22的TCP流量进行分析，识别SSH加密流量。
- **FTPS (989/990)**：检测目标端口为989或990的TCP流量，确定是否为FTPS加密流量。
- **IMAPS (993)**：针对目标端口为993的TCP流量，进行IMAPS加密流量的识别。
- **POP3S (995)**：通过分析目标端口为995的TCP流量，识别POP3S加密流量。

## 项目结构
```
network_monitor/
├── app/
│   ├── __init__.py
│   ├── views.py
│   ├── models.py
│   ├── utils/
│   │   ├── pcap_analyzer.py
│   │   ├── deep_learning_model.py
│   │   ├── analyzers.py
│   │   ├── cache_manager.py
│   │   ├── data_augmentation.py
│   │   ├── data_cache.py
│   │   ├── encryption_analyzer.py
│   │   ├── flow_analyzer.py
│   │   ├── hyperparameter_optimization.py
│   │   └── model_monitoring.py
│   ├── static/
│   │   ├── css/
│   │   └── js/
│   └── templates/
│       ├── index.html
│       ├── anomaly_detection.html
│       └── encryption_detection.html
├── requirements.txt
├── run.py
└── README.md
```

## 常见问题
1. **无法启动捕获**
   - 确保以管理员权限运行，在Windows系统中可尝试右键选择“以管理员身份运行”；在Linux/MacOS系统中，使用`sudo`命令启动程序。
   - 检查选择的网络接口是否可用，可通过系统网络设置或网络工具确认接口状态，若接口不可用，选择其他正常工作的接口。

2. **上传文件失败**
   - 确认文件格式（.pcap/.pcapng），若文件格式不正确，需转换为支持的格式后再上传。
   - 检查文件大小是否超限，目前系统未明确文件大小限制，若上传大文件失败，可尝试分割文件或联系管理员调整配置。

3. **分析结果不显示**
   - 检查浏览器控制台错误，按F12打开浏览器控制台，查看是否有JavaScript错误信息，根据错误提示解决问题。
   - 确认文件是否损坏，可尝试使用其他工具打开PCAP文件，若文件无法正常打开，则需获取正确的文件后重新上传分析。

4. **模型选择问题**
   - **选择哪种模型最适合我的需求？**
     - 如果计算资源有限或需要快速分析，选择“基础LSTM模型”。
     - 对于一般用途，“标准CNN-LSTM模型”提供了很好的平衡。
     - 当需要高精度分析或处理复杂安全场景时，选择“ResCNN-ABLGAN模型”。
   - **ResCNN-ABLGAN模型的名称含义是什么？**
     - Res：残差连接（Residual Connections）
     - CNN：卷积神经网络（Convolutional Neural Network）
     - A：注意力机制（Attention）
     - B：双向（Bidirectional）LSTM
     - L：长短期记忆网络（Long Short-Term Memory）
     - G：生成对抗网络（Generative Adversarial Network）
     - A：对抗（Adversarial）多任务学习
     - N：网络（Network）

5. **深度学习模型评估结果异常**
   - 检查训练数据质量，确保训练数据包含足够的正常和异常流量样本，且数据标注准确。
   - 确认模型超参数设置，可尝试使用超参数优化方法（如贝叶斯优化、网格搜索等）寻找更合适的超参数。

6. **系统运行缓慢**
   - 检查系统资源占用，使用系统监控工具查看CPU、内存等资源使用情况，若资源不足，关闭其他占用资源的程序。
   - 如果使用ResCNN-ABLGAN模型时系统运行缓慢，可尝试切换到标准或基础模型以提高性能。
   - 优化深度学习模型，可尝试调整模型结构、进行模型压缩（如剪枝）等操作，提高模型运行效率。
   - 对于ResCNN-ABLGAN模型，可以尝试禁用对抗训练或GAN组件，仅使用残差连接和注意力机制，以减少计算负担。

## 安全建议
1. 定期检查异常检测结果，及时发现潜在的网络安全威胁。
2. 及时处理发现的异常流量，根据异常类型采取相应的措施，如阻断连接、封禁IP等。
3. 妥善保管捕获的数据文件，设置合理的文件访问权限，防止数据泄露。
4. 定期更新系统和依赖包，保持系统的安全性和稳定性，修复已知的漏洞。

## 技术栈
- **后端**：Python + Flask
- **数据处理**：Scapy + Pandas
- **前端**：Bootstrap + jQuery + ECharts
- **机器学习**：PyTorch（用于深度学习模型）

## 许可证
MIT License

## 致谢
感谢以下开源项目：
- Flask
- Scapy
- PyTorch
- ECharts