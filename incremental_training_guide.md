# 模型增量训练功能使用指南

本文档介绍如何使用网络流量监控系统的模型增量训练功能。增量训练允许模型在不重新训练整个模型的情况下，使用新的数据进行更新，从而适应不断变化的网络流量模式。

## 目录

1. [功能概述](#1-功能概述)
2. [工作原理](#2-工作原理)
3. [使用方法](#3-使用方法)
4. [API接口](#4-api接口)
5. [测试脚本](#5-测试脚本)
6. [常见问题](#6-常见问题)

## 1. 功能概述

增量训练功能允许系统在检测到模型性能下降时，自动触发模型重训练，或者通过API手动触发重训练。这种方式有以下优点：

- **适应性强**：模型可以适应不断变化的网络流量模式
- **资源效率**：不需要重新训练整个模型，只需要使用新数据进行增量更新
- **自动化**：系统可以自动检测性能下降并触发训练，减少人工干预

## 2. 工作原理

增量训练功能的工作流程如下：

1. **数据收集**：系统在进行预测时，如果提供了真实标签，会将特征和标签保存到训练队列中
2. **性能监控**：系统持续监控模型性能指标（如准确率、延迟等）
3. **触发训练**：当检测到连续多次性能下降，或通过API手动触发时，启动增量训练
4. **模型更新**：使用收集的数据对模型进行增量训练，并保存更新后的模型
5. **重置监控**：重置性能基准值和下降计数器

## 3. 使用方法

### 3.1 自动增量训练

系统会自动监控模型性能，当检测到连续5次性能下降时，会自动触发增量训练。要使用此功能，只需确保在调用模型进行预测时提供真实标签和特征：

```python
# 示例：提供预测结果、真实标签和特征
model_monitor.update(
    model_name='anomaly_detector',
    prediction=prediction,
    ground_truth=true_label,
    features=features,
    latency=latency
)
```

### 3.2 手动增量训练

您也可以通过API手动触发增量训练：

```bash
curl -X POST http://localhost:5000/api/trigger-model-training \
  -H "Content-Type: application/json" \
  -d '{"model_name": "anomaly_detector"}'
```

### 3.3 添加训练数据

您可以通过API手动添加训练数据到队列：

```bash
curl -X POST http://localhost:5000/api/add-training-data \
  -H "Content-Type: application/json" \
  -d '{
    "model_name": "anomaly_detector",
    "features": [0.1, 0.2, 0.3, ...],
    "label": 1,
    "count": 1
  }'
```

## 4. API接口

系统提供以下API接口用于增量训练功能：

### 4.1 触发增量训练

- **URL**: `/api/trigger-model-training`
- **方法**: POST
- **请求体**:
  ```json
  {
    "model_name": "anomaly_detector"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "已成功触发模型 anomaly_detector 的增量训练",
    "status": "训练已在后台启动，请稍后查看训练日志"
  }
  ```

### 4.2 获取训练状态

- **URL**: `/api/model-training-status`
- **方法**: GET
- **参数**: `model_name` (可选，如果不提供则返回所有模型的状态)
- **响应**:
  ```json
  {
    "success": true,
    "status": {
      "training_in_progress": false,
      "training_triggered": false,
      "queue_size": 120,
      "metrics": {
        "accuracy": 0.95,
        "latency": 0.02,
        "total_predictions": 1000
      },
      "training_log": [
        {
          "timestamp": "2023-05-01T12:00:00",
          "success": true,
          "message": "成功完成增量训练，使用60个样本",
          "model_path": "app/models/anomaly_model.pth"
        }
      ]
    }
  }
  ```

### 4.3 添加训练数据

- **URL**: `/api/add-training-data`
- **方法**: POST
- **请求体**:
  ```json
  {
    "model_name": "anomaly_detector",
    "features": [0.1, 0.2, 0.3, ...],
    "label": 1,
    "count": 1
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "已成功添加 1 个样本到模型 anomaly_detector 的训练队列",
    "queue_size": 121
  }
  ```

## 5. 测试脚本

系统提供了一个测试脚本 `test_incremental_training.py`，用于演示如何使用增量训练功能：

```bash
# 运行测试脚本
python test_incremental_training.py
```

测试脚本会执行以下操作：
1. 检查模型初始状态
2. 添加训练数据到队列
3. 触发增量训练
4. 监控训练进度
5. 检查最终状态

## 6. 常见问题

### 6.1 训练队列数据不足

如果训练队列中的数据少于50个样本，系统会拒绝触发增量训练。确保在触发训练前添加足够的样本。

### 6.2 模型已在训练中

如果模型已经在训练中，系统会拒绝再次触发训练。等待当前训练完成后再尝试。

### 6.3 训练失败

如果训练失败，可以查看训练日志了解详细原因。常见原因包括：
- 数据格式不正确
- 内存不足
- GPU不可用（如果配置使用GPU）

### 6.4 性能未改善

如果增量训练后模型性能未改善，可能是因为：
- 训练数据质量不高
- 训练样本数量不足
- 模型已经达到性能上限

尝试提供更多高质量的训练样本，或考虑使用更复杂的模型架构。
