from . import db
import json
from datetime import datetime

# 自定义JSON编码器，处理特殊类型
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        # 处理FlagValue类型
        if hasattr(obj, 'value') and hasattr(obj, '__int__'):
            return int(obj)
        # 处理其他不可序列化的类型
        try:
            return str(obj)
        except:
            return None
        # 如果无法处理，让父类处理
        return super().default(obj)

class PcapFile(db.Model):
    """PCAP文件模型"""
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    file_size = db.Column(db.Integer)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    flows = db.relationship('NetworkFlow', backref='pcap_file', lazy=True)
    analysis_results = db.relationship('AnalysisResult', backref='pcap_file', lazy=True)

class NetworkFlow(db.Model):
    """网络流量模型"""
    id = db.Column(db.Integer, primary_key=True)
    pcap_file_id = db.Column(db.Integer, db.ForeignKey('pcap_file.id'), nullable=False)
    src_ip = db.Column(db.String(50))
    dst_ip = db.Column(db.String(50))
    src_port = db.Column(db.Integer)
    dst_port = db.Column(db.Integer)
    protocol = db.Column(db.String(10))
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    packet_count = db.Column(db.Integer)
    byte_count = db.Column(db.Integer)
    is_anomaly = db.Column(db.Boolean, default=False)
    is_encrypted = db.Column(db.Boolean, default=False)
    attack_type = db.Column(db.String(50))
    application_type = db.Column(db.String(50))

class AnalysisResult(db.Model):
    """PCAP文件分析结果缓存模型"""
    id = db.Column(db.Integer, primary_key=True)
    pcap_file_id = db.Column(db.Integer, db.ForeignKey('pcap_file.id'), nullable=False)
    analysis_type = db.Column(db.String(50), nullable=False)  # 'anomaly', 'encryption', 'flow', etc.
    result_data = db.Column(db.Text)  # 存储JSON格式的分析结果
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def set_result_data(self, data):
        """将分析结果转换为JSON字符串并存储"""
        # 使用自定义编码器处理特殊类型
        self.result_data = json.dumps(data, cls=CustomJSONEncoder)

    def get_result_data(self):
        """将存储的JSON字符串转换回Python对象"""
        if self.result_data:
            return json.loads(self.result_data)
        return None
