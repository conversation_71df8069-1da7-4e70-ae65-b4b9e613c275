{% extends "base.html" %}

{% block page_title %}加密流量检测{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active" aria-current="page">加密检测</li>
{% endblock %}

{% block head %}
<style>
    .bg-gradient-light {
        background: linear-gradient(to right, #f8f9fa, #e9ecef);
    }

    .card.shadow-sm {
        transition: all 0.3s ease;
    }

    .card.shadow-sm:hover {
        transform: translateY(-5px);
        box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
    }

    .card-header {
        border-bottom: 1px solid rgba(0,0,0,.125);
    }

    .card-header .card-title {
        color: #495057;
        font-weight: 600;
    }

    .card-header i {
        color: #6c757d;
    }

    /* 暗色模式兼容 */
    .dark-mode .bg-gradient-light {
        background: linear-gradient(to right, #343a40, #212529);
    }

    .dark-mode .card-header .card-title {
        color: #e0e0e0;
    }

    .dark-mode .card-header i {
        color: #adb5bd;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">

    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">PCAP文件分析</h5>
                </div>
                <div class="card-body">
                    <form id="pcap-upload-form" class="mb-4">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <input type="file" class="form-control" id="pcap-file" accept=".pcap,.pcapng" multiple required>
                            </div>
                            <div class="col-auto">
                                <select class="form-select" id="model-type">
                                    <option value="simple">基础LSTM模型</option>
                                    <option value="cnnlstm">标准CNN-LSTM模型</option>
                                    <option value="advanced" selected>ResCNN-ABLGAN模型</option>
                                </select>
                                <small class="form-text text-muted">
                                    简单模型速度快，复杂模型精度高
                                </small>
                            </div>
                            <div class="col-auto">
                                <button type="button" id="upload-analyze-btn" class="btn btn-primary">
                                    <i class="fas fa-upload me-2"></i>上传并分析
                                </button>
                            </div>
                        </div>
                    </form>

                    <div id="analysis-progress" class="my-4" style="display: none;">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="card-title mb-0">文件分析进度</h6>
                            </div>
                            <div class="card-body">
                                <div id="file-list" class="mb-3">
                                    <!-- 文件列表将在这里动态生成 -->
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <div class="progress flex-grow-1" style="height: 20px;">
                                        <div id="total-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated"
                                             role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                            0%
                                        </div>
                                    </div>
                                    <span id="progress-percentage" class="ms-2">0%</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <small id="progress-status">准备分析...</small>
                                    <small id="estimated-time">预计剩余时间: 计算中...</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 me-3">分析结果</h5>
                        <div id="results-file-selector" class="d-none me-3">
                            <select id="file-result-selector" class="form-select form-select-sm" style="min-width: 200px;">
                                <option value="combined">合并所有结果</option>
                                <!-- 文件选项将在这里动态生成 -->
                            </select>
                        </div>
                        <small id="model-info-display" class="text-muted"></small>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="show-only-encrypted">
                            <label class="form-check-label" for="show-only-encrypted">只显示加密数据包</label>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info" id="no-data-message">
                        请上传PCAP文件进行分析
                    </div>
                    <div id="results-container" style="display: none;">
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">总数据包数</h5>
                                        <h3 id="total-packets">0</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-dark">
                                    <div class="card-body">
                                        <h5 class="card-title">加密数据包</h5>
                                        <h3 id="encrypted-packets">0</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">未加密数据包</h5>
                                        <h3 id="unencrypted-packets">0</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">加密比例</h5>
                                        <h3 id="encryption-ratio">0%</h3>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 熵可视化部分 -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="card-title mb-0">熵值可视化</h5>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary active" id="entropy-histogram-btn">熵值直方图</button>
                                            <button type="button" class="btn btn-sm btn-outline-primary" id="entropy-scatter-btn">熵与大小散点图</button>
                                            <button type="button" class="btn btn-sm btn-outline-primary" id="entropy-timeline-btn">熵值时间线</button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div id="entropy-visualization" style="height: 400px;"></div>
                                        <div class="text-center mt-2">
                                            <small class="text-muted">熵值是检测加密的关键指标，高熵值(接这7-8)通常表示加密数据</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card shadow-sm">
                                    <div class="card-header bg-gradient-light">
                                        <h5 class="card-title mb-0">
                                            <i class="fas fa-chart-pie me-2"></i>
                                            加密协议分布
                                        </h5>
                                    </div>
                                    <div class="card-body p-3">
                                        <div id="encryption-protocols-chart-container" style="height: 350px;">
                                            <canvas id="encryption-protocols-chart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card shadow-sm">
                                    <div class="card-header bg-gradient-light">
                                        <h5 class="card-title mb-0">
                                            <i class="fas fa-chart-line me-2"></i>
                                            加密流量趋势
                                        </h5>
                                    </div>
                                    <div class="card-body p-3">
                                        <div style="height: 350px;">
                                            <canvas id="encryption-trend-chart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- JA3指纹标签云已移除 -->

                        <!-- JA3指纹详情模态框已移除 -->

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <button class="btn btn-sm btn-outline-secondary" id="view-model-metrics">
                                    <i class="fas fa-chart-line me-1"></i>查看模型指标
                                </button>
                            </div>
                        </div>
                        <table class="table table-hover" id="packet-table">
                            <thead>
                                <tr>
                                    <th>编号</th>
                                    <th>时间</th>
                                    <th>源地址</th>
                                    <th>目标地址</th>
                                    <th>协议</th>
                                    <th>长度</th>
                                    <th>加密状态</th>
                                    <th>加密类型</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据包详情模态框 -->
<div class="modal fade" id="packet-detail-modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">数据包详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="accordion" id="packet-detail-accordion">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模型指标模态框 -->
<div class="modal fade" id="model-metrics-modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">模型性能指标</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="metrics-loading" class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p>加载模型指标...</p>
                </div>
                <div id="metrics-content" style="display: none;">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">准确率</h5>
                                    <h3 id="metric-accuracy">-</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">延迟 (ms)</h5>
                                    <h3 id="metric-latency">-</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">总预测次数</h5>
                                    <h3 id="metric-total-predictions">-</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">性能下降计数</h5>
                                    <h3 id="metric-decline-counter">-</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">准确率历史</h5>
                                    <div id="accuracy-history-chart" style="height: 250px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="metrics-error" class="alert alert-danger" style="display: none;">
                    无法加载模型指标。请确保模型已初始化并且有足够的数据。
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/plotly.js/2.5.1/plotly.min.js"></script>
<script>
$(document).ready(function() {
    let protocolsChart = null;
    let trendChart = null;

    // 存储原始数据包列表
    let allPackets = [];

    // 切换显示模式
    $('#show-only-encrypted').change(function() {
        const showOnlyEncrypted = $(this).is(':checked');
        console.log('切换显示模式:', showOnlyEncrypted ? '只显示加密' : '显示全部');

        // 更新表格
        const tbody = $('#packet-table tbody');
        tbody.empty();

        // 过滤数据包
        const packetsToShow = showOnlyEncrypted ? allPackets.filter(p => p.is_encrypted) : allPackets;
        console.log('过滤后数据包数量:', packetsToShow.length);

        // 添加数据包到表格
        packetsToShow.forEach(packet => {
            tbody.append(`
                <tr class="${packet.is_encrypted ? 'table-info' : ''}" data-packet-id="${packet.id}">
                    <td>${packet.number}</td>
                    <td>${packet.time}</td>
                    <td>${packet.src}</td>
                    <td>${packet.dst}</td>
                    <td>${packet.protocol}</td>
                    <td>${packet.length}</td>
                    <td>${packet.is_encrypted ? '加密' : '未加密'}</td>
                    <td>${packet.encryption_type || '-'}</td>
                </tr>
            `);
        });
    });

    // 全局变量用于跟踪文件处理
    let filesToProcess = [];
    let currentFileIndex = 0;
    let totalFiles = 0;
    let startTime = 0;
    let fileStartTime = 0;
    let processedFilesSize = 0;
    let totalFilesSize = 0;
    let analysisResults = [];

    // 处理文件上传
    $('#upload-analyze-btn').on('click', function() {
        const fileInput = $('#pcap-file')[0];

        if (!fileInput.files.length) {
            alert('请选择至少一个 PCAP 文件');
            return;
        }

        // 重置全局变量
        filesToProcess = [];
        currentFileIndex = 0;
        totalFiles = fileInput.files.length;
        startTime = Date.now();
        fileStartTime = startTime;
        processedFilesSize = 0;
        totalFilesSize = 0;
        analysisResults = [];

        // 计算文件总大小
        for (let i = 0; i < fileInput.files.length; i++) {
            totalFilesSize += fileInput.files[i].size;
        }

        // 准备文件列表显示
        const fileListContainer = $('#file-list');
        fileListContainer.empty();

        for (let i = 0; i < fileInput.files.length; i++) {
            const file = fileInput.files[i];
            filesToProcess.push(file);

            // 创建文件项
            const fileItem = $(`
                <div class="file-item mb-2" id="file-item-${i}">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="file-name">${file.name}</span>
                        <span class="file-status badge bg-secondary">等待中</span>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div id="progress-bar-${i}" class="progress-bar" role="progressbar" style="width: 0%"
                             aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            `);

            fileListContainer.append(fileItem);
        }

        // 显示进度区域
        $('#analysis-progress').show();
        $('#no-data-message').hide();
        $('#results-container').hide();

        // 更新总进度条和状态
        updateTotalProgress(0);
        $('#progress-status').text('正在上传文件...');

        // 开始处理第一个文件
        processNextFile();
    });

    // 处理下一个文件
    function processNextFile() {
        if (currentFileIndex >= filesToProcess.length) {
            // 所有文件处理完毕
            finishProcessing();
            return;
        }

        const file = filesToProcess[currentFileIndex];
        const fileItem = $(`#file-item-${currentFileIndex}`);
        fileItem.find('.file-status').removeClass('bg-secondary').addClass('bg-primary').text('上传中');

        // 重置当前文件的开始时间
        fileStartTime = Date.now();

        // 上传文件
        const formData = new FormData();
        formData.append('file', file);

        $.ajax({
            url: '/upload-pcap',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhr: function() {
                const xhr = new window.XMLHttpRequest();

                // 上传进度
                xhr.upload.addEventListener('progress', function(evt) {
                    if (evt.lengthComputable) {
                        const percentComplete = (evt.loaded / evt.total) * 100;
                        $(`#progress-bar-${currentFileIndex}`).css('width', percentComplete + '%');

                        // 更新总进度
                        const overallProgress = (processedFilesSize + evt.loaded) / totalFilesSize * 100 / 2; // 上传占总进度50%
                        updateTotalProgress(overallProgress);

                        // 更新预计时间
                        updateEstimatedTime(evt.loaded, file.size, overallProgress);
                    }
                }, false);

                return xhr;
            },
            success: function(response) {
                if (response.success) {
                    fileItem.find('.file-status').removeClass('bg-primary').addClass('bg-info').text('分析中');
                    $(`#progress-bar-${currentFileIndex}`).css('width', '100%');

                    // 分析文件
                    analyzeFile(response.file_path, currentFileIndex);
                } else {
                    fileItem.find('.file-status').removeClass('bg-primary').addClass('bg-danger').text('失败');
                    console.error('文件上传失败:', response.error);

                    // 继续处理下一个文件
                    processedFilesSize += file.size;
                    currentFileIndex++;
                    processNextFile();
                }
            },
            error: function(xhr, status, error) {
                fileItem.find('.file-status').removeClass('bg-primary').addClass('bg-danger').text('失败');
                console.error('文件上传错误:', status, error);

                // 继续处理下一个文件
                processedFilesSize += file.size;
                currentFileIndex++;
                processNextFile();
            }
        });
    }

    // 分析文件
    function analyzeFile(filePath, fileIndex) {
        const modelType = $('#model-type').val();
        const file = filesToProcess[fileIndex];

        // 更新状态
        $('#progress-status').text(`正在分析: ${file.name}`);

        // 创建一个模拟进度的定时器，因为后端不提供实时进度
        let analysisProgress = 50; // 从50%开始（上传占前50%）
        const progressInterval = setInterval(() => {
            // 模拟分析进度，每次增加1%，但不超过95%
            if (analysisProgress < 95) {
                analysisProgress += 1;

                // 计算总进度（上传占50%，分析占50%）
                const overallProgress = 50 + (analysisProgress - 50) * (file.size / totalFilesSize);
                updateTotalProgress(overallProgress);

                // 更新预计时间
                const elapsedTime = Date.now() - fileStartTime;
                const progress = (analysisProgress - 50) / 50; // 分析进度百分比
                if (progress > 0) {
                    const totalEstimatedTime = elapsedTime / progress;
                    const remainingTime = totalEstimatedTime - elapsedTime;
                    updateEstimatedTimeDisplay(remainingTime);
                }
            }
        }, 500);

        $.ajax({
            url: '/api/analyze-encryption',
            type: 'POST',
            data: JSON.stringify({
                file_path: filePath,
                model_type: modelType
            }),
            contentType: 'application/json',
            success: function(data) {
                // 清除进度定时器
                clearInterval(progressInterval);

                // 更新文件状态
                const fileItem = $(`#file-item-${fileIndex}`);

                if (data.error) {
                    fileItem.find('.file-status').removeClass('bg-info').addClass('bg-danger').text('分析失败');
                    console.error('分析错误:', data.error);
                } else {
                    fileItem.find('.file-status').removeClass('bg-info').addClass('bg-success').text('完成');
                    $(`#progress-bar-${fileIndex}`).css('width', '100%').addClass('bg-success');

                    // 存储分析结果
                    analysisResults.push(data);

                    // 添加到文件选择器
                    addFileToResultSelector(file.name, analysisResults.length - 1);

                    // 如果是第一个文件，显示结果
                    if (analysisResults.length === 1) {
                        // 更新结果显示
                        displayFileResult(0);
                        $('#results-container').show();
                    }
                }

                // 更新已处理文件大小
                processedFilesSize += file.size;

                // 更新总进度
                const overallProgress = (processedFilesSize / totalFilesSize) * 100;
                updateTotalProgress(overallProgress);

                // 处理下一个文件
                currentFileIndex++;
                processNextFile();
            },
            error: function(xhr, status, error) {
                // 清除进度定时器
                clearInterval(progressInterval);

                // 更新文件状态
                const fileItem = $(`#file-item-${fileIndex}`);
                fileItem.find('.file-status').removeClass('bg-info').addClass('bg-danger').text('分析失败');
                console.error('分析请求失败:', status, error);

                // 更新已处理文件大小
                processedFilesSize += file.size;

                // 处理下一个文件
                currentFileIndex++;
                processNextFile();
            }
        });
    }

    // 更新总进度条
    function updateTotalProgress(percentage) {
        percentage = Math.min(100, Math.max(0, percentage)); // 确保在 0-100 范围内
        const roundedPercentage = Math.round(percentage);

        $('#total-progress-bar').css('width', roundedPercentage + '%');
        $('#total-progress-bar').attr('aria-valuenow', roundedPercentage);
        $('#total-progress-bar').text(roundedPercentage + '%');
        $('#progress-percentage').text(roundedPercentage + '%');

        // 如果完成，改变颜色
        if (roundedPercentage >= 100) {
            $('#total-progress-bar').removeClass('progress-bar-animated').addClass('bg-success');
            $('#progress-status').text('分析完成');
        }
    }

    // 更新预计时间显示
    function updateEstimatedTimeDisplay(milliseconds) {
        if (milliseconds <= 0) {
            $('#estimated-time').text('即将完成');
            return;
        }

        // 转换为秒
        const seconds = Math.round(milliseconds / 1000);

        if (seconds < 60) {
            $('#estimated-time').text(`预计剩余时间: ${seconds} 秒`);
        } else if (seconds < 3600) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            $('#estimated-time').text(`预计剩余时间: ${minutes} 分 ${remainingSeconds} 秒`);
        } else {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            $('#estimated-time').text(`预计剩余时间: ${hours} 小时 ${minutes} 分`);
        }
    }

    // 更新预计时间
    function updateEstimatedTime(loaded, total, overallProgress) {
        const elapsedTime = Date.now() - startTime;
        const progress = overallProgress / 100;

        if (progress > 0) {
            const totalEstimatedTime = elapsedTime / progress;
            const remainingTime = totalEstimatedTime - elapsedTime;
            updateEstimatedTimeDisplay(remainingTime);
        }
    }

    // 完成处理
    function finishProcessing() {
        // 更新总进度
        updateTotalProgress(100);
        $('#progress-status').text('分析完成');
        $('#estimated-time').text('已完成');

        // 如果没有成功的分析结果，显示错误信息
        if (analysisResults.length === 0) {
            $('#analysis-progress').hide();
            $('#no-data-message').show();
            alert('所有文件分析失败，请重试');
            return;
        }

        // 如果有多个文件分析成功，显示成功消息并显示文件选择器
        if (analysisResults.length > 1) {
            const successCount = analysisResults.length;
            const totalCount = filesToProcess.length;

            // 显示文件选择器
            $('#results-file-selector').removeClass('d-none');

            // 显示合并结果
            displayCombinedResults();

            alert(`分析完成！共 ${totalCount} 个文件，成功分析 ${successCount} 个。\n已显示所有文件的合并结果。`);
        }
    }

    // 添加文件到结果选择器
    function addFileToResultSelector(fileName, index) {
        // 如果有多个文件，显示选择器
        if (analysisResults.length > 1) {
            $('#results-file-selector').removeClass('d-none');
        }

        // 添加文件选项
        $('#file-result-selector').append(`<option value="${index}">${fileName}</option>`);
    }

    // 显示指定文件的结果
    function displayFileResult(index) {
        if (index < 0 || index >= analysisResults.length) {
            console.error('无效的文件索引:', index);
            return;
        }

        const data = analysisResults[index];

        // 存储原始数据包列表
        allPackets = data.packets || [];

        // 更新结果显示
        updateResults(data);

        // 显示模型信息
        if (data.model_info) {
            const modelInfo = `使用模型: ${data.model_info.description}`;
            $('#model-info-display').text(modelInfo);
        }
    }

    // 显示合并结果
    function displayCombinedResults() {
        if (analysisResults.length === 0) {
            return;
        }

        // 使用第一个结果作为基础
        const baseResult = JSON.parse(JSON.stringify(analysisResults[0]));

        // 合并所有数据包
        let allPackets = [];
        let totalEncryptedPackets = 0;
        let totalPackets = 0;

        // 合并协议统计
        const combinedProtocolStats = {};

        // 合并时间序列
        const combinedTimeSeries = [];

        // 合并熵值数据
        const combinedEntropyData = [];

        // 遍历所有结果
        for (const result of analysisResults) {
            // 合并数据包
            if (result.packets && Array.isArray(result.packets)) {
                allPackets = allPackets.concat(result.packets);
            }

            // 统计加密数据包
            if (result.summary) {
                totalEncryptedPackets += result.summary.encrypted_packets || 0;
                totalPackets += result.summary.total_packets || 0;

                // 合并协议统计
                if (result.protocol_stats) {
                    for (const [protocol, count] of Object.entries(result.protocol_stats)) {
                        combinedProtocolStats[protocol] = (combinedProtocolStats[protocol] || 0) + count;
                    }
                }

                // 合并熵值数据
                if (result.summary.entropy_data && Array.isArray(result.summary.entropy_data)) {
                    combinedEntropyData.push(...result.summary.entropy_data);
                }
            }

            // 合并时间序列
            if (result.time_series && Array.isArray(result.time_series)) {
                combinedTimeSeries.push(...result.time_series);
            }
        }

        // 更新基础结果
        baseResult.packets = allPackets;
        baseResult.protocol_stats = combinedProtocolStats;
        baseResult.time_series = combinedTimeSeries;

        if (!baseResult.summary) {
            baseResult.summary = {};
        }

        baseResult.summary.total_packets = totalPackets;
        baseResult.summary.encrypted_packets = totalEncryptedPackets;
        baseResult.summary.encryption_ratio = totalPackets > 0 ? (totalEncryptedPackets / totalPackets) : 0;
        baseResult.summary.entropy_data = combinedEntropyData;

        // 更新结果显示
        updateResults(baseResult);

        // 更新模型信息
        $('#model-info-display').text('合并所有文件的结果');
    }



    // 更新结果
    function updateResults(data) {
        console.log('开始更新结果，数据结构:', Object.keys(data));

        // 检查数据包是否存在
        if (!data.packets || !Array.isArray(data.packets)) {
            console.error('数据包不存在或不是数组:', data.packets);
            alert('数据格式错误: 缺少数据包信息');
            return;
        }

        // 存储原始数据包列表供过滤使用
        allPackets = data.packets;
        console.log('数据包总数:', allPackets.length);

        // 更新统计信息
        const totalPackets = data.packets.length;
        const encryptedPackets = data.packets.filter(p => p.is_encrypted).length;
        const unencryptedPackets = totalPackets - encryptedPackets;
        const encryptionRatio = totalPackets > 0 ? ((encryptedPackets / totalPackets) * 100).toFixed(2) : '0.00';

        console.log('统计信息:', {
            totalPackets,
            encryptedPackets,
            unencryptedPackets,
            encryptionRatio
        });

        $('#total-packets').text(totalPackets);
        $('#encrypted-packets').text(encryptedPackets);
        $('#unencrypted-packets').text(unencryptedPackets);
        $('#encryption-ratio').text(encryptionRatio + '%');

        // 检查协议统计是否存在
        if (!data.protocol_stats) {
            console.error('协议统计不存在:', data);
            alert('数据格式错误: 缺少协议统计信息');
            return;
        }

        // 保存当前协议统计
        currentProtocolStats = data.protocol_stats;

        console.log('协议统计:', currentProtocolStats);

        // 更新加密协议分布图表
        try {
            updateProtocolsChart(data.protocol_stats);
        } catch (e) {
            console.error('更新协议图表错误:', e);
            alert('更新协议图表失败: ' + e.message);
        }

        // 检查时间序列是否存在
        if (!data.time_series || !Array.isArray(data.time_series)) {
            console.error('时间序列不存在或不是数组:', data.time_series);
        } else {
            // 更新加密流量趋势图表
            try {
                updateTrendChart(data.time_series);
            } catch (e) {
                console.error('更新趋势图表错误:', e);
            }
        }

        // 检查熵值数据是否存在
        console.log('检查熵值数据:', data.summary);
        if (data.summary && data.summary.entropy_data && data.summary.entropy_data.length > 0) {
            console.log('找到熵值数据:', data.summary.entropy_data.length, '条');
            try {
                updateEntropyVisualization(data.summary.entropy_data, 'histogram');
            } catch (e) {
                console.error('更新熵值可视化错误:', e);
            }
        } else {
            console.log('未找到熵值数据');
        }

        // 检查是否只显示加密数据包
        const showOnlyEncrypted = $('#show-only-encrypted').is(':checked');
        const packetsToShow = showOnlyEncrypted ? data.packets.filter(p => p.is_encrypted) : data.packets;

        // 更新数据包表格
        const tbody = $('#packet-table tbody');
        tbody.empty();

        packetsToShow.forEach(packet => {
            tbody.append(`
                <tr class="${packet.is_encrypted ? 'table-info' : ''}" data-packet-id="${packet.id}">
                    <td>${packet.number}</td>
                    <td>${packet.time}</td>
                    <td>${packet.src}</td>
                    <td>${packet.dst}</td>
                    <td>${packet.protocol}</td>
                    <td>${packet.length}</td>
                    <td>${packet.is_encrypted ? '加密' : '未加密'}</td>
                    <td>${packet.encryption_type || '-'}</td>
                </tr>
            `);
        });
    }

    // 更新加密协议分布图表 - 增强版环形图
    function updateProtocolsChart(protocolStats) {
        const ctx = document.getElementById('encryption-protocols-chart').getContext('2d');

        if (protocolsChart) {
            protocolsChart.destroy();
        }

        // 处理数据，按数量排序
        const sortedData = Object.entries(protocolStats)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 8); // 最多显示8种协议

        // 如果有更多协议，将它们合并为"其他"
        let otherCount = 0;
        if (Object.keys(protocolStats).length > 8) {
            const totalCount = Object.values(protocolStats).reduce((sum, count) => sum + count, 0);
            const displayedCount = sortedData.reduce((sum, [_, count]) => sum + count, 0);
            otherCount = totalCount - displayedCount;

            if (otherCount > 0) {
                sortedData.push(['其他', otherCount]);
            }
        }

        // 协议分布环形图
        protocolsChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: sortedData.map(([label, _]) => label),
                datasets: [{
                    data: sortedData.map(([_, value]) => value),
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.8)',   // 红色
                        'rgba(54, 162, 235, 0.8)',  // 蓝色
                        'rgba(255, 206, 86, 0.8)',  // 黄色
                        'rgba(75, 192, 192, 0.8)',  // 青色
                        'rgba(153, 102, 255, 0.8)', // 紫色
                        'rgba(255, 159, 64, 0.8)',  // 橙色
                        'rgba(77, 83, 96, 0.8)',    // 灰色
                        'rgba(0, 181, 204, 0.8)',   // 青绿色
                        'rgba(169, 169, 169, 0.8)'  // 深灰色(其他)
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(77, 83, 96, 1)',
                        'rgba(0, 181, 204, 1)',
                        'rgba(169, 169, 169, 1)'
                    ],
                    borderWidth: 2,
                    hoverOffset: 8,
                    hoverBorderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '60%',
                layout: {
                    padding: 20
                },
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            padding: 15,
                            usePointStyle: true,
                            pointStyle: 'circle',
                            font: {
                                size: 12
                            }
                        }
                    },
                    title: {
                        display: true,
                        text: '加密协议分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        padding: {
                            top: 10,
                            bottom: 15
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((sum, val) => sum + val, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        },
                        padding: 12,
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleFont: {
                            size: 14
                        },
                        bodyFont: {
                            size: 13
                        },
                        displayColors: true,
                        boxPadding: 5
                    }
                },
                animation: {
                    animateScale: true,
                    animateRotate: true,
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });
    }

    // JA3客户端数据处理功能已移除

    // JA3标签云功能已移除

    // JA3详情功能已移除

    // 更新加密流量趋势图表 - 增强版
    function updateTrendChart(timeSeries) {
        const ctx = document.getElementById('encryption-trend-chart').getContext('2d');

        if (trendChart) {
            trendChart.destroy();
        }

        // 处理数据，确保时间序列有效
        if (!timeSeries || !timeSeries.length) {
            // 如果没有数据，创建示例数据
            timeSeries = Array.from({length: 10}, (_, i) => ({
                time: `T${i+1}`,
                encryption_ratio: Math.random() * 100,
                encrypted_packets: Math.floor(Math.random() * 50),
                total_packets: Math.floor(Math.random() * 100) + 50
            }));
        }

        // 提取数据
        const labels = timeSeries.map(point => point.time);
        const encryptionRatios = timeSeries.map(point => point.encryption_ratio);
        const encryptedPackets = timeSeries.map(point => point.encrypted_packets || 0);
        const totalPackets = timeSeries.map(point => point.total_packets || 0);

        // 计算移动平均线（平滑曲线）
        const smoothedData = [];
        const windowSize = 3; // 移动平均窗口大小

        for (let i = 0; i < encryptionRatios.length; i++) {
            let sum = 0;
            let count = 0;

            for (let j = Math.max(0, i - windowSize + 1); j <= Math.min(encryptionRatios.length - 1, i + windowSize - 1); j++) {
                sum += encryptionRatios[j];
                count++;
            }

            smoothedData.push(sum / count);
        }

        trendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: '加密流量比例',
                        data: encryptionRatios,
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        borderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6,
                        fill: true,
                        tension: 0.4
                    },
                    {
                        label: '平滑趋势线',
                        data: smoothedData,
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: false,
                        tension: 0.4,
                        borderDash: [5, 5]
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        grid: {
                            color: 'rgba(200, 200, 200, 0.2)'
                        },
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        },
                        title: {
                            display: true,
                            text: '加密比例 (%)',
                            font: {
                                size: 14,
                                weight: 'bold'
                            },
                            padding: {top: 10, bottom: 10}
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(200, 200, 200, 0.2)'
                        },
                        title: {
                            display: true,
                            text: '时间',
                            font: {
                                size: 14,
                                weight: 'bold'
                            },
                            padding: {top: 10, bottom: 10}
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 15,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleFont: {
                            size: 14
                        },
                        bodyFont: {
                            size: 13
                        },
                        padding: 12,
                        callbacks: {
                            label: function(context) {
                                const datasetLabel = context.dataset.label || '';
                                const value = context.parsed.y || 0;
                                const index = context.dataIndex;

                                if (datasetLabel === '加密流量比例') {
                                    const encrypted = encryptedPackets[index] || 0;
                                    const total = totalPackets[index] || 0;
                                    return [`${datasetLabel}: ${value.toFixed(2)}%`, `加密数据包: ${encrypted}/${total}`];
                                } else {
                                    return `${datasetLabel}: ${value.toFixed(2)}%`;
                                }
                            }
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });
    }

    // 点击数据包显示详情
    $('#packet-table').on('click', 'tr', function() {
        const packetId = $(this).data('packet-id');
        if (packetId) {
            $.get(`/api/packet-detail/${packetId}`, function(data) {
                const accordion = $('#packet-detail-accordion');
                accordion.empty();

                Object.entries(data.layers).forEach(([layer, info], index) => {
                    accordion.append(`
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button ${index === 0 ? '' : 'collapsed'}" type="button"
                                        data-bs-toggle="collapse" data-bs-target="#layer-${index}">
                                    ${layer}
                                </button>
                            </h2>
                            <div id="layer-${index}" class="accordion-collapse collapse ${index === 0 ? 'show' : ''}"
                                 data-bs-parent="#packet-detail-accordion">
                                <div class="accordion-body">
                                    <pre><code>${JSON.stringify(info, null, 2)}</code></pre>
                                </div>
                            </div>
                        </div>
                    `);
                });

                $('#packet-detail-modal').modal('show');
            });
        }
    });

    // 点击查看模型指标按钮
    $('#view-model-metrics').click(function() {
        // 显示模态框和加载状态
        $('#metrics-loading').show();
        $('#metrics-content').hide();
        $('#metrics-error').hide();
        $('#model-metrics-modal').modal('show');

        // 请求模型指标数据
        $.get('/api/model-metrics', { model: 'encryption_detector' }, function(response) {
            $('#metrics-loading').hide();

            if (response.success && response.metrics) {
                // 更新指标显示
                const metrics = response.metrics;

                // 格式化数字显示
                $('#metric-accuracy').text(metrics.accuracy ? (metrics.accuracy * 100).toFixed(2) + '%' : '-');
                $('#metric-latency').text(metrics.latency ? metrics.latency.toFixed(2) : '-');
                $('#metric-total-predictions').text(metrics.total_predictions || 0);
                $('#metric-decline-counter').text(metrics.decline_counter || 0);

                // 如果有准确率历史数据，绘制图表
                if (metrics.accuracy_history && metrics.accuracy_history.length > 0) {
                    const chartData = {
                        x: Array.from({ length: metrics.accuracy_history.length }, (_, i) => i + 1),
                        y: metrics.accuracy_history.map(val => val * 100), // 转换为百分比
                        type: 'scatter',
                        mode: 'lines+markers',
                        name: '准确率',
                        line: { color: '#4e73df' }
                    };

                    const layout = {
                        margin: { t: 30, r: 30, l: 50, b: 50 },
                        xaxis: { title: '预测次数' },
                        yaxis: { title: '准确率 (%)', range: [0, 100] }
                    };

                    Plotly.newPlot('accuracy-history-chart', [chartData], layout);
                } else {
                    $('#accuracy-history-chart').html('<div class="text-center text-muted">\u6ca1\u6709\u8db3\u591f\u7684\u5386\u53f2\u6570\u636e</div>');
                }

                $('#metrics-content').show();
            } else {
                $('#metrics-error').show();
                console.error('获取模型指标失败:', response.error || '未知错误');
            }
        }).fail(function(xhr, status, error) {
            $('#metrics-loading').hide();
            $('#metrics-error').show();
            console.error('请求模型指标失败:', status, error);
        });
    });

    // 熵值可视化相关函数
    let currentEntropyData = [];
    let currentEntropyView = 'histogram';

    // 更新熵值可视化
    function updateEntropyVisualization(entropyData, viewType) {
        // 保存数据和当前视图类型
        currentEntropyData = entropyData;
        currentEntropyView = viewType || currentEntropyView;

        // 根据视图类型选择不同的可视化方式
        switch(currentEntropyView) {
            case 'histogram':
                renderEntropyHistogram(entropyData);
                break;
            case 'scatter':
                renderEntropyScatter(entropyData);
                break;
            case 'timeline':
                renderEntropyTimeline(entropyData);
                break;
            default:
                renderEntropyHistogram(entropyData);
        }
    }

    // 渲染熵值直方图
    function renderEntropyHistogram(entropyData) {
        // 提取熵值数据
        const encryptedEntropies = entropyData.filter(d => d.is_encrypted).map(d => d.entropy);
        const unencryptedEntropies = entropyData.filter(d => !d.is_encrypted).map(d => d.entropy);

        // 创建直方图数据
        const data = [
            {
                x: encryptedEntropies,
                type: 'histogram',
                name: '加密数据包',
                opacity: 0.7,
                marker: {
                    color: 'rgba(255, 100, 102, 0.7)',
                },
                xbins: {
                    size: 0.2
                }
            },
            {
                x: unencryptedEntropies,
                type: 'histogram',
                name: '非加密数据包',
                opacity: 0.7,
                marker: {
                    color: 'rgba(100, 149, 237, 0.7)',
                },
                xbins: {
                    size: 0.2
                }
            }
        ];

        // 设置布局
        const layout = {
            title: '数据包熵值分布',
            xaxis: {
                title: '熵值',
                range: [0, 8]
            },
            yaxis: {
                title: '数据包数量'
            },
            barmode: 'overlay',
            legend: {
                x: 0.1,
                y: 1
            },
            shapes: [{
                type: 'line',
                x0: 7,
                y0: 0,
                x1: 7,
                y1: 1,
                yref: 'paper',
                line: {
                    color: 'red',
                    width: 2,
                    dash: 'dash'
                }
            }],
            annotations: [{
                x: 7.2,
                y: 0.9,
                yref: 'paper',
                text: '加密阈值',
                showarrow: false,
                font: {
                    color: 'red'
                }
            }]
        };

        // 渲染图表
        Plotly.newPlot('entropy-visualization', data, layout);
    }

    // 渲染熵值与大小散点图
    function renderEntropyScatter(entropyData) {
        // 创建数据
        const encryptedData = entropyData.filter(d => d.is_encrypted);
        const unencryptedData = entropyData.filter(d => !d.is_encrypted);

        const data = [
            {
                x: encryptedData.map(d => d.size),
                y: encryptedData.map(d => d.entropy),
                mode: 'markers',
                type: 'scatter',
                name: '加密数据包',
                marker: {
                    color: 'rgba(255, 100, 102, 0.7)',
                    size: 8
                }
            },
            {
                x: unencryptedData.map(d => d.size),
                y: unencryptedData.map(d => d.entropy),
                mode: 'markers',
                type: 'scatter',
                name: '非加密数据包',
                marker: {
                    color: 'rgba(100, 149, 237, 0.7)',
                    size: 8
                }
            }
        ];

        // 设置布局
        const layout = {
            title: '数据包大小与熵值关系',
            xaxis: {
                title: '数据包大小 (bytes)'
            },
            yaxis: {
                title: '熵值',
                range: [0, 8]
            },
            shapes: [{
                type: 'line',
                x0: 0,
                y0: 7,
                x1: 1,
                y1: 7,
                xref: 'paper',
                line: {
                    color: 'red',
                    width: 2,
                    dash: 'dash'
                }
            }]
        };

        // 渲染图表
        Plotly.newPlot('entropy-visualization', data, layout);
    }

    // 渲染熵值时间线
    function renderEntropyTimeline(entropyData) {
        // 按包序号排序
        const sortedData = [...entropyData].sort((a, b) => a.packet_number - b.packet_number);

        // 创建数据
        const data = [
            {
                x: sortedData.map(d => d.packet_number),
                y: sortedData.map(d => d.entropy),
                mode: 'lines+markers',
                type: 'scatter',
                name: '熵值',
                line: {
                    color: 'rgba(100, 149, 237, 0.7)'
                },
                marker: {
                    color: sortedData.map(d => d.is_encrypted ? 'rgba(255, 100, 102, 0.7)' : 'rgba(100, 149, 237, 0.7)'),
                    size: 6
                }
            }
        ];

        // 设置布局
        const layout = {
            title: '熵值随时间变化',
            xaxis: {
                title: '数据包序号'
            },
            yaxis: {
                title: '熵值',
                range: [0, 8]
            },
            shapes: [{
                type: 'line',
                x0: 0,
                y0: 7,
                x1: 1,
                y1: 7,
                xref: 'paper',
                line: {
                    color: 'red',
                    width: 2,
                    dash: 'dash'
                }
            }]
        };

        // 渲染图表
        Plotly.newPlot('entropy-visualization', data, layout);
    }

    // 绑定熵值可视化切换按钮事件
    $('#entropy-histogram-btn').click(function() {
        $(this).addClass('active').siblings().removeClass('active');
        updateEntropyVisualization(currentEntropyData, 'histogram');
    });

    $('#entropy-scatter-btn').click(function() {
        $(this).addClass('active').siblings().removeClass('active');
        updateEntropyVisualization(currentEntropyData, 'scatter');
    });

    $('#entropy-timeline-btn').click(function() {
        $(this).addClass('active').siblings().removeClass('active');
        updateEntropyVisualization(currentEntropyData, 'timeline');
    });

    // 文件结果选择器事件
    $('#file-result-selector').change(function() {
        const selectedValue = $(this).val();

        if (selectedValue === 'combined') {
            // 显示合并结果
            displayCombinedResults();
        } else {
            // 显示指定文件的结果
            const index = parseInt(selectedValue);
            if (!isNaN(index) && index >= 0 && index < analysisResults.length) {
                displayFileResult(index);
            }
        }
    });

});
</script>
{% endblock %}