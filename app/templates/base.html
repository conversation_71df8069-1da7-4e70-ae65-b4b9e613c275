<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络流量分析系统</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.0.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4361ee;
            --primary-hover: #3a56d4;
            --sidebar-width: 250px;
            --sidebar-bg: #f8f9fa;
            --sidebar-hover: #e9ecef;
            --sidebar-active: #4361ee;
            --sidebar-active-text: #ffffff;
            --sidebar-text: #495057;
            --transition-speed: 0.3s;
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --header-height: 60px;
        }

        body {
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            transition: background-color var(--transition-speed), color var(--transition-speed);
            padding-top: var(--header-height);
        }

        /* 顶部导航栏 */
        .top-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--header-height);
            background-color: #ffffff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            z-index: 1030;
            display: flex;
            align-items: center;
            padding: 0 1rem;
            transition: background-color var(--transition-speed);
        }

        .navbar-brand {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-color);
            display: flex;
            align-items: center;
        }

        .navbar-brand i {
            margin-right: 0.5rem;
            font-size: 1.5rem;
        }

        /* 侧边栏 */
        .sidebar {
            position: fixed;
            top: var(--header-height);
            bottom: 0;
            left: 0;
            width: var(--sidebar-width);
            z-index: 100;
            background-color: var(--sidebar-bg);
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            transition: all var(--transition-speed);
            overflow-y: auto;
        }

        .sidebar-sticky {
            padding-top: 1rem;
        }

        .nav-link {
            color: var(--sidebar-text);
            padding: 0.75rem 1.25rem;
            border-radius: 0.25rem;
            margin: 0.25rem 0.75rem;
            display: flex;
            align-items: center;
            transition: all var(--transition-speed);
        }

        .nav-link i {
            margin-right: 0.75rem;
            font-size: 1.1rem;
            width: 1.5rem;
            text-align: center;
            transition: all var(--transition-speed);
        }

        .nav-link:hover {
            background-color: var(--sidebar-hover);
            transform: translateX(5px);
        }

        .nav-link.active {
            background-color: var(--sidebar-active);
            color: var(--sidebar-active-text);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .nav-link.active i {
            transform: scale(1.2);
        }

        /* 主内容区域 */
        .main-content {
            margin-left: var(--sidebar-width);
            padding: 1.5rem;
            transition: margin-left var(--transition-speed);
        }

        /* 卡片样式 */
        .card {
            border: none;
            border-radius: 0.5rem;
            box-shadow: var(--card-shadow);
            transition: all var(--transition-speed);
            overflow: hidden;
        }

        .card:hover {
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-3px);
        }

        .card-header {
            background-color: rgba(0, 0, 0, 0.03);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1rem 1.25rem;
        }

        /* 暗黑模式 */
        .dark-mode {
            --sidebar-bg: #1e1e1e;
            --sidebar-hover: #2d2d2d;
            --sidebar-text: #e0e0e0;
            background-color: #121212;
            color: #e0e0e0;
        }

        .dark-mode .top-navbar {
            background-color: #1e1e1e;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .dark-mode .navbar-brand {
            color: #e0e0e0;
        }

        .dark-mode .card {
            background-color: #2d2d2d;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }

        .dark-mode .card-header {
            background-color: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .dark-mode .table {
            color: #e0e0e0;
        }

        /* 暗黑模式切换按钮 */
        .dark-mode-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all var(--transition-speed);
        }

        .dark-mode-toggle:hover {
            transform: scale(1.1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .navbar-toggler {
                display: block;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="top-navbar">
        <button class="navbar-toggler d-md-none" type="button" id="sidebarToggle">
            <i class="fas fa-bars"></i>
        </button>
        <a class="navbar-brand" href="/">
            <i class="fas fa-network-wired"></i>
            网络流量监控系统
        </a>
        <div class="ms-auto"></div>
    </nav>

    <!-- 侧边导航栏 -->
    <nav class="sidebar">
        <div class="sidebar-sticky">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="/">
                        <i class="fas fa-chart-line"></i> 网络流量监控
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/anomaly-detection">
                        <i class="fas fa-exclamation-triangle"></i> 异常检测
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/encryption-detection">
                        <i class="fas fa-lock"></i> 加密检测
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="container-fluid py-3">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <h1 class="page-title h3 mb-0">{% block page_title %}{% endblock %}</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="/"><i class="fas fa-home"></i></a></li>
                                {% block breadcrumb %}{% endblock %}
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>
            {% block content %}{% endblock %}
        </div>
    </main>

    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.0.2/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/plotly.js/2.5.1/plotly.min.js"></script>
    <button class="btn btn-primary dark-mode-toggle" id="darkModeToggle" title="切换到暗黑模式">
        <i class="fas fa-moon"></i>
    </button>
    <script>
        $(document).ready(function() {
            // 高亮当前页面的导航链接
            const currentPath = window.location.pathname;
            $('.nav-link').each(function() {
                const linkPath = $(this).attr('href');
                if (currentPath === linkPath) {
                    $(this).addClass('active');
                }
            });

            // 响应式侧边栏切换
            $('#sidebarToggle').click(function() {
                $('.sidebar').toggleClass('show');
            });

            // 点击主内容区域时关闭移动端侧边栏
            $('.main-content').click(function() {
                if ($('.sidebar').hasClass('show') && window.innerWidth < 768) {
                    $('.sidebar').removeClass('show');
                }
            });

            // 暗黑模式切换
            const darkModeToggle = $('#darkModeToggle');

            // 检查本地存储
            if (localStorage.getItem('darkMode') === 'enabled') {
                $('body').addClass('dark-mode');
                darkModeToggle.html('<i class="fas fa-sun"></i>');
                darkModeToggle.attr('title', '切换到明亮模式');
            } else {
                darkModeToggle.html('<i class="fas fa-moon"></i>');
                darkModeToggle.attr('title', '切换到暗黑模式');
            }

            // 切换事件
            darkModeToggle.click(function() {
                $('body').toggleClass('dark-mode');

                if ($('body').hasClass('dark-mode')) {
                    localStorage.setItem('darkMode', 'enabled');
                    darkModeToggle.html('<i class="fas fa-sun"></i>');
                    darkModeToggle.attr('title', '切换到明亮模式');
                } else {
                    localStorage.setItem('darkMode', 'disabled');
                    darkModeToggle.html('<i class="fas fa-moon"></i>');
                    darkModeToggle.attr('title', '切换到暗黑模式');
                }
            });

            // 添加卡片悬停效果
            $('.card').hover(
                function() {
                    $(this).addClass('card-hover');
                },
                function() {
                    $(this).removeClass('card-hover');
                }
            );
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>