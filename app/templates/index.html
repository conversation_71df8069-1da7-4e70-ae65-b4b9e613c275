{% extends "base.html" %}

{% block page_title %}网络流量监控{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active" aria-current="page">网络流量监控</li>
{% endblock %}

{% block content %}
<div class="container-fluid">

    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">实时流量捕获</h5>
                    <div>
                        <button id="save-capture-btn" class="btn btn-success" disabled>
                            <i class="fas fa-save me-1"></i> 保存为PCAP
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="input-group">
                                <select class="form-select" id="interface-select">
                                    <option value="">选择网络接口...</option>
                                </select>
                                <button class="btn btn-primary" id="start-capture-btn">开始捕获</button>
                                <button class="btn btn-danger" id="stop-capture-btn" disabled>停止捕获</button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <span class="badge bg-primary rounded-pill" id="status-badge">就绪</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <h5>实时流量统计</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body">
                                            <h5 class="card-title">总数据包数</h5>
                                            <h3 id="total-packets">0</h3>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-success text-white">
                                        <div class="card-body">
                                            <h5 class="card-title">总流量</h5>
                                            <h3 id="total-bytes">0 KB</h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">流量趋势</h5>
                    <div>
                        <div class="form-check form-switch d-inline-block me-2">
                            <input class="form-check-input" type="checkbox" id="show-anomalies" checked>
                            <label class="form-check-label" for="show-anomalies">显示异常事件</label>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="traffic-chart"></canvas>
                    <div id="anomaly-tooltip" class="position-absolute bg-dark text-white p-2 rounded" style="display: none; z-index: 1000;"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">协议分布</h5>
                    <div>
                        <div class="form-check form-switch d-inline-block me-2">
                            <input class="form-check-input" type="checkbox" id="auto-update-chart" checked>
                            <label class="form-check-label" for="auto-update-chart">自动更新</label>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <canvas id="protocol-chart" style="height: 400px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">数据包详情</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="packet-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>时间</th>
                                    <th>源地址</th>
                                    <th>目标地址</th>
                                    <th>源端口</th>
                                    <th>目标端口</th>
                                    <th>协议</th>
                                    <th>长度</th>
                                    <th>详情</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 数据包信息将在这里动态添加 -->
                            </tbody>
                        </table>
                    </div>
                    <div id="no-packets-message" class="alert alert-info text-center">
                        开始捕获数据包后，详细信息将在这里显示
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 保存PCAP文件模态框 -->
<div class="modal fade" id="save-pcap-modal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">保存捕获流量</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="save-pcap-alert" class="alert alert-danger" style="display: none;"></div>
                <form id="save-pcap-form">
                    <div class="mb-3">
                        <label for="pcap-filename" class="form-label">文件名</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="pcap-filename">
                            <span class="input-group-text">.pcap</span>
                        </div>
                        <div class="form-text">文件将自动添加.pcap扩展名（如果没有）</div>
                    </div>
                    <div class="mb-3">
                        <label for="save-location" class="form-label">保存位置</label>
                        <select class="form-select" id="save-location">
                            <option value="default">默认位置 (app/uploads)</option>
                            <option value="downloads">下载文件夹</option>
                            <option value="custom">自定义位置</option>
                        </select>
                    </div>
                    <div class="mb-3" id="custom-path-group" style="display: none;">
                        <label for="custom-path" class="form-label">自定义路径</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="custom-path" placeholder="C:\Users\<USER>\Documents">
                            <button class="btn btn-outline-secondary" type="button" id="browse-folder-btn">
                                <i class="fas fa-folder-open"></i> 浏览
                            </button>
                        </div>
                        <div class="form-text">输入完整路径，例如：C:\Users\<USER>\Desktop</div>
                    </div>
                </form>
                <div id="save-progress" class="progress mt-3" style="display: none;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="save-pcap-confirm">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 保存成功模态框 -->
<div class="modal fade" id="save-success-modal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">保存成功</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i> 捕获流量已成功保存为PCAP文件
                </div>
                <div class="mb-3">
                    <label class="form-label">文件路径</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="saved-file-path" readonly>
                        <button class="btn btn-outline-secondary" type="button" id="copy-path-btn">
                            <i class="fas fa-copy"></i> 复制
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="open-folder-btn">
                    <i class="fas fa-folder-open me-1"></i> 打开文件夹
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://d3js.org/d3.v5.min.js"></script>
<script src="https://unpkg.com/d3-sankey@0.12.3/dist/d3-sankey.min.js"></script>
<script src="https://unpkg.com/d3-array@2"></script>
<script src="https://unpkg.com/d3-collection@1"></script>
<script src="https://unpkg.com/d3-path@1"></script>
<script src="https://unpkg.com/d3-shape@1"></script>
<script src="https://unpkg.com/d3-color@1"></script>
<script>
$(document).ready(function() {
    let isCapturing = false;
    let packetCount = 0;
    let lastPacketIndex = 0;
    let trafficChart;
    let trafficData = {
        labels: [],
        datasets: [
            {
                label: '流量 (bytes/s)',
                data: [],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                fill: true,
                tension: 0.4
            },
            {
                label: '异常事件',
                data: [],
                backgroundColor: 'red',
                borderColor: 'red',
                pointRadius: 6,
                pointHoverRadius: 8,
                pointStyle: 'circle',
                showLine: false
            }
        ]
    };

    // 存储异常事件详情
    let anomalyEvents = [];

    // 初始化图表
    function initChart() {
        const ctx = document.getElementById('traffic-chart').getContext('2d');
        trafficChart = new Chart(ctx, {
            type: 'line',
            data: trafficData,
            options: {
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '时间'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '流量 (bytes/s)'
                        }
                    }
                },
                animation: {
                    duration: 500
                },
                elements: {
                    line: {
                        tension: 0.4
                    }
                },
                interaction: {
                    mode: 'nearest',
                    intersect: false
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                if (context.datasetIndex === 1) { // 异常事件数据集
                                    const eventIndex = context.dataIndex;
                                    const event = anomalyEvents[eventIndex];
                                    if (event) {
                                        return [`事件类型: ${event.type}`, `影响范围: ${event.impact}`];
                                    }
                                    return '异常事件';
                                }
                                return `流量: ${context.parsed.y} bytes/s`;
                            }
                        }
                    }
                },
                onClick: function(event, elements) {
                    if (elements.length > 0 && elements[0].datasetIndex === 1) {
                        const index = elements[0].index;
                        const eventDetails = anomalyEvents[index];
                        if (eventDetails) {
                            alert(`异常事件详情:\n时间: ${eventDetails.time}\n类型: ${eventDetails.type}\n影响范围: ${eventDetails.impact}`);
                        }
                    }
                }
            }
        });
    }

    // 更新图表
    function updateChart(timestamp, bytesPerSecond, anomalyEvent = null) {
        // 保持最近10分钟的数据（假设每秒更新一次，则保留600个数据点）
        const MAX_DATA_POINTS = 600;

        if (trafficData.labels.length > MAX_DATA_POINTS) {
            trafficData.labels.shift();
            trafficData.datasets[0].data.shift();

            // 同时移除异常事件数据集中的第一个元素
            if (trafficData.datasets[1].data.length > 0) {
                trafficData.datasets[1].data.shift();
                anomalyEvents.shift();
            }
        }

        trafficData.labels.push(timestamp);

        // 检测异常流量峰值（这里简单设置一个阈值，实际应用中可能需要更复杂的算法）
        const isAbnormal = bytesPerSecond > 30000; // 假设30KB/s以上为异常

        // 如果是异常流量，使用红色高亮
        if (isAbnormal) {
            // 添加一个红色的点标记异常
            trafficData.datasets[1].data.push(bytesPerSecond);

            // 存储异常事件详情
            const event = anomalyEvent || {
                time: timestamp,
                type: '流量峰值',
                impact: '网络拥塞'
            };
            anomalyEvents.push(event);
        } else {
            // 为保持数据点索引一致，在非异常点位置添加null
            trafficData.datasets[1].data.push(null);
            anomalyEvents.push(null);
        }

        // 更新流量数据
        trafficData.datasets[0].data.push(bytesPerSecond);

        // 更新图表
        trafficChart.update();
    }

    // 协议分布图表对象
    let protocolChart;
    let protocolData = {
        labels: [],
        datasets: [{
            label: '协议流量 (bytes/s)',
            data: [],
            backgroundColor: [],
            borderColor: [],
            borderWidth: 1
        }]
    };

    // 协议颜色映射
    const protocolColors = {
        'HTTP': 'rgba(76, 175, 80, 0.7)',
        'HTTPS': 'rgba(33, 150, 243, 0.7)',
        'DNS': 'rgba(255, 193, 7, 0.7)',
        'TCP': 'rgba(156, 39, 176, 0.7)',
        'UDP': 'rgba(255, 87, 34, 0.7)',
        'SSH': 'rgba(96, 125, 139, 0.7)',
        'FTP': 'rgba(121, 85, 72, 0.7)',
        'SMTP': 'rgba(233, 30, 99, 0.7)',
        'POP3': 'rgba(158, 158, 158, 0.7)',
        'IMAP': 'rgba(205, 220, 57, 0.7)',
        'HTTP-ALT': 'rgba(255, 152, 0, 0.7)',
        'HTTPS-ALT': 'rgba(3, 169, 244, 0.7)',
        'Other': 'rgba(189, 189, 189, 0.7)'
    };

    // 初始化协议分布图
    function initProtocolChart() {
        const ctx = document.getElementById('protocol-chart').getContext('2d');
        protocolChart = new Chart(ctx, {
            type: 'bar',
            data: protocolData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '流量 (bytes/s)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '协议'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `流量: ${context.raw} bytes/s`;
                            }
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeOutQuart'
                    }
                }
            }
        });
    }

    // 更新协议分布图
    function updateProtocolChart(data) {
        try {
            console.log('收到协议流量数据:', data);

            // 检查是否启用自动更新
            if (!$('#auto-update-chart').is(':checked')) {
                return;
            }

            // 如果没有数据，使用示例数据
            if (!data || !data.protocol_stats) {
                const demoData = {
                    'TCP': 1500,
                    'UDP': 800,
                    'HTTP': 1200,
                    'HTTPS': 2000,
                    'DNS': 500
                };

                updateChartWithData(demoData);
                return;
            }

            // 使用实际数据更新图表
            updateChartWithData(data.protocol_stats);

        } catch (error) {
            console.error('更新协议分布图错误:', error);
        }
    }

    // 使用数据更新图表
    function updateChartWithData(protocolStats) {
        // 将对象转换为数组并按值排序
        const sortedData = Object.entries(protocolStats)
            .sort((a, b) => b[1] - a[1]); // 按值降序排序

        // 提取标签和数据
        const labels = sortedData.map(item => item[0]);
        const values = sortedData.map(item => item[1]);

        // 生成颜色数组
        const backgroundColors = labels.map(label =>
            protocolColors[label] || protocolColors['Other']
        );

        const borderColors = backgroundColors.map(color =>
            color.replace('0.7', '1') // 将透明度改为1以获得边框颜色
        );

        // 更新图表数据
        protocolChart.data.labels = labels;
        protocolChart.data.datasets[0].data = values;
        protocolChart.data.datasets[0].backgroundColor = backgroundColors;
        protocolChart.data.datasets[0].borderColor = borderColors;

        // 更新图表
        protocolChart.update();
    }

    // 获取网络接口列表
    function getNetworkInterfaces() {
        $.get('/api/network-interfaces', function(response) {
            if (response.success) {
                const select = $('#interface-select');
                select.empty();
                select.append('<option value="">选择网络接口...</option>');

                response.interfaces.forEach(iface => {
                    select.append(`<option value="${iface.name}">${iface.description}</option>`);
                });
            } else {
                alert('获取网络接口失败: ' + response.error);
            }
        });
    }

    // 获取捕获统计信息
    function getCaptureStats() {
        if (isCapturing) {
            $.get('/api/capture-stats', function(response) {
                if (response.success) {
                    // 更新统计数据
                    $('#total-packets').text(response.total_packets);

                    // 格式化字节大小
                    let bytesText;
                    if (response.total_bytes < 1024) {
                        bytesText = response.total_bytes + ' B';
                    } else if (response.total_bytes < 1024 * 1024) {
                        bytesText = (response.total_bytes / 1024).toFixed(2) + ' KB';
                    } else {
                        bytesText = (response.total_bytes / (1024 * 1024)).toFixed(2) + ' MB';
                    }
                    $('#total-bytes').text(bytesText);

                                    // 更新图表
                    const now = new Date().toLocaleTimeString();

                    // 检查是否有异常事件
                    let anomalyEvent = null;
                    if (response.anomaly_events && response.anomaly_events.length > 0) {
                        anomalyEvent = response.anomaly_events[0];
                    }

                    updateChart(now, response.bytes_per_second || 0, anomalyEvent);

                    // 更新协议分布图
                    updateProtocolChart(response);

                    // 如果有数据包，启用保存按钮
                    if (response.total_packets > 0) {
                        $('#save-capture-btn').prop('disabled', false);
                    }

                    // 递归调用，每秒更新一次
                    setTimeout(getCaptureStats, 1000);
                }
            });
        }
    }

    // 根据协议类型返回行样式类
    function getProtocolRowClass(protocol) {
        if (protocol.includes('TCP')) {
            return 'table-primary';
        } else if (protocol.includes('UDP')) {
            return 'table-success';
        } else if (protocol.includes('ICMP')) {
            return 'table-warning';
        } else if (protocol.includes('ARP')) {
            return 'table-info';
        } else if (protocol.includes('IPv6')) {
            return 'table-secondary';
        } else {
            return '';
        }
    }

    // 根据协议类型返回徽章颜色
    function getProtocolBadgeColor(protocol) {
        if (protocol.includes('HTTP')) {
            return 'success';
        } else if (protocol.includes('HTTPS')) {
            return 'primary';
        } else if (protocol.includes('DNS')) {
            return 'info';
        } else if (protocol.includes('TCP')) {
            return 'primary';
        } else if (protocol.includes('UDP')) {
            return 'success';
        } else if (protocol.includes('ICMP')) {
            return 'warning';
        } else if (protocol.includes('ARP')) {
            return 'info';
        } else if (protocol.includes('IPv6')) {
            return 'secondary';
        } else {
            return 'dark';
        }
    }

    // 获取最新数据包
    function getLatestPackets() {
        if (isCapturing) {
            $.get('/api/latest-packets', { since: lastPacketIndex }, function(response) {
                if (response.success) {
                    // 隐藏提示信息
                    $('#no-packets-message').hide();

                    // 更新数据包表格
                    const tbody = $('#packet-table tbody');

                    response.packets.forEach(packet => {
                        // 添加到表格
                        // 添加数据包行，并根据协议类型设置不同的背景色
                        const rowClass = getProtocolRowClass(packet.protocol);
                        tbody.append(`
                            <tr class="${rowClass}">
                                <td>${packet.id}</td>
                                <td>${packet.time}</td>
                                <td>${packet.src_ip}</td>
                                <td>${packet.dst_ip}</td>
                                <td>${packet.src_port}</td>
                                <td>${packet.dst_port}</td>
                                <td><span class="badge bg-${getProtocolBadgeColor(packet.protocol)}">${packet.protocol}</span></td>
                                <td>${packet.length}</td>
                                <td><small>${packet.details || ''}</small></td>
                            </tr>
                        `);
                    });

                    // 如果表格行数超过100，删除旧的行
                    if (tbody.children().length > 100) {
                        tbody.children().slice(0, tbody.children().length - 100).remove();
                    }

                    // 更新下一次请求的起始索引
                    lastPacketIndex = response.next_index;

                    // 递归调用，每秒更新一次
                    setTimeout(getLatestPackets, 1000);
                }
            });
        }
    }

    // 开始捕获
    $('#start-capture-btn').click(function() {
        const interface = $('#interface-select').val();
        if (!interface) {
            alert('请选择网络接口');
            return;
        }

        $.ajax({
            url: '/api/start-capture',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ interface: interface }),
            success: function(response) {
                if (response.success) {
                    isCapturing = true;

                    // 更新UI状态
                    $('#start-capture-btn').prop('disabled', true);
                    $('#stop-capture-btn').prop('disabled', false);
                    $('#status-badge').text('捕获中').removeClass('bg-primary').addClass('bg-danger');

                    // 重置统计数据
                    lastPacketIndex = 0;
                    $('#packet-table tbody').empty();

                    // 开始定时获取统计信息
                    getCaptureStats();
                    getLatestPackets();
                } else {
                    alert('启动捕获失败: ' + response.error);
                }
            },
            error: function(xhr) {
                alert('启动捕获失败: ' + (xhr.responseJSON ? xhr.responseJSON.error : '未知错误'));
            }
        });
    });

    // 停止捕获
    $('#stop-capture-btn').click(function() {
        $.ajax({
            url: '/api/stop-capture',
            type: 'POST',
            success: function(response) {
                if (response.success) {
                    isCapturing = false;

                    // 更新UI状态
                    $('#start-capture-btn').prop('disabled', false);
                    $('#stop-capture-btn').prop('disabled', true);
                    $('#status-badge').text('已停止').removeClass('bg-danger').addClass('bg-primary');
                } else {
                    alert('停止捕获失败: ' + response.error);
                }
            },
            error: function(xhr) {
                alert('停止捕获失败: ' + (xhr.responseJSON ? xhr.responseJSON.error : '未知错误'));
            }
        });
    });

    // 保存位置切换
    $('#save-location').on('change', function() {
        if ($(this).val() === 'custom') {
            $('#custom-path-group').show();
        } else {
            $('#custom-path-group').hide();
        }
    });

    // 浏览文件夹按钮点击
    $('#browse-folder-btn').click(function() {
        // 注意：由于浏览器安全限制，我们无法直接访问文件系统
        // 这里我们只能提供一些常用路径供用户选择
        const commonPaths = [
            { name: '桌面', path: 'C:\\Users\\<USER>\\Desktop' },
            { name: '文档', path: 'C:\\Users\\<USER>\\Documents' },
            { name: '下载', path: 'C:\\Users\\<USER>\\Downloads' },
            { name: 'C:\\', path: 'C:\\' },
            { name: 'D:\\', path: 'D:\\' }
        ];

        // 在实际应用中，这里可以实现一个简单的文件夹选择对话框
        // 这里我们只是提供一个示例，将常用路径填入到输入框中
        const currentPath = $('#custom-path').val() || '';
        if (currentPath) {
            // 如果已有路径，尝试获取父目录
            const parentPath = currentPath.split('\\').slice(0, -1).join('\\');
            if (parentPath) {
                $('#custom-path').val(parentPath);
                return;
            }
        }

        // 如果没有已有路径或无法获取父目录，使用默认路径
        $('#custom-path').val('C:\\Users\\<USER>\\') !== -1) {
            folderPath = filePath.substring(0, filePath.lastIndexOf('\\'));
        }
        // 处理Unix路径
        else if (filePath.indexOf('/') !== -1) {
            folderPath = filePath.substring(0, filePath.lastIndexOf('/'));
        }

        // 在实际应用中，这里可以使用Electron的shell.openPath或其他方式打开文件夹
        // 在浏览器中我们无法直接打开本地文件夹，所以这里只是显示一个提示
        alert('请手动打开此文件夹: ' + folderPath);
    });

    // 确认保存按钮点击
    $('#save-pcap-confirm').click(function() {
        // 验证表单
        const filename = $('#pcap-filename').val();
        if (!filename) {
            $('#save-pcap-alert').text('请输入文件名').show();
            return;
        }

        const saveLocation = $('#save-location').val();
        if (saveLocation === 'custom') {
            const customPath = $('#custom-path').val();
            if (!customPath) {
                $('#save-pcap-alert').text('请输入自定义路径').show();
                return;
            }
        }

        // 显示进度条
        $('#save-progress').show();
        $('#save-pcap-alert').hide();
        $('#save-pcap-confirm').prop('disabled', true);

        // 获取自定义路径
        const customPath = $('#custom-path').val();

        $.ajax({
            url: '/api/save-capture',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                filename: filename,
                location: saveLocation,
                custom_path: customPath
            }),
            success: function(response) {
                // 重置按钮状态
                $('#save-pcap-confirm').prop('disabled', false);
                $('#save-progress').hide();

                if (response.success) {
                    // 关闭保存模态框
                    bootstrap.Modal.getInstance(document.getElementById('save-pcap-modal')).hide();

                    // 设置成功模态框内容
                    $('#saved-file-path').val(response.file_path);

                    // 显示成功模态框
                    const successModal = new bootstrap.Modal(document.getElementById('save-success-modal'));
                    successModal.show();
                } else {
                    $('#save-pcap-alert').text('保存失败: ' + response.error).show();
                }
            },
            error: function(xhr) {
                // 重置按钮状态
                $('#save-pcap-confirm').prop('disabled', false);
                $('#save-progress').hide();

                const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : '未知错误';
                $('#save-pcap-alert').text('保存失败: ' + errorMsg).show();
            }
        });
    });

    // 初始化图表
    initChart();
    initProtocolChart();

    // 获取网络接口列表
    getNetworkInterfaces();

    // 切换显示异常事件
    $('#show-anomalies').change(function() {
        const showAnomalies = $(this).is(':checked');
        trafficChart.data.datasets[1].hidden = !showAnomalies;
        trafficChart.update();
    });

    // 切换协议图表自动更新
    $('#auto-update-chart').change(function() {
        if ($(this).is(':checked')) {
            // 如果开启自动更新，立即获取最新数据
            $.get('/api/capture-stats', function(response) {
                if (response.success) {
                    updateProtocolChart(response);
                }
            });
        }
    });

    // 自动调整图表大小
    window.addEventListener('resize', function() {
        if (trafficChart) {
            trafficChart.resize();
        }
    });
});
</script>
{% endblock %}