{% extends "base.html" %}

{% block page_title %}异常流量检测{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active" aria-current="page">异常检测</li>
{% endblock %}

{% block content %}
<div class="container-fluid">

    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">PCAP文件分析</h5>
                </div>
                <div class="card-body">
                    <form id="pcap-upload-form" class="mb-4">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <input type="file" class="form-control" id="pcap-file" accept=".pcap,.pcapng" multiple required>
                            </div>
                            <div class="col-auto">
                                <select class="form-select" id="model-type">
                                    <option value="simple">基础LSTM模型</option>
                                    <option value="cnnlstm">标准CNN-LSTM模型</option>
                                    <option value="advanced" selected>ResCNN-ABLGAN模型</option>
                                </select>
                                <small class="form-text text-muted">
                                    简单模型速度快，复杂模型精度高
                                </small>
                            </div>
                            <div class="col-auto">
                                <button type="button" id="upload-analyze-btn" class="btn btn-primary">
                                    <i class="fas fa-upload me-2"></i>上传并分析
                                </button>
                            </div>
                        </div>
                    </form>

                    <div id="analysis-progress" class="my-4" style="display: none;">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="card-title mb-0">文件分析进度</h6>
                            </div>
                            <div class="card-body">
                                <div id="file-list" class="mb-3">
                                    <!-- 文件列表将在这里动态生成 -->
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <div class="progress flex-grow-1" style="height: 20px;">
                                        <div id="total-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated"
                                             role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                            0%
                                        </div>
                                    </div>
                                    <span id="progress-percentage" class="ms-2">0%</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <small id="progress-status">准备分析...</small>
                                    <small id="estimated-time">预计剩余时间: 计算中...</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 me-3">分析结果</h5>
                        <div id="results-file-selector" class="d-none me-3">
                            <select id="file-result-selector" class="form-select form-select-sm" style="min-width: 200px;">
                                <option value="combined">合并所有结果</option>
                                <!-- 文件选项将在这里动态生成 -->
                            </select>
                        </div>
                        <small id="model-info-display" class="text-muted"></small>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="show-only-anomalies">
                            <label class="form-check-label" for="show-only-anomalies">只显示异常数据包</label>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info" id="no-data-message">
                        请上传PCAP文件进行分析
                    </div>
                    <div id="results-container" style="display: none;">
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">总数据包数</h5>
                                        <h3 id="total-packets">0</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">异常数据包</h5>
                                        <h3 id="anomaly-packets">0</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">正常数据包</h5>
                                        <h3 id="normal-packets">0</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">异常比例</h5>
                                        <h3 id="anomaly-ratio">0%</h3>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 异常趋势图表 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="card-title mb-0">异常比率时间趋势</h5>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="highlight-threshold" checked>
                                            <label class="form-check-label" for="highlight-threshold">高亮显示异常阈值</label>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div id="anomaly-trend-chart" style="height: 300px;"></div>
                                        <div class="text-center text-muted" id="no-trend-data" style="display: none;">
                                            没有足够的时间序列数据
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 异常类型分布图表 -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="card-title mb-0">异常类型分布</h5>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-secondary active" id="chart-type-pie">饼图</button>
                                            <button type="button" class="btn btn-outline-secondary" id="chart-type-bar">柱状图</button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div id="anomaly-type-chart" style="height: 300px;"></div>
                                        <div class="text-center text-muted" id="no-type-data" style="display: none;">
                                            没有检测到异常类型数据
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">异常类型详情</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-info" id="type-detail-info">
                                            点击左侧图表中的异常类型可查看详细信息并筛选表格
                                        </div>
                                        <div id="type-detail-content" style="display: none;">
                                            <h4 id="selected-type-name">-</h4>
                                            <div class="row mb-3">
                                                <div class="col-6">
                                                    <div class="card bg-danger text-white">
                                                        <div class="card-body py-2">
                                                            <h6 class="card-title mb-0">数量</h6>
                                                            <h4 id="selected-type-count" class="mb-0">0</h4>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="card bg-info text-white">
                                                        <div class="card-body py-2">
                                                            <h6 class="card-title mb-0">占比</h6>
                                                            <h4 id="selected-type-percentage" class="mb-0">0%</h4>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <button class="btn btn-sm btn-primary" id="filter-by-type">筛选此类型异常</button>
                                            <button class="btn btn-sm btn-outline-secondary" id="reset-type-filter" style="display: none;">重置筛选</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 网络流量热图 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="card-title mb-0">网络流量热图</h5>
                                        <div class="d-flex align-items-center">
                                            <div class="form-check form-switch me-3">
                                                <input class="form-check-input" type="checkbox" id="show-only-anomaly-flows" checked>
                                                <label class="form-check-label" for="show-only-anomaly-flows">只显示异常流量</label>
                                            </div>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="heatmap-options" data-bs-toggle="dropdown" aria-expanded="false">
                                                    选项
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="heatmap-options">
                                                    <li>
                                                        <div class="dropdown-item">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" id="enable-heatmap-zoom" checked>
                                                                <label class="form-check-label" for="enable-heatmap-zoom">启用缩放</label>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="dropdown-item">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" id="group-similar-ips" checked>
                                                                <label class="form-check-label" for="group-similar-ips">分组相似 IP</label>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <div class="dropdown-item">
                                                            <label for="severity-threshold" class="form-label">严重度阈值: <span id="severity-threshold-value">50%</span></label>
                                                            <input type="range" class="form-range" min="0" max="100" step="5" value="50" id="severity-threshold">
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div id="traffic-heatmap" style="height: 400px;"></div>
                                        <div class="text-center text-muted" id="no-heatmap-data" style="display: none;">
                                            没有足够的流量数据用于生成热图
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <button class="btn btn-sm btn-outline-secondary" id="view-model-metrics">
                                    <i class="fas fa-chart-line me-1"></i>查看模型指标
                                </button>
                            </div>
                        </div>
                        <table class="table table-hover" id="packet-table">
                            <thead>
                                <tr>
                                    <th>编号</th>
                                    <th>时间</th>
                                    <th>源地址</th>
                                    <th>目标地址</th>
                                    <th>协议</th>
                                    <th>长度</th>
                                    <th>状态</th>
                                    <th>攻击类型</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据包详情模态框 -->
<div class="modal fade" id="packet-detail-modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">数据包详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="accordion" id="packet-detail-accordion">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模型指标模态框 -->
<div class="modal fade" id="model-metrics-modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">模型性能指标</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="metrics-loading" class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p>加载模型指标...</p>
                </div>
                <div id="metrics-content" style="display: none;">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">准确率</h5>
                                    <h3 id="metric-accuracy">-</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">延迟 (ms)</h5>
                                    <h3 id="metric-latency">-</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">总预测次数</h5>
                                    <h3 id="metric-total-predictions">-</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">性能下降计数</h5>
                                    <h3 id="metric-decline-counter">-</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">准确率历史</h5>
                                    <div id="accuracy-history-chart" style="height: 250px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="metrics-error" class="alert alert-danger" style="display: none;">
                    无法加载模型指标。请确保模型已初始化并且有足够的数据。
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 存储原始数据包列表
    let allPackets = [];
    // 当前选中的异常类型
    let selectedAnomalyType = null;
    // 当前图表类型（饼图或柱状图）
    let currentChartType = 'pie';
    // 异常类型统计数据
    let anomalyTypeStats = {};
    // 存储异常类型的颜色映射
    let typeColorMap = {};
    // 当前热图的严重度阈值
    let severityThreshold = 50;
    // 存储热图数据
    let heatmapData = null;
    // 存储热图实例
    let heatmapInstance = null;

    // 监听高亮阈值切换
    $('#highlight-threshold').change(function() {
        // 如果有数据，重新绘制图表
        if (allPackets.length > 0) {
            const data = { packets: allPackets, time_series: window.lastTimeSeriesData || [] };
            updateAnomalyTrendChart(data.time_series);
        }
    });

    // 监听图表类型切换
    $('#chart-type-pie, #chart-type-bar').click(function() {
        // 更新按钮状态
        $('#chart-type-pie, #chart-type-bar').removeClass('active');
        $(this).addClass('active');

        // 设置图表类型
        currentChartType = this.id === 'chart-type-pie' ? 'pie' : 'bar';

        // 重新绘制图表
        if (Object.keys(anomalyTypeStats).length > 0) {
            drawAnomalyTypeChart(anomalyTypeStats, currentChartType);
        }
    });

    // 监听筛选按钮
    $('#filter-by-type').click(function() {
        if (selectedAnomalyType) {
            // 显示重置按钮
            $('#reset-type-filter').show();

            // 检查是否只显示异常数据包
            const showOnlyAnomalies = $('#show-only-anomalies').is(':checked');
            const packetsToShow = showOnlyAnomalies ? allPackets.filter(p => p.is_anomaly) : allPackets;

            // 更新表格，应用类型筛选
            updatePacketTable(packetsToShow, selectedAnomalyType);
        }
    });

    // 监听重置筛选按钮
    $('#reset-type-filter').click(function() {
        // 隐藏重置按钮
        $(this).hide();

        // 检查是否只显示异常数据包
        const showOnlyAnomalies = $('#show-only-anomalies').is(':checked');
        const packetsToShow = showOnlyAnomalies ? allPackets.filter(p => p.is_anomaly) : allPackets;

        // 更新表格，不应用类型筛选
        updatePacketTable(packetsToShow);
    });

    // 监听热图只显示异常流量切换
    $('#show-only-anomaly-flows').change(function() {
        if (allPackets.length > 0) {
            updateTrafficHeatmap(allPackets);
        }
    });

    // 监听热图缩放切换
    $('#enable-heatmap-zoom').change(function() {
        if (heatmapInstance && heatmapData) {
            updateTrafficHeatmap(allPackets, true);
        }
    });

    // 监听分组相似 IP 切换
    $('#group-similar-ips').change(function() {
        if (allPackets.length > 0) {
            updateTrafficHeatmap(allPackets);
        }
    });

    // 监听严重度阈值滑块变化
    $('#severity-threshold').on('input', function() {
        severityThreshold = parseInt($(this).val());
        $('#severity-threshold-value').text(severityThreshold + '%');

        if (allPackets.length > 0) {
            updateTrafficHeatmap(allPackets, true);
        }
    });

    // 切换显示模式
    $('#show-only-anomalies').change(function() {
        const showOnlyAnomalies = $(this).is(':checked');
        console.log('切换显示模式:', showOnlyAnomalies ? '只显示异常' : '显示全部');

        // 过滤数据包
        const packetsToShow = showOnlyAnomalies ? allPackets.filter(p => p.is_anomaly) : allPackets;
        console.log('过滤后数据包数量:', packetsToShow.length);

        // 更新表格，保持当前类型筛选
        updatePacketTable(packetsToShow, $('#reset-type-filter').is(':visible') ? selectedAnomalyType : null);
    });

    // 更新异常类型图表
    function updateAnomalyTypeChart(anomalyStats, packets) {
        const chartContainer = $('#anomaly-type-chart');
        const noDataMessage = $('#no-type-data');

        // 如果没有异常类型数据，显示提示信息
        if (!anomalyStats || Object.keys(anomalyStats).length === 0) {
            chartContainer.hide();
            noDataMessage.show();
            return;
        }

        chartContainer.show();
        noDataMessage.hide();

        // 存储异常类型统计数据供后续使用
        anomalyTypeStats = anomalyStats;

        // 绘制图表
        drawAnomalyTypeChart(anomalyStats, currentChartType);
    }

    // 绘制异常类型图表
    function drawAnomalyTypeChart(anomalyStats, chartType) {
        // 准备图表数据
        const types = Object.keys(anomalyStats);
        const counts = Object.values(anomalyStats);
        const totalAnomalies = counts.reduce((sum, count) => sum + count, 0);

        // 生成颜色映射，确保每种类型有固定的颜色
        if (Object.keys(typeColorMap).length === 0) {
            // 预定义的颜色数组
            const colors = [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40',
                '#8AC249', '#EA526F', '#00A8E8', '#FF5733', '#C70039', '#900C3F'
            ];

            // 为每种类型分配颜色
            types.forEach((type, index) => {
                typeColorMap[type] = colors[index % colors.length];
            });
        }

        // 准备图表数据
        let chartData;
        let layout;

        if (chartType === 'pie') {
            // 饼图数据
            chartData = [{
                type: 'pie',
                labels: types,
                values: counts,
                textinfo: 'label+percent',
                hoverinfo: 'label+value+percent',
                marker: {
                    colors: types.map(type => typeColorMap[type])
                },
                hole: 0.4
            }];

            layout = {
                margin: { t: 10, r: 10, l: 10, b: 10 },
                showlegend: false,
                annotations: [{
                    font: { size: 14 },
                    showarrow: false,
                    text: `总计: ${totalAnomalies}`,
                    x: 0.5,
                    y: 0.5
                }]
            };
        } else {
            // 柱状图数据
            chartData = [{
                type: 'bar',
                x: types,
                y: counts,
                marker: {
                    color: types.map(type => typeColorMap[type])
                },
                text: counts.map(count => `${count} (${((count / totalAnomalies) * 100).toFixed(1)}%)`),
                textposition: 'auto',
                hoverinfo: 'x+y+text'
            }];

            layout = {
                margin: { t: 10, r: 10, l: 50, b: 100 },
                xaxis: {
                    tickangle: -45
                },
                yaxis: {
                    title: '数量'
                }
            };
        }

        // 绘制图表
        Plotly.newPlot('anomaly-type-chart', chartData, layout, {
            responsive: true,
            displayModeBar: false
        });

        // 添加点击事件
        document.getElementById('anomaly-type-chart').on('plotly_click', function(data) {
            // 获取点击的类型
            const clickedType = chartType === 'pie'
                ? data.points[0].label
                : data.points[0].x;

            // 更新选中类型
            selectedAnomalyType = clickedType;

            // 更新类型详情显示
            updateTypeDetails(clickedType, anomalyStats[clickedType], totalAnomalies);
        });
    }

    // 更新类型详情显示
    function updateTypeDetails(type, count, total) {
        // 隐藏提示信息，显示详情内容
        $('#type-detail-info').hide();
        $('#type-detail-content').show();

        // 更新类型名称和统计信息
        $('#selected-type-name').text(type);
        $('#selected-type-count').text(count);
        $('#selected-type-percentage').text(`${((count / total) * 100).toFixed(1)}%`);

        // 如果已经应用了筛选，显示重置按钮
        if ($('#reset-type-filter').is(':visible')) {
            $('#filter-by-type').hide();
        } else {
            $('#filter-by-type').show();
        }
    }

    // 更新网络流量热图
    function updateTrafficHeatmap(packets, reuseData = false) {
        const chartContainer = $('#traffic-heatmap');
        const noDataMessage = $('#no-heatmap-data');

        // 检查是否有足够的数据
        if (!packets || packets.length < 5) {
            chartContainer.hide();
            noDataMessage.show();
            return;
        }

        chartContainer.show();
        noDataMessage.hide();

        // 如果不重用数据，则重新处理数据
        if (!reuseData || !heatmapData) {
            // 检查是否只显示异常流量
            const showOnlyAnomalyFlows = $('#show-only-anomaly-flows').is(':checked');
            const packetsToProcess = showOnlyAnomalyFlows ? packets.filter(p => p.is_anomaly) : packets;

            // 如果没有数据包可处理，显示提示信息
            if (packetsToProcess.length === 0) {
                chartContainer.hide();
                noDataMessage.show();
                return;
            }

            // 检查是否分组相似 IP
            const groupSimilarIPs = $('#group-similar-ips').is(':checked');

            // 处理数据包，生成热图数据
            heatmapData = processPacketsForHeatmap(packetsToProcess, groupSimilarIPs);
        }

        // 绘制热图
        drawTrafficHeatmap(heatmapData);
    }

    // 处理数据包以生成热图数据
    function processPacketsForHeatmap(packets, groupSimilarIPs) {
        // 提取所有唯一的源IP和目标IP
        let srcIPs = new Set();
        let dstIPs = new Set();

        // 收集所有IP
        packets.forEach(packet => {
            if (packet.src) srcIPs.add(packet.src);
            if (packet.dst) dstIPs.add(packet.dst);
        });

        // 如果需要分组相似 IP，则将相同子网的IP分组
        if (groupSimilarIPs) {
            srcIPs = groupIPsBySubnet(Array.from(srcIPs));
            dstIPs = groupIPsBySubnet(Array.from(dstIPs));
        } else {
            srcIPs = Array.from(srcIPs);
            dstIPs = Array.from(dstIPs);
        }

        // 创建热图数据矩阵
        const matrix = Array(srcIPs.length).fill().map(() => Array(dstIPs.length).fill(0));
        const anomalyMatrix = Array(srcIPs.length).fill().map(() => Array(dstIPs.length).fill(0));

        // 填充矩阵数据
        packets.forEach(packet => {
            if (!packet.src || !packet.dst) return;

            // 找到源IP和目标IP的索引
            let srcIndex = findIPIndex(packet.src, srcIPs, groupSimilarIPs);
            let dstIndex = findIPIndex(packet.dst, dstIPs, groupSimilarIPs);

            if (srcIndex !== -1 && dstIndex !== -1) {
                // 增加流量计数
                matrix[srcIndex][dstIndex] += 1;

                // 如果是异常数据包，增加异常计数
                if (packet.is_anomaly) {
                    anomalyMatrix[srcIndex][dstIndex] += 1;
                }
            }
        });

        // 计算异常比率矩阵
        const ratioMatrix = matrix.map((row, i) =>
            row.map((val, j) => val > 0 ? (anomalyMatrix[i][j] / val) * 100 : 0)
        );

        return {
            srcIPs,
            dstIPs,
            flowMatrix: matrix,
            anomalyMatrix,
            ratioMatrix
        };
    }

    // 将IP按子网分组
    function groupIPsBySubnet(ips) {
        // 简化实现：只按第一个段分组
        const groups = {};

        ips.forEach(ip => {
            const parts = ip.split('.');
            if (parts.length === 4) {
                const subnet = parts[0] + '.*';
                if (!groups[subnet]) {
                    groups[subnet] = [];
                }
                groups[subnet].push(ip);
            } else {
                // 非IPv4地址单独分组
                if (!groups[ip]) {
                    groups[ip] = [ip];
                }
            }
        });

        // 返回分组后的数组
        return Object.keys(groups).map(key => {
            if (groups[key].length > 1) {
                return key; // 如果有多个IP，返回子网表示
            } else {
                return groups[key][0]; // 如果只有一个IP，返回原始IP
            }
        });
    }

    // 在IP数组中查找IP的索引
    function findIPIndex(ip, ipArray, isGrouped) {
        if (isGrouped) {
            // 如果是分组模式，需要检查子网
            const parts = ip.split('.');
            if (parts.length === 4) {
                const subnet = parts[0] + '.*';
                return ipArray.findIndex(item => item === subnet || item === ip);
            }
        }

        // 直接查找完全匹配
        return ipArray.indexOf(ip);
    }

    // 绘制网络流量热图
    function drawTrafficHeatmap(data) {
        // 检查数据是否有效
        if (!data || !data.srcIPs || !data.dstIPs || !data.ratioMatrix) {
            return;
        }

        // 准备热图数据
        const { srcIPs, dstIPs, flowMatrix, ratioMatrix } = data;

        // 创建热图数据点
        const heatmapValues = [];

        // 应用严重度阈值过滤
        ratioMatrix.forEach((row, i) => {
            row.forEach((ratio, j) => {
                // 只显示有流量的单元格
                if (flowMatrix[i][j] > 0) {
                    // 如果异常比率超过阈值，或者不需要过滤
                    if (ratio >= severityThreshold) {
                        heatmapValues.push({
                            x: j,  // 目标IP索引
                            y: i,  // 源IP索引
                            z: ratio  // 异常比率
                        });
                    }
                }
            });
        });

        // 准备Plotly数据
        const trace = {
            x: dstIPs,
            y: srcIPs,
            z: ratioMatrix,
            type: 'heatmap',
            colorscale: [
                [0, 'rgb(0, 255, 0)'],      // 绿色（安全）
                [0.5, 'rgb(255, 255, 0)'],   // 黄色（警告）
                [1, 'rgb(255, 0, 0)']        // 红色（危险）
            ],
            showscale: true,
            colorbar: {
                title: '异常比率 (%)',
                titleside: 'right'
            },
            hoverinfo: 'text',
            text: ratioMatrix.map((row, i) =>
                row.map((ratio, j) =>
                    `源IP: ${srcIPs[i]}<br>目标IP: ${dstIPs[j]}<br>流量数: ${flowMatrix[i][j]}<br>异常比率: ${ratio.toFixed(1)}%`
                )
            )
        };

        // 图表布局
        const layout = {
            title: '',
            margin: { t: 50, r: 50, b: 100, l: 100 },
            xaxis: {
                title: '目标 IP',
                tickangle: -45
            },
            yaxis: {
                title: '源 IP'
            },
            annotations: []
        };

        // 添加注释，显示异常比率
        ratioMatrix.forEach((row, i) => {
            row.forEach((ratio, j) => {
                if (flowMatrix[i][j] > 0 && ratio >= severityThreshold) {
                    layout.annotations.push({
                        x: dstIPs[j],
                        y: srcIPs[i],
                        text: `${ratio.toFixed(0)}%`,
                        showarrow: false,
                        font: {
                            color: ratio > 75 ? 'white' : 'black'
                        }
                    });
                }
            });
        });

        // 检查是否启用缩放
        const enableZoom = $('#enable-heatmap-zoom').is(':checked');
        if (enableZoom) {
            layout.dragmode = 'zoom';
            layout.showlegend = false;
        }

        // 绘制热图
        Plotly.newPlot('traffic-heatmap', [trace], layout, {
            responsive: true,
            displayModeBar: true,
            displaylogo: false,
            modeBarButtonsToRemove: ['lasso2d', 'select2d']
        });

        // 存储热图实例
        heatmapInstance = document.getElementById('traffic-heatmap');

        // 添加点击事件
        heatmapInstance.on('plotly_click', function(data) {
            const point = data.points[0];
            const srcIP = srcIPs[point.y];
            const dstIP = dstIPs[point.x];
            const ratio = ratioMatrix[point.y][point.x];
            const flowCount = flowMatrix[point.y][point.x];

            // 弹出详细信息
            alert(`流量详情:\n源IP: ${srcIP}\n目标IP: ${dstIP}\n流量数: ${flowCount}\n异常比率: ${ratio.toFixed(1)}%`);
        });
    }

    // 全局变量用于跟踪文件处理
    let filesToProcess = [];
    let currentFileIndex = 0;
    let totalFiles = 0;
    let startTime = 0;
    let fileStartTime = 0;
    let processedFilesSize = 0;
    let totalFilesSize = 0;
    let analysisResults = [];

    // 处理文件上传
    $('#upload-analyze-btn').on('click', function() {
        const fileInput = $('#pcap-file')[0];

        if (!fileInput.files.length) {
            alert('请选择至少一个 PCAP 文件');
            return;
        }

        // 重置全局变量
        filesToProcess = [];
        currentFileIndex = 0;
        totalFiles = fileInput.files.length;
        startTime = Date.now();
        fileStartTime = startTime;
        processedFilesSize = 0;
        totalFilesSize = 0;
        analysisResults = [];

        // 计算文件总大小
        for (let i = 0; i < fileInput.files.length; i++) {
            totalFilesSize += fileInput.files[i].size;
        }

        // 准备文件列表显示
        const fileListContainer = $('#file-list');
        fileListContainer.empty();

        for (let i = 0; i < fileInput.files.length; i++) {
            const file = fileInput.files[i];
            filesToProcess.push(file);

            // 创建文件项
            const fileItem = $(`
                <div class="file-item mb-2" id="file-item-${i}">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="file-name">${file.name}</span>
                        <span class="file-status badge bg-secondary">等待中</span>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div id="progress-bar-${i}" class="progress-bar" role="progressbar" style="width: 0%"
                             aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            `);

            fileListContainer.append(fileItem);
        }

        // 显示进度区域
        $('#analysis-progress').show();
        $('#no-data-message').hide();
        $('#results-container').hide();

        // 更新总进度条和状态
        updateTotalProgress(0);
        $('#progress-status').text('正在上传文件...');

        // 开始处理第一个文件
        processNextFile();
    });

    // 处理下一个文件
    function processNextFile() {
        if (currentFileIndex >= filesToProcess.length) {
            // 所有文件处理完毕
            finishProcessing();
            return;
        }

        const file = filesToProcess[currentFileIndex];
        const fileItem = $(`#file-item-${currentFileIndex}`);
        fileItem.find('.file-status').removeClass('bg-secondary').addClass('bg-primary').text('上传中');

        // 重置当前文件的开始时间
        fileStartTime = Date.now();

        // 上传文件
        const formData = new FormData();
        formData.append('file', file);

        $.ajax({
            url: '/upload-pcap',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhr: function() {
                const xhr = new window.XMLHttpRequest();

                // 上传进度
                xhr.upload.addEventListener('progress', function(evt) {
                    if (evt.lengthComputable) {
                        const percentComplete = (evt.loaded / evt.total) * 100;
                        $(`#progress-bar-${currentFileIndex}`).css('width', percentComplete + '%');

                        // 更新总进度
                        const overallProgress = (processedFilesSize + evt.loaded) / totalFilesSize * 100 / 2; // 上传占总进度50%
                        updateTotalProgress(overallProgress);

                        // 更新预计时间
                        updateEstimatedTime(evt.loaded, file.size, overallProgress);
                    }
                }, false);

                return xhr;
            },
            success: function(response) {
                if (response.success) {
                    fileItem.find('.file-status').removeClass('bg-primary').addClass('bg-info').text('分析中');
                    $(`#progress-bar-${currentFileIndex}`).css('width', '100%');

                    // 分析文件
                    analyzeFile(response.file_path, currentFileIndex);
                } else {
                    fileItem.find('.file-status').removeClass('bg-primary').addClass('bg-danger').text('失败');
                    console.error('文件上传失败:', response.error);

                    // 继续处理下一个文件
                    processedFilesSize += file.size;
                    currentFileIndex++;
                    processNextFile();
                }
            },
            error: function(xhr, status, error) {
                fileItem.find('.file-status').removeClass('bg-primary').addClass('bg-danger').text('失败');
                console.error('文件上传错误:', status, error);

                // 继续处理下一个文件
                processedFilesSize += file.size;
                currentFileIndex++;
                processNextFile();
            }
        });
    }

    // 分析文件
    function analyzeFile(filePath, fileIndex) {
        const modelType = $('#model-type').val();
        const file = filesToProcess[fileIndex];

        // 更新状态
        $('#progress-status').text(`正在分析: ${file.name}`);

        // 创建一个模拟进度的定时器，因为后端不提供实时进度
        let analysisProgress = 50; // 从50%开始（上传占前50%）
        const progressInterval = setInterval(() => {
            // 模拟分析进度，每次增加1%，但不超过95%
            if (analysisProgress < 95) {
                analysisProgress += 1;

                // 计算总进度（上传占50%，分析占50%）
                const overallProgress = 50 + (analysisProgress - 50) * (file.size / totalFilesSize);
                updateTotalProgress(overallProgress);

                // 更新预计时间
                const elapsedTime = Date.now() - fileStartTime;
                const progress = (analysisProgress - 50) / 50; // 分析进度百分比
                if (progress > 0) {
                    const totalEstimatedTime = elapsedTime / progress;
                    const remainingTime = totalEstimatedTime - elapsedTime;
                    updateEstimatedTimeDisplay(remainingTime);
                }
            }
        }, 500);

        $.ajax({
            url: '/api/analyze-pcap',
            type: 'POST',
            data: JSON.stringify({
                file_path: filePath,
                model_type: modelType
            }),
            contentType: 'application/json',
            success: function(data) {
                // 清除进度定时器
                clearInterval(progressInterval);

                // 更新文件状态
                const fileItem = $(`#file-item-${fileIndex}`);

                if (data.error) {
                    fileItem.find('.file-status').removeClass('bg-info').addClass('bg-danger').text('分析失败');
                    console.error('分析错误:', data.error);
                } else {
                    fileItem.find('.file-status').removeClass('bg-info').addClass('bg-success').text('完成');
                    $(`#progress-bar-${fileIndex}`).css('width', '100%').addClass('bg-success');

                    // 存储分析结果
                    analysisResults.push(data);

                    // 添加到文件选择器
                    addFileToResultSelector(file.name, analysisResults.length - 1);

                    // 如果是第一个文件，显示结果
                    if (analysisResults.length === 1) {
                        // 更新结果显示
                        displayFileResult(0);
                        $('#results-container').show();
                    }
                }

                // 更新已处理文件大小
                processedFilesSize += file.size;

                // 更新总进度
                const overallProgress = (processedFilesSize / totalFilesSize) * 100;
                updateTotalProgress(overallProgress);

                // 处理下一个文件
                currentFileIndex++;
                processNextFile();
            },
            error: function(xhr, status, error) {
                // 清除进度定时器
                clearInterval(progressInterval);

                // 更新文件状态
                const fileItem = $(`#file-item-${fileIndex}`);
                fileItem.find('.file-status').removeClass('bg-info').addClass('bg-danger').text('分析失败');
                console.error('分析请求失败:', status, error);

                // 更新已处理文件大小
                processedFilesSize += file.size;

                // 处理下一个文件
                currentFileIndex++;
                processNextFile();
            }
        });
    }

    // 更新总进度条
    function updateTotalProgress(percentage) {
        percentage = Math.min(100, Math.max(0, percentage)); // 确保在 0-100 范围内
        const roundedPercentage = Math.round(percentage);

        $('#total-progress-bar').css('width', roundedPercentage + '%');
        $('#total-progress-bar').attr('aria-valuenow', roundedPercentage);
        $('#total-progress-bar').text(roundedPercentage + '%');
        $('#progress-percentage').text(roundedPercentage + '%');

        // 如果完成，改变颜色
        if (roundedPercentage >= 100) {
            $('#total-progress-bar').removeClass('progress-bar-animated').addClass('bg-success');
            $('#progress-status').text('分析完成');
        }
    }

    // 更新预计时间显示
    function updateEstimatedTimeDisplay(milliseconds) {
        if (milliseconds <= 0) {
            $('#estimated-time').text('即将完成');
            return;
        }

        // 转换为秒
        const seconds = Math.round(milliseconds / 1000);

        if (seconds < 60) {
            $('#estimated-time').text(`预计剩余时间: ${seconds} 秒`);
        } else if (seconds < 3600) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            $('#estimated-time').text(`预计剩余时间: ${minutes} 分 ${remainingSeconds} 秒`);
        } else {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            $('#estimated-time').text(`预计剩余时间: ${hours} 小时 ${minutes} 分`);
        }
    }

    // 更新预计时间
    function updateEstimatedTime(loaded, total, overallProgress) {
        const elapsedTime = Date.now() - startTime;
        const progress = overallProgress / 100;

        if (progress > 0) {
            const totalEstimatedTime = elapsedTime / progress;
            const remainingTime = totalEstimatedTime - elapsedTime;
            updateEstimatedTimeDisplay(remainingTime);
        }
    }

    // 完成处理
    function finishProcessing() {
        // 更新总进度
        updateTotalProgress(100);
        $('#progress-status').text('分析完成');
        $('#estimated-time').text('已完成');

        // 如果没有成功的分析结果，显示错误信息
        if (analysisResults.length === 0) {
            $('#analysis-progress').hide();
            $('#no-data-message').show();
            alert('所有文件分析失败，请重试');
            return;
        }

        // 如果有多个文件分析成功，显示成功消息
        if (analysisResults.length > 1) {
            const successCount = analysisResults.length;
            const totalCount = filesToProcess.length;

            // 显示文件选择器
            $('#results-file-selector').removeClass('d-none');

            // 显示合并结果
            displayCombinedResults();

            alert(`分析完成！共 ${totalCount} 个文件，成功分析 ${successCount} 个。\n已显示所有文件的合并结果。`);
        }
    }

    // 添加文件到结果选择器
    function addFileToResultSelector(fileName, index) {
        // 如果有多个文件，显示选择器
        if (analysisResults.length > 1) {
            $('#results-file-selector').removeClass('d-none');
        }

        // 添加文件选项
        $('#file-result-selector').append(`<option value="${index}">${fileName}</option>`);
    }

    // 显示指定文件的结果
    function displayFileResult(index) {
        if (index < 0 || index >= analysisResults.length) {
            console.error('无效的文件索引:', index);
            return;
        }

        const data = analysisResults[index];

        // 更新结果显示
        updateResults(data);

        // 显示模型信息
        if (data.model_info) {
            const modelInfo = `使用模型: ${data.model_info.description}`;
            $('#model-info-display').text(modelInfo);
        }
    }

    // 显示合并结果
    function displayCombinedResults() {
        if (analysisResults.length === 0) {
            return;
        }

        // 使用第一个结果作为基础
        const baseResult = JSON.parse(JSON.stringify(analysisResults[0]));

        // 合并所有数据
        let allPackets = [];
        let allTimeSeries = [];
        let combinedAnomalyStats = {};

        // 遍历所有结果
        for (const result of analysisResults) {
            // 合并数据包
            if (result.packets && Array.isArray(result.packets)) {
                // 更新数据包编号，确保连续
                const startNumber = allPackets.length > 0 ? allPackets[allPackets.length - 1].number + 1 : 1;
                const adjustedPackets = result.packets.map((packet, index) => ({
                    ...packet,
                    number: startNumber + index
                }));

                allPackets = allPackets.concat(adjustedPackets);
            }

            // 合并时间序列
            if (result.time_series && Array.isArray(result.time_series)) {
                allTimeSeries = allTimeSeries.concat(result.time_series);
            }

            // 合并异常类型统计
            if (result.anomaly_stats) {
                for (const [type, count] of Object.entries(result.anomaly_stats)) {
                    combinedAnomalyStats[type] = (combinedAnomalyStats[type] || 0) + count;
                }
            }
        }

        // 按时间排序时间序列
        allTimeSeries.sort((a, b) => {
            const timeA = new Date(a.time).getTime();
            const timeB = new Date(b.time).getTime();
            return timeA - timeB;
        });

        // 更新基础结果
        baseResult.packets = allPackets;
        baseResult.time_series = allTimeSeries;
        baseResult.anomaly_stats = combinedAnomalyStats;

        // 更新结果显示
        updateResults(baseResult);

        // 更新模型信息
        $('#model-info-display').text('合并所有文件的结果');
    }

    // 更新结果
    function updateResults(data) {
        // 如果数据格式是新的（包含 packets 属性）
        const packets = data.packets || data;

        // 存储原始数据包列表供过滤使用
        allPackets = packets;

        // 更新统计信息
        const totalPackets = packets.length;
        const anomalyPackets = packets.filter(p => p.is_anomaly).length;
        const normalPackets = totalPackets - anomalyPackets;
        const anomalyRatio = ((anomalyPackets / totalPackets) * 100).toFixed(2);

        $('#total-packets').text(totalPackets);
        $('#anomaly-packets').text(anomalyPackets);
        $('#normal-packets').text(normalPackets);
        $('#anomaly-ratio').text(anomalyRatio + '%');

        // 检查是否只显示异常数据包
        const showOnlyAnomalies = $('#show-only-anomalies').is(':checked');
        const packetsToShow = showOnlyAnomalies ? packets.filter(p => p.is_anomaly) : packets;

        // 更新数据包表格
        updatePacketTable(packetsToShow);

        // 更新异常趋势图表
        updateAnomalyTrendChart(data.time_series);

        // 更新异常类型分布图表
        updateAnomalyTypeChart(data.anomaly_stats, packets);

        // 更新网络流量热图
        updateTrafficHeatmap(packets);
    }

    // 更新数据包表格
    function updatePacketTable(packetsToShow, filterType = null) {
        const tbody = $('#packet-table tbody');
        tbody.empty();

        // 如果有类型筛选，则只显示该类型的异常数据包
        const filteredPackets = filterType
            ? packetsToShow.filter(p => p.is_anomaly && p.attack_type === filterType)
            : packetsToShow;

        filteredPackets.forEach(packet => {
            tbody.append(`
                <tr class="${packet.is_anomaly ? 'table-danger' : ''}" data-packet-id="${packet.id}">
                    <td>${packet.number}</td>
                    <td>${packet.time}</td>
                    <td>${packet.src}</td>
                    <td>${packet.dst}</td>
                    <td>${packet.protocol}</td>
                    <td>${packet.length}</td>
                    <td>${packet.is_anomaly ? '异常' : '正常'}</td>
                    <td>${packet.is_anomaly ? packet.attack_type : '-'}</td>
                </tr>
            `);
        });
    }

    // 更新异常趋势图表
    function updateAnomalyTrendChart(timeSeriesData) {
        // 保存时间序列数据以便切换高亮时使用
        window.lastTimeSeriesData = timeSeriesData;

        const chartContainer = $('#anomaly-trend-chart');
        const noDataMessage = $('#no-trend-data');

        // 检查是否有时间序列数据
        if (!timeSeriesData || timeSeriesData.length < 2) {
            chartContainer.hide();
            noDataMessage.show();
            return;
        }

        chartContainer.show();
        noDataMessage.hide();

        // 准备图表数据
        const times = timeSeriesData.map(point => point.time);
        const ratios = timeSeriesData.map(point => point.anomaly_ratio);

        // 设置异常阈值（可以根据需要调整）
        const anomalyThreshold = 30; // 30%以上视为高异常

        // 创建基本数据集
        const mainTrace = {
            x: times,
            y: ratios,
            type: 'scatter',
            mode: 'lines+markers',
            name: '异常比率',
            line: { color: '#4e73df', width: 2 },
            marker: { size: 5 }
        };

        // 创建图表数据
        const chartData = [mainTrace];

        // 如果启用了高亮显示
        if ($('#highlight-threshold').is(':checked')) {
            // 找出超过阈值的点
            const highAnomalyPoints = {
                x: [],
                y: [],
                type: 'scatter',
                mode: 'markers',
                name: `高异常 (>${anomalyThreshold}%)`,
                marker: {
                    color: 'red',
                    size: 8,
                    symbol: 'circle'
                }
            };

            // 填充高异常点数据
            ratios.forEach((ratio, index) => {
                if (ratio >= anomalyThreshold) {
                    highAnomalyPoints.x.push(times[index]);
                    highAnomalyPoints.y.push(ratio);
                }
            });

            // 如果有高异常点，添加到图表
            if (highAnomalyPoints.x.length > 0) {
                chartData.push(highAnomalyPoints);
            }

            // 添加阈值线
            const thresholdLine = {
                x: [times[0], times[times.length - 1]],
                y: [anomalyThreshold, anomalyThreshold],
                type: 'scatter',
                mode: 'lines',
                name: '异常阈值',
                line: {
                    color: 'rgba(255, 0, 0, 0.5)',
                    width: 2,
                    dash: 'dash'
                }
            };

            chartData.push(thresholdLine);
        }

        // 图表布局
        const layout = {
            title: '',
            margin: { t: 10, r: 30, l: 60, b: 60 },
            xaxis: {
                title: '时间',
                tickangle: -45
            },
            yaxis: {
                title: '异常比率 (%)',
                range: [0, Math.max(100, Math.max(...ratios) * 1.1)]
            },
            hovermode: 'closest',
            legend: { orientation: 'h', y: -0.2 },
            showlegend: true
        };

        // 绘制图表
        Plotly.newPlot('anomaly-trend-chart', chartData, layout);
    }

    // 点击数据包显示详情
    $('#packet-table').on('click', 'tr', function() {
        const packetId = $(this).data('packet-id');
        if (packetId) {
            $.get(`/api/packet-detail/${packetId}`, function(data) {
                const accordion = $('#packet-detail-accordion');
                accordion.empty();

                Object.entries(data.layers).forEach(([layer, info], index) => {
                    accordion.append(`
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button ${index === 0 ? '' : 'collapsed'}" type="button"
                                        data-bs-toggle="collapse" data-bs-target="#layer-${index}">
                                    ${layer}
                                </button>
                            </h2>
                            <div id="layer-${index}" class="accordion-collapse collapse ${index === 0 ? 'show' : ''}"
                                 data-bs-parent="#packet-detail-accordion">
                                <div class="accordion-body">
                                    <pre><code>${JSON.stringify(info, null, 2)}</code></pre>
                                </div>
                            </div>
                        </div>
                    `);
                });

                $('#packet-detail-modal').modal('show');
            });
        }
    });

    // 点击查看模型指标按钮
    $('#view-model-metrics').click(function() {
        // 显示模态框和加载状态
        $('#metrics-loading').show();
        $('#metrics-content').hide();
        $('#metrics-error').hide();
        $('#model-metrics-modal').modal('show');

        // 请求模型指标数据
        $.get('/api/model-metrics', { model: 'anomaly_detector' }, function(response) {
            $('#metrics-loading').hide();

            if (response.success && response.metrics) {
                // 更新指标显示
                const metrics = response.metrics;

                // 格式化数字显示
                $('#metric-accuracy').text(metrics.accuracy ? (metrics.accuracy * 100).toFixed(2) + '%' : '-');
                $('#metric-latency').text(metrics.latency ? metrics.latency.toFixed(2) : '-');
                $('#metric-total-predictions').text(metrics.total_predictions || 0);
                $('#metric-decline-counter').text(metrics.decline_counter || 0);

                // 如果有准确率历史数据，绘制图表
                if (metrics.accuracy_history && metrics.accuracy_history.length > 0) {
                    const chartData = {
                        x: Array.from({ length: metrics.accuracy_history.length }, (_, i) => i + 1),
                        y: metrics.accuracy_history.map(val => val * 100), // 转换为百分比
                        type: 'scatter',
                        mode: 'lines+markers',
                        name: '准确率',
                        line: { color: '#4e73df' }
                    };

                    const layout = {
                        margin: { t: 30, r: 30, l: 50, b: 50 },
                        xaxis: { title: '预测次数' },
                        yaxis: { title: '准确率 (%)', range: [0, 100] }
                    };

                    Plotly.newPlot('accuracy-history-chart', [chartData], layout);
                } else {
                    $('#accuracy-history-chart').html('<div class="text-center text-muted">\u6ca1\u6709\u8db3\u591f\u7684\u5386\u53f2\u6570\u636e</div>');
                }

                $('#metrics-content').show();
            } else {
                $('#metrics-error').show();
                console.error('获取模型指标失败:', response.error || '未知错误');
            }
        }).fail(function(xhr, status, error) {
            $('#metrics-loading').hide();
            $('#metrics-error').show();
            console.error('请求模型指标失败:', status, error);
        });
    });

    // 文件结果选择器事件
    $('#file-result-selector').change(function() {
        const selectedValue = $(this).val();

        if (selectedValue === 'combined') {
            // 显示合并结果
            displayCombinedResults();
        } else {
            // 显示指定文件的结果
            const index = parseInt(selectedValue);
            if (!isNaN(index) && index >= 0 && index < analysisResults.length) {
                displayFileResult(index);
            }
        }
    });

});
</script>
{% endblock %}