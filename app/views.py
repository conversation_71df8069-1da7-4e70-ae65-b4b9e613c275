from flask import Blueprint, render_template, jsonify, request, current_app, send_from_directory
from . import db
from .SQLitemodels import PcapFile, NetworkFlow, AnalysisResult
from .utils.pcap_analyzer import PcapAnalyzer, PacketCapture
from .utils.flow_analyzer import FlowAnalyzer
from .utils.cache_manager import AnalysisCache
from .utils.analyzers import EnhancedEncryptionDetector, AdvancedAnomalyDetector
from .utils.model_monitoring import monitor as model_monitor, ModelPerformanceTracker
from .utils.data_augmentation import DataAugmentor
from .utils.hyperparameter_optimization import optimize_model_hyperparameters
import plotly.express as px
import pandas as pd
import os
import numpy as np
import torch
from werkzeug.utils import secure_filename
import json
from collections import defaultdict
import time
from datetime import datetime

main = Blueprint('main', __name__)
packet_capture = PacketCapture()

# 初始化数据增强器
data_augmentor = DataAugmentor({
    'use_masking': True,
    'mask_ratio': 0.2,
    'use_perturbation': True,
    'noise_level': 0.05,
    'use_sampling': True,
    'sampling_method': 'smote'
})

# 延迟初始化模型监控器
def init_model_monitor(app):
    # 确保模型目录存在
    models_dir = os.path.join(app.root_path, 'models')
    os.makedirs(models_dir, exist_ok=True)

    model_paths = {
        'anomaly_detector': os.path.join(models_dir, 'anomaly_model.pth'),
        'encryption_detector': os.path.join(models_dir, 'encryption_model.pth')
    }

    # 初始化模型监控器
    model_monitor.model_paths = model_paths

    # 创建模型跟踪器
    model_monitor.trackers = {
        'anomaly_detector': ModelPerformanceTracker(
            window_size=100,
            threshold=0.05,
            model_path=model_paths['anomaly_detector']
        ),
        'encryption_detector': ModelPerformanceTracker(
            window_size=100,
            threshold=0.05,
            model_path=model_paths['encryption_detector']
        )
    }

    model_monitor._initialized = True

    print(f"模型监控器初始化完成，跟踪器: {list(model_monitor.trackers.keys())}")

# 确保上传目录存在
def ensure_upload_dir():
    try:
        # 尝试使用current_app
        upload_folder = os.path.join(current_app.root_path, 'uploads')
    except RuntimeError:
        # 如果在应用上下文外调用，使用相对路径
        base_dir = os.path.abspath(os.path.dirname(__file__))
        upload_folder = os.path.join(base_dir, 'uploads')

    os.makedirs(upload_folder, exist_ok=True)
    return upload_folder

@main.route('/')
def index():
    """首页 - 网络流量监控"""
    return render_template('index.html')

@main.route('/api/network-interfaces')
def get_network_interfaces():
    """获取网络接口列表"""
    try:
        interfaces = packet_capture.get_network_interfaces()
        return jsonify({'success': True, 'interfaces': interfaces})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/start-capture', methods=['POST'])
def start_capture():
    """开始捕获数据包"""
    try:
        if not request.is_json:
            return jsonify({'success': False, 'error': '请求格式错误'}), 400

        data = request.get_json()
        interface = data.get('interface')

        if not interface:
            return jsonify({'success': False, 'error': '未指定网络接口'}), 400

        if packet_capture.start_capture(interface):
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': '启动捕获失败'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/stop-capture', methods=['POST'])
def stop_capture():
    """停止捕获数据包"""
    try:
        if packet_capture.stop_capture():
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': '停止捕获失败'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/capture-stats')
def get_capture_stats():
    """获取捕获统计信息"""
    try:
        stats = packet_capture.get_capture_stats()
        return jsonify({'success': True, **stats})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/latest-packets')
def get_latest_packets():
    """获取最新捕获的数据包信息"""
    try:
        # 获取查询参数
        since = request.args.get('since', 0, type=int)
        limit = request.args.get('limit', 100, type=int)

        # 安全检查
        if limit > 1000:
            limit = 1000  # 限制最大返回数量

        # 提取最新数据包
        latest_packets = []
        from scapy.all import IP, TCP, UDP

        # 确定起始索引
        start_idx = max(0, len(packet_capture.captured_packets) - limit)
        if since > 0 and since < len(packet_capture.captured_packets):
            start_idx = since

        # 获取数据包
        for i, packet in enumerate(packet_capture.captured_packets[start_idx:], start_idx):
            # 增强版本，提供更详细的数据包信息
            packet_info = {
                'id': i,
                'time': datetime.fromtimestamp(packet.time).strftime('%Y-%m-%d %H:%M:%S.%f'),
                'length': len(packet),
                'protocol': 'Unknown',
                'src_ip': '',
                'dst_ip': '',
                'src_port': '',
                'dst_port': '',
                'details': ''
            }

            # 处理IP数据包
            if IP in packet:
                packet_info['src_ip'] = packet[IP].src
                packet_info['dst_ip'] = packet[IP].dst
                ip_flags = packet[IP].flags
                ttl = packet[IP].ttl
                tos = packet[IP].tos

                # 添加IP详情
                packet_info['details'] = f'TTL: {ttl}, TOS: {tos}, Flags: {ip_flags}'

                # 处理TCP数据包
                if TCP in packet:
                    packet_info['protocol'] = 'TCP'
                    packet_info['src_port'] = packet[TCP].sport
                    packet_info['dst_port'] = packet[TCP].dport

                    # 提取TCP标志和应用层协议
                    tcp_flags = packet[TCP].flags
                    seq = packet[TCP].seq
                    ack = packet[TCP].ack
                    window = packet[TCP].window

                    # 根据端口识别常见应用层协议
                    app_proto = ''
                    if packet_info['dst_port'] == 80 or packet_info['src_port'] == 80:
                        app_proto = 'HTTP'
                    elif packet_info['dst_port'] == 443 or packet_info['src_port'] == 443:
                        app_proto = 'HTTPS'
                    elif packet_info['dst_port'] == 22 or packet_info['src_port'] == 22:
                        app_proto = 'SSH'
                    elif packet_info['dst_port'] == 21 or packet_info['src_port'] == 21:
                        app_proto = 'FTP'
                    elif packet_info['dst_port'] == 25 or packet_info['src_port'] == 25:
                        app_proto = 'SMTP'
                    elif packet_info['dst_port'] == 53 or packet_info['src_port'] == 53:
                        app_proto = 'DNS'

                    if app_proto:
                        packet_info['protocol'] = f'TCP/{app_proto}'

                    # 添加TCP详情
                    packet_info['details'] += f', TCP Flags: {tcp_flags}, Seq: {seq}, Ack: {ack}, Win: {window}'

                # 处理UDP数据包
                elif UDP in packet:
                    packet_info['protocol'] = 'UDP'
                    packet_info['src_port'] = packet[UDP].sport
                    packet_info['dst_port'] = packet[UDP].dport

                    # 根据端口识别常见应用层协议
                    app_proto = ''
                    if packet_info['dst_port'] == 53 or packet_info['src_port'] == 53:
                        app_proto = 'DNS'
                        # 尝试提取DNS信息
                        if packet.haslayer('DNS'):
                            dns = packet.getlayer('DNS')
                            if dns.qr == 0:  # 查询
                                qname = dns.qd.qname.decode() if dns.qd else ''
                                packet_info['details'] += f', DNS Query: {qname}'
                            else:  # 响应
                                packet_info['details'] += f', DNS Response'
                    elif packet_info['dst_port'] == 67 or packet_info['src_port'] == 67 or \
                         packet_info['dst_port'] == 68 or packet_info['src_port'] == 68:
                        app_proto = 'DHCP'
                    elif packet_info['dst_port'] == 123 or packet_info['src_port'] == 123:
                        app_proto = 'NTP'
                    elif packet_info['dst_port'] == 161 or packet_info['src_port'] == 161:
                        app_proto = 'SNMP'

                    if app_proto:
                        packet_info['protocol'] = f'UDP/{app_proto}'

                # 处理ICMP数据包
                elif packet.haslayer('ICMP'):
                    packet_info['protocol'] = 'ICMP'
                    icmp = packet.getlayer('ICMP')
                    icmp_type = icmp.type if hasattr(icmp, 'type') else ''
                    icmp_code = icmp.code if hasattr(icmp, 'code') else ''

                    # 添加ICMP类型信息
                    icmp_type_str = ''
                    if icmp_type == 0:
                        icmp_type_str = 'Echo Reply'
                    elif icmp_type == 8:
                        icmp_type_str = 'Echo Request'
                    elif icmp_type == 3:
                        icmp_type_str = 'Destination Unreachable'
                    elif icmp_type == 11:
                        icmp_type_str = 'Time Exceeded'

                    packet_info['details'] += f', ICMP Type: {icmp_type} ({icmp_type_str}), Code: {icmp_code}'

                # 其他IP协议
                else:
                    proto = packet[IP].proto
                    if proto == 1:
                        packet_info['protocol'] = 'ICMP'
                    elif proto == 2:
                        packet_info['protocol'] = 'IGMP'
                    elif proto == 6:
                        packet_info['protocol'] = 'TCP'
                    elif proto == 17:
                        packet_info['protocol'] = 'UDP'
                    elif proto == 50:
                        packet_info['protocol'] = 'ESP'
                    elif proto == 51:
                        packet_info['protocol'] = 'AH'
                    elif proto == 47:
                        packet_info['protocol'] = 'GRE'
                    elif proto == 132:
                        packet_info['protocol'] = 'SCTP'
                    else:
                        packet_info['protocol'] = f'IP({proto})'

            # 处理ARP数据包
            elif packet.haslayer('ARP'):
                packet_info['protocol'] = 'ARP'
                arp = packet.getlayer('ARP')
                packet_info['src_ip'] = arp.psrc if hasattr(arp, 'psrc') else ''
                packet_info['dst_ip'] = arp.pdst if hasattr(arp, 'pdst') else ''

                # 添加ARP操作类型
                arp_op = arp.op if hasattr(arp, 'op') else 0
                if arp_op == 1:
                    packet_info['details'] = 'ARP Request'
                elif arp_op == 2:
                    packet_info['details'] = 'ARP Reply'
                else:
                    packet_info['details'] = f'ARP Operation: {arp_op}'

            # 处理IPv6数据包
            elif packet.haslayer('IPv6'):
                ipv6 = packet.getlayer('IPv6')
                packet_info['protocol'] = 'IPv6'
                packet_info['src_ip'] = ipv6.src if hasattr(ipv6, 'src') else ''
                packet_info['dst_ip'] = ipv6.dst if hasattr(ipv6, 'dst') else ''

                # 添加IPv6详情
                next_header = ipv6.nh if hasattr(ipv6, 'nh') else ''
                hop_limit = ipv6.hlim if hasattr(ipv6, 'hlim') else ''
                packet_info['details'] = f'Next Header: {next_header}, Hop Limit: {hop_limit}'

                # 处理IPv6上的传输层
                if packet.haslayer('TCP'):
                    tcp = packet.getlayer('TCP')
                    packet_info['protocol'] = 'IPv6/TCP'
                    packet_info['src_port'] = tcp.sport if hasattr(tcp, 'sport') else ''
                    packet_info['dst_port'] = tcp.dport if hasattr(tcp, 'dport') else ''
                elif packet.haslayer('UDP'):
                    udp = packet.getlayer('UDP')
                    packet_info['protocol'] = 'IPv6/UDP'
                    packet_info['src_port'] = udp.sport if hasattr(udp, 'sport') else ''
                    packet_info['dst_port'] = udp.dport if hasattr(udp, 'dport') else ''
                elif packet.haslayer('ICMPv6'):
                    packet_info['protocol'] = 'ICMPv6'
                    icmpv6 = packet.getlayer('ICMPv6')
                    icmpv6_type = icmpv6.type if hasattr(icmpv6, 'type') else ''
                    packet_info['details'] += f', ICMPv6 Type: {icmpv6_type}'

            # 处理以太网帧
            elif packet.haslayer('Ether'):
                ether = packet.getlayer('Ether')
                packet_info['protocol'] = 'Ethernet'
                packet_info['src_ip'] = ether.src if hasattr(ether, 'src') else ''
                packet_info['dst_ip'] = ether.dst if hasattr(ether, 'dst') else ''

                # 添加以太网类型
                eth_type = ether.type if hasattr(ether, 'type') else 0
                packet_info['details'] = f'Ether Type: {hex(eth_type)}'

            latest_packets.append(packet_info)

        return jsonify({
            'success': True,
            'packets': latest_packets,
            'total': len(packet_capture.captured_packets),
            'next_index': min(start_idx + len(latest_packets), len(packet_capture.captured_packets))
        })
    except Exception as e:
        print(f"获取最新数据包出错: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/save-capture', methods=['POST'])
def save_capture():
    """保存捕获的流量为PCAP文件"""
    try:
        if not packet_capture.captured_packets:
            return jsonify({'success': False, 'error': '没有捕获的数据包可以保存'}), 400

        # 获取请求数据
        if not request.is_json:
            return jsonify({'success': False, 'error': '请求格式错误，需要JSON格式'}), 415

        data = request.get_json()
        custom_filename = data.get('filename')
        save_location = data.get('location', 'default')
        custom_path = data.get('custom_path', '')

        # 处理文件名
        if not custom_filename:
            # 生成默认文件名，使用当前时间戳
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"capture_{timestamp}.pcap"
        else:
            # 确保文件名安全且有.pcap扩展名
            filename = secure_filename(custom_filename)
            if not filename.lower().endswith('.pcap'):
                filename += '.pcap'

        # 确定保存路径
        if save_location == 'default':
            # 默认位置（app/uploads）
            save_dir = ensure_upload_dir()
        elif save_location == 'downloads':
            # 下载文件夹
            save_dir = os.path.join(os.path.expanduser('~'), 'Downloads')
            if not os.path.exists(save_dir):
                # 如果下载文件夹不存在，尝试其他常见位置
                possible_dirs = [
                    os.path.join(os.path.expanduser('~'), 'Download'),
                    os.path.join(os.path.expanduser('~'), '下载')
                ]
                for dir in possible_dirs:
                    if os.path.exists(dir):
                        save_dir = dir
                        break
                else:
                    # 如果所有尝试都失败，回退到默认位置
                    save_dir = ensure_upload_dir()
        elif save_location == 'custom':
            # 自定义位置
            if not custom_path:
                return jsonify({'success': False, 'error': '选择了自定义位置但未提供路径'}), 400

            # 尝试将相对路径转换为绝对路径
            if not os.path.isabs(custom_path):
                custom_path = os.path.abspath(custom_path)

            save_dir = custom_path

            # 检查目录是否存在，如果不存在尝试创建
            if not os.path.exists(save_dir):
                try:
                    os.makedirs(save_dir, exist_ok=True)
                except Exception as e:
                    return jsonify({'success': False, 'error': f'无法创建目录: {str(e)}'}), 400

            # 检查目录是否可写
            if not os.access(save_dir, os.W_OK):
                return jsonify({'success': False, 'error': f'目录无写入权限: {save_dir}'}), 400
        else:
            # 未知的位置类型，使用默认位置
            save_dir = ensure_upload_dir()

        # 完整的文件路径
        file_path = os.path.join(save_dir, filename)

        # 保存PCAP文件
        from scapy.all import wrpcap
        wrpcap(file_path, packet_capture.captured_packets)

        # 添加到数据库
        pcap_file = PcapFile(
            filename=filename,
            file_path=file_path,
            file_size=os.path.getsize(file_path)
        )
        db.session.add(pcap_file)
        db.session.commit()

        return jsonify({
            'success': True,
            'file_path': file_path,
            'message': '捕获流量已保存为PCAP文件'
        })
    except Exception as e:
        print(f"保存捕获流量出错: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/packet-detail/<int:packet_id>')
def get_packet_detail(packet_id):
    """获取数据包详细信息"""
    try:
        packet = NetworkFlow.query.get_or_404(packet_id)
        return jsonify({
            'id': packet.id,
            'time': packet.timestamp.strftime('%Y-%m-%d %H:%M:%S.%f'),
            'layers': json.loads(packet.layers)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@main.route('/upload-pcap', methods=['POST'])
def upload_pcap():
    """上传PCAP文件"""
    try:
        print("\n\n接收到上传请求")
        print("请求方法:", request.method)
        print("请求内容类型:", request.content_type)
        print("请求文件:", request.files)

        if 'file' not in request.files:
            print("错误: 没有文件被上传")
            return jsonify({'success': False, 'error': '没有文件被上传'}), 400

        file = request.files['file']
        print("文件名:", file.filename)

        if file.filename == '':
            print("错误: 未选择文件")
            return jsonify({'success': False, 'error': '未选择文件'}), 400

        if not file.filename.lower().endswith(('.pcap', '.pcapng')):
            print("错误: 文件类型不支持")
            return jsonify({'success': False, 'error': '只支持PCAP/PCAPNG文件'}), 400

        # 安全地保存文件
        filename = secure_filename(file.filename)
        upload_folder = ensure_upload_dir()
        file_path = os.path.join(upload_folder, filename)
        print("保存文件到:", file_path)

        try:
            file.save(file_path)
            print("文件保存成功")
        except Exception as save_error:
            print("文件保存失败:", str(save_error))
            return jsonify({'success': False, 'error': f'文件保存失败: {str(save_error)}'}), 500

        # 创建数据库记录
        try:
            pcap_file = PcapFile(
                filename=filename,
                file_path=file_path,
                file_size=os.path.getsize(file_path)
            )
            db.session.add(pcap_file)
            db.session.commit()
            print("数据库记录创建成功, ID:", pcap_file.id)
        except Exception as db_error:
            print("数据库记录创建失败:", str(db_error))
            return jsonify({'success': False, 'error': f'数据库记录创建失败: {str(db_error)}'}), 500

        return jsonify({
            'success': True,
            'file_path': file_path
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/analyze-pcap', methods=['POST'])
def analyze_pcap():
    """分析PCAP文件，检测异常流量"""
    try:
        if not request.is_json:
            return jsonify({'success': False, 'error': '请求格式错误，需要JSON格式'}), 415

        file_path = request.json.get('file_path')
        if not file_path:
            return jsonify({'success': False, 'error': '未提供文件路径'}), 400

        if not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件不存在'}), 404

        # 获取模型类型参数，默认使用高级模型
        model_type = request.json.get('model_type', 'advanced')
        # 验证模型类型
        if model_type not in ['simple', 'cnnlstm', 'advanced', 'rescnn-ablgan']:
            model_type = 'advanced'  # 如果无效，使用默认值

        # 将 rescnn-ablgan 映射到 advanced，保持兼容性
        if model_type == 'rescnn-ablgan':
            model_type = 'advanced'

        # 查找对应的PCAP文件记录
        pcap_file = PcapFile.query.filter_by(file_path=file_path).first()
        if not pcap_file:
            return jsonify({'success': False, 'error': '文件未在数据库中注册'}), 404

        # 检查缓存中是否有结果
        # 将模型类型添加到缓存键中，以区分不同模型的结果
        cache_key = f'anomaly_{model_type}'
        cached_result = AnalysisCache.get_cached_result(pcap_file.id, cache_key)
        if cached_result:
            print(f"使用缓存的异常检测结果，模型类型: {model_type}, PCAP文件ID: {pcap_file.id}")
            return jsonify(cached_result)

        # 使用高级异常检测器进行分析
        start_time = time.time()
        anomaly_detector = AdvancedAnomalyDetector(model_type=model_type)
        result = anomaly_detector.analyze_pcap_file(file_path)
        analysis_time = time.time() - start_time
        print(f"使用{model_type}模型分析完成，共有 {len(result['packets'])} 个数据包")

        # 更新模型监控指标
        # 计算平均延迟
        avg_latency = analysis_time / max(1, len(result['packets']))

        # 记录延迟指标
        for _ in range(min(10, len(result['packets']))):
            model_monitor.update(
                'anomaly_detector',
                prediction=None,
                latency=avg_latency
            )

        # 如果有真实标签，记录准确率
        if 'ground_truth' in request.json:
            ground_truth = request.json.get('ground_truth', {})
            for packet in result.get('packets', []):
                packet_id = packet.get('id')
                if packet_id in ground_truth:
                    model_monitor.update(
                        'anomaly_detector',
                        prediction=1 if packet.get('is_anomaly') else 0,
                        ground_truth=1 if ground_truth[packet_id] else 0
                    )

        # 添加模型信息
        result['model_info'] = {
            'type': model_type,
            'description': {
                'simple': '基础LSTM模型',
                'cnnlstm': '标准CNN-LSTM模型',
                'advanced': 'ResCNN-ABLGAN模型'
            }.get(model_type, '未知模型')
        }

        # 缓存分析结果
        AnalysisCache.cache_result(pcap_file.id, cache_key, result)
        print(f"异常检测结果已缓存，模型类型: {model_type}, PCAP文件ID: {pcap_file.id}")

        return jsonify(result)
    except Exception as e:
        print(f"异常检测错误: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/analyze-encryption', methods=['POST'])
def analyze_encryption():
    """分析PCAP文件，检测加密流量"""
    try:
        if not request.is_json:
            return jsonify({'success': False, 'error': '请求格式错误，需要JSON格式'}), 415

        file_path = request.json.get('file_path')
        if not file_path:
            return jsonify({'success': False, 'error': '未提供文件路径'}), 400

        if not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件不存在'}), 404

        # 获取模型类型参数，默认使用高级模型
        model_type = request.json.get('model_type', 'advanced')
        # 验证模型类型
        if model_type not in ['simple', 'cnnlstm', 'advanced', 'rescnn-ablgan']:
            model_type = 'advanced'  # 如果无效，使用默认值

        # 将 rescnn-ablgan 映射到 advanced，保持兼容性
        if model_type == 'rescnn-ablgan':
            model_type = 'advanced'

        # 查找对应的PCAP文件记录
        pcap_file = PcapFile.query.filter_by(file_path=file_path).first()
        if not pcap_file:
            return jsonify({'success': False, 'error': '文件未在数据库中注册'}), 404

        # 检查缓存中是否有结果
        # 将模型类型添加到缓存键中，以区分不同模型的结果
        cache_key = f'encryption_{model_type}'
        cached_result = AnalysisCache.get_cached_result(pcap_file.id, cache_key)
        if cached_result:
            print(f"使用缓存的加密检测结果，模型类型: {model_type}, PCAP文件ID: {pcap_file.id}")
            return jsonify(cached_result)

        # 使用增强的加密检测器进行分析
        start_time = time.time()
        encryption_detector = EnhancedEncryptionDetector(model_type=model_type)
        result = encryption_detector.analyze_file_for_encryption(file_path)
        analysis_time = time.time() - start_time
        print(f"使用{model_type}模型分析完成，共有 {len(result['packets'])} 个数据包，其中加密数据包 {result['summary']['encrypted_packets']} 个")

        # 更新模型监控指标
        # 计算平均延迟
        avg_latency = analysis_time / max(1, len(result['packets']))

        # 记录延迟指标
        for _ in range(min(10, len(result['packets']))):
            model_monitor.update(
                'encryption_detector',
                prediction=None,
                latency=avg_latency
            )

        # 如果有真实标签，记录准确率
        if 'ground_truth' in request.json:
            ground_truth = request.json.get('ground_truth', {})
            for packet in result.get('packets', []):
                packet_id = packet.get('id')
                if packet_id in ground_truth:
                    model_monitor.update(
                        'encryption_detector',
                        prediction=1 if packet.get('is_encrypted') else 0,
                        ground_truth=1 if ground_truth[packet_id] else 0
                    )

        # 添加模型信息
        result['model_info'] = {
            'type': model_type,
            'description': {
                'simple': '基础LSTM模型',
                'cnnlstm': '标准CNN-LSTM模型',
                'advanced': 'ResCNN-ABLGAN模型（残差连接+双向LSTM+注意力+多任务+GAN）'
            }.get(model_type, '未知模型')
        }

        # 缓存分析结果
        AnalysisCache.cache_result(pcap_file.id, cache_key, result)
        print(f"加密检测结果已缓存，模型类型: {model_type}, PCAP文件ID: {pcap_file.id}")

        return jsonify(result)
    except Exception as e:
        print(f"加密分析错误: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/pcap-files')
def list_pcap_files():
    """列出所有上传的PCAP文件"""
    files = PcapFile.query.all()
    return jsonify([{
        'id': f.id,
        'filename': f.filename,
        'size': f.file_size,
        'created_at': f.created_at.isoformat()
    } for f in files])

@main.route('/download-pcap/<int:file_id>')
def download_pcap(file_id):
    """下载PCAP文件"""
    pcap_file = PcapFile.query.get_or_404(file_id)
    return send_from_directory(
        os.path.dirname(pcap_file.file_path),
        os.path.basename(pcap_file.file_path),
        as_attachment=True
    )



@main.route('/api/optimize-hyperparameters', methods=['POST'])
def optimize_hyperparameters():
    """优化模型超参数"""
    try:
        if not request.is_json:
            return jsonify({'success': False, 'error': '请求格式错误，需要JSON格式'}), 415

        data = request.get_json()
        model_type = data.get('model_type', 'advanced')
        method = data.get('method', 'bayesian')
        n_trials = data.get('n_trials', 30)

        # 这里只是模拟超参数优化过程，实际应用中需要提供训练数据
        # 并调用optimize_model_hyperparameters函数

        # 模拟优化结果
        best_params = {
            'hidden_size': 128,
            'num_layers': 2,
            'dropout': 0.5,
            'lr': 0.001
        }

        return jsonify({
            'success': True,
            'message': f'超参数优化完成，使用{method}方法，运行{n_trials}次试验',
            'best_params': best_params
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/realtime-traffic')
def get_realtime_traffic():
    """获取实时流量数据"""
    analyzer = PcapAnalyzer()
    traffic_data = analyzer.get_realtime_traffic()
    return jsonify(traffic_data)

@main.route('/api/trigger-model-training', methods=['POST'])
def trigger_model_training():
    """手动触发模型增量训练"""
    try:
        if not request.is_json:
            return jsonify({'success': False, 'error': '请求格式错误，需要JSON格式'}), 415

        data = request.get_json()
        model_name = data.get('model_name')

        if not model_name:
            return jsonify({'success': False, 'error': '未提供模型名称'}), 400

        # 检查模型名称是否有效
        valid_models = list(model_monitor.trackers.keys()) if hasattr(model_monitor, 'trackers') else []
        if model_name not in valid_models:
            return jsonify({
                'success': False,
                'error': f'无效的模型名称: {model_name}',
                'valid_models': valid_models
            }), 400

        # 触发模型重训练
        result = model_monitor.trigger_retraining(model_name)

        if result:
            return jsonify({
                'success': True,
                'message': f'已成功触发模型 {model_name} 的增量训练',
                'status': '训练已在后台启动，请稍后查看训练日志'
            })
        else:
            return jsonify({
                'success': False,
                'error': f'无法触发模型 {model_name} 的增量训练，可能是训练数据不足或模型已在训练中'
            }), 400

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/model-training-status')
def get_model_training_status():
    """获取模型训练状态"""
    try:
        model_name = request.args.get('model_name')

        if not model_name:
            # 返回所有模型的状态
            if not hasattr(model_monitor, 'trackers') or not model_monitor.trackers:
                return jsonify({'success': False, 'error': '模型监控器未初始化或跟踪器为空'}), 404

            status = {}
            for name, tracker in model_monitor.trackers.items():
                status[name] = {
                    'training_in_progress': tracker.training_in_progress,
                    'training_triggered': tracker.training_triggered,
                    'queue_size': tracker.training_queue.qsize(),
                    'metrics': tracker.get_metrics()
                }
            return jsonify({'success': True, 'status': status})

        # 获取指定模型的状态
        if not hasattr(model_monitor, 'trackers') or model_name not in model_monitor.trackers:
            return jsonify({
                'success': False,
                'error': f'模型 {model_name} 不存在或监控器未初始化',
                'valid_models': list(model_monitor.trackers.keys()) if hasattr(model_monitor, 'trackers') else []
            }), 404

        tracker = model_monitor.trackers[model_name]

        # 获取训练日志
        training_log = []
        if tracker.model_path:
            training_log_path = os.path.join(os.path.dirname(tracker.model_path), 'training_log.json')
            if os.path.exists(training_log_path):
                try:
                    with open(training_log_path, 'r') as f:
                        training_log = json.load(f)
                except Exception as e:
                    print(f"读取训练日志失败: {str(e)}")

        return jsonify({
            'success': True,
            'status': {
                'training_in_progress': tracker.training_in_progress,
                'training_triggered': tracker.training_triggered,
                'queue_size': tracker.training_queue.qsize(),
                'metrics': tracker.get_metrics(),
                'training_log': training_log
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/add-training-data', methods=['POST'])
def add_training_data():
    """添加训练数据到模型训练队列"""
    try:
        if not request.is_json:
            return jsonify({'success': False, 'error': '请求格式错误，需要JSON格式'}), 415

        data = request.get_json()
        model_name = data.get('model_name')
        features = data.get('features')
        label = data.get('label')
        count = data.get('count', 1)  # 默认添加1个样本

        # 验证输入
        if not model_name:
            return jsonify({'success': False, 'error': '未提供模型名称'}), 400

        if features is None or label is None:
            return jsonify({'success': False, 'error': '特征或标签不能为空'}), 400

        # 检查模型名称是否有效
        if not hasattr(model_monitor, 'trackers') or model_name not in model_monitor.trackers:
            return jsonify({
                'success': False,
                'error': f'模型 {model_name} 不存在或监控器未初始化',
                'valid_models': list(model_monitor.trackers.keys()) if hasattr(model_monitor, 'trackers') else []
            }), 404

        # 获取跟踪器
        tracker = model_monitor.trackers[model_name]

        # 将特征转换为数组
        if isinstance(features, list):
            features_array = np.array(features, dtype=np.float32)
        else:
            return jsonify({'success': False, 'error': '特征必须是数组'}), 400

        # 将标签转换为整数
        try:
            label_int = int(label)
        except (ValueError, TypeError):
            return jsonify({'success': False, 'error': '标签必须是整数'}), 400

        # 将数据添加到训练队列
        for _ in range(count):
            # 转换为张量
            features_tensor = torch.tensor(features_array, dtype=torch.float32)
            tracker.update(prediction=label_int, ground_truth=label_int, features=features_tensor)

        return jsonify({
            'success': True,
            'message': f'已成功添加 {count} 个样本到模型 {model_name} 的训练队列',
            'queue_size': tracker.training_queue.qsize()
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/anomaly-detection')
def anomaly_detection():
    """异常检测页面"""
    return render_template('anomaly_detection.html')

@main.route('/encryption-detection')
def encryption_detection():
    """加密检测页面"""
    return render_template('encryption_detection.html')

@main.route('/api/model-metrics')
def get_model_metrics():
    """获取模型性能指标"""
    try:
        # 获取查询参数
        model_name = request.args.get('model', None)

        # 获取模型指标
        metrics = model_monitor.get_metrics(model_name)

        if metrics is None:
            return jsonify({
                'success': False,
                'error': f'模型 {model_name} 不存在或未初始化'
            }), 404

        return jsonify({
            'success': True,
            'metrics': metrics
        })
    except Exception as e:
        print(f"获取模型指标错误: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/detect-encryption', methods=['POST'])
def detect_encryption():
    """检测加密流量"""
    try:
        file_path = request.json.get('file_path')
        if not file_path:
            return jsonify({'success': False, 'error': '未提供文件路径'}), 400

        if not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件不存在'}), 404

        # 获取模型类型参数，默认使用高级模型
        model_type = request.json.get('model_type', 'advanced')
        # 验证模型类型
        if model_type not in ['simple', 'cnnlstm', 'advanced', 'rescnn-ablgan']:
            model_type = 'advanced'  # 如果无效，使用默认值

        # 将 rescnn-ablgan 映射到 advanced，保持兼容性
        if model_type == 'rescnn-ablgan':
            model_type = 'advanced'

        # 查找对应的PCAP文件记录
        pcap_file = PcapFile.query.filter_by(file_path=file_path).first()
        if not pcap_file:
            return jsonify({'success': False, 'error': '文件未在数据库中注册'}), 404

        # 检查缓存中是否有结果
        # 将模型类型添加到缓存键中，以区分不同模型的结果
        cache_key = f'encryption_detect_{model_type}'
        cached_result = AnalysisCache.get_cached_result(pcap_file.id, cache_key)
        if cached_result:
            print(f"使用缓存的加密检测结果，模型类型: {model_type}, PCAP文件ID: {pcap_file.id}")
            return jsonify(cached_result)

        # 使用增强的加密检测器进行分析
        encryption_detector = EnhancedEncryptionDetector(model_type=model_type)
        result = encryption_detector.analyze_file_for_encryption(file_path)
        print(f"使用{model_type}模型分析完成，共有 {len(result['packets'])} 个数据包，其中加密数据包 {result['summary']['encrypted_packets']} 个")

        # 添加模型信息
        result['model_info'] = {
            'type': model_type,
            'description': {
                'simple': '基础LSTM模型',
                'cnnlstm': '标准CNN-LSTM模型',
                'advanced': 'ResCNN-ABLGAN模型（残差连接+双向LSTM+注意力+多任务+GAN）'
            }.get(model_type, '未知模型')
        }

        # 缓存分析结果
        AnalysisCache.cache_result(pcap_file.id, cache_key, result)
        print(f"加密检测结果已缓存，模型类型: {model_type}, PCAP文件ID: {pcap_file.id}")

        return jsonify(result)
    except Exception as e:
        print(f"加密检测错误: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500