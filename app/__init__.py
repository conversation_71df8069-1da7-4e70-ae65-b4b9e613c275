from flask import Flask, render_template
from flask_sqlalchemy import SQLAlchemy
import os

db = SQLAlchemy()

def create_app():
    app = Flask(__name__)

    # 配置数据库
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///network_monitor.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = os.urandom(24)

    db.init_app(app)

    # 注册蓝图
    from .views import main, init_model_monitor
    app.register_blueprint(main)

    # 初始化模型监控器
    with app.app_context():
        init_model_monitor(app)

    return app