# Web框架和工具
Flask>=2.0.1
Werkzeug>=2.0.1
flask-sqlalchemy>=2.5.1
flask-login>=0.5.0
flask-wtf>=0.15.1
python-dotenv>=0.19.0

# 数据处理和分析
scapy>=2.4.5
pandas>=1.3.3
numpy>=1.21.2
dpkt>=1.9.7
scikit-learn>=0.24.2

# 系统监控
psutil>=5.8.0

# 深度学习
torch>=1.9.0
torchvision>=0.10.0
torchtext>=0.10.0
tqdm>=4.62.3

# 可视化
matplotlib>=3.4.3
plotly>=5.3.1

# 安全相关
cryptography>=3.4.7

# 可选依赖
opencv-python>=4.5.3  # 用于Grad-CAM可视化
Pillow>=8.3.1  # 图像处理

# 模型训练与评估
seaborn>=0.11.2  # 高级可视化
scipy>=1.7.1  # 科学计算
optuna>=2.10.0  # 超参数优化
shap>=0.40.0  # 模型可解释性

# 数据增强与处理
imbalanced-learn>=0.8.1  # 处理不平衡数据
albumentations>=1.1.0  # 数据增强