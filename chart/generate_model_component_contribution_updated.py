import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import os

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 创建保存图表的目录
save_dir = "D:/aaaabysj/network_monitor最终 - 副本/chart"
os.makedirs(save_dir, exist_ok=True)

# 设置Seaborn风格
sns.set(style="whitegrid")

# 数据
configurations = [
    '完整ResCNN-ABLGAN', 
    '无残差连接', 
    '替换CNN为普通全连接层', 
    '无注意力机制',
    '替换双向LSTM为单向LSTM', 
    '无GAN组件', 
    '单任务学习'
]

accuracies = [97.5, 94.0, 93.2, 94.8, 95.6, 95.1, 94.7]
decreases = [0, 3.5, 4.3, 2.7, 1.9, 2.4, 2.8]

# 创建图表
plt.figure(figsize=(12, 8))

# 设置颜色
colors = ['#1f77b4', '#ff7f0e', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']

# 创建柱状图
bars = plt.bar(configurations, accuracies, color=colors, width=0.6)

# 添加数值标签
for i, bar in enumerate(bars):
    height = bar.get_height()
    if i == 0:
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.3,
                f'{height}%',
                ha='center', va='bottom', fontsize=11, fontweight='bold')
    else:
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.3,
                f'{height}%\n(↓{decreases[i]}%)',
                ha='center', va='bottom', fontsize=11, color='#d62728')

# 设置坐标轴
plt.ylim(90, 100)  # 修正Y轴范围
plt.ylabel('准确率 (%)', fontsize=14)
plt.xticks(rotation=15, ha='right', fontsize=12)
plt.yticks(fontsize=12)

# 添加标题
plt.title('图5-3 模型组件贡献分析', fontsize=16, fontweight='bold', pad=20)
plt.suptitle('各组件对模型准确率的影响', fontsize=14, y=0.95)

# 添加网格线
plt.grid(axis='y', linestyle='--', alpha=0.7)

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig(os.path.join(save_dir, "model_component_contribution.png"), dpi=300, bbox_inches='tight')
plt.close()

print("模型组件贡献分析图表已生成并保存。")
