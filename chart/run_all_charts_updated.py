import os
import subprocess
import time

# 设置脚本目录
script_dir = "D:/aaaabysj/network_monitor最终 - 副本/chart"

# 要运行的脚本列表
scripts = [
    "generate_model_component_contribution_updated.py",
    "generate_precision_recall_curve_updated.py",
    "generate_dataset_accuracy_comparison_updated.py",
    "generate_anomaly_type_accuracy_updated.py",
    "generate_threshold_analysis_updated.py",
    "generate_roc_curve_comparison_updated.py"
]

# 运行所有脚本
print("开始生成所有图表...")
for script in scripts:
    script_path = os.path.join(script_dir, script)
    print(f"正在运行: {script}")
    try:
        start_time = time.time()
        subprocess.run(["python", script_path], check=True)
        end_time = time.time()
        print(f"完成: {script} (耗时: {end_time - start_time:.2f}秒)")
    except subprocess.CalledProcessError as e:
        print(f"错误: 运行 {script} 时出错: {e}")
    except Exception as e:
        print(f"错误: {e}")
    print("-" * 50)

print("所有图表生成完成！")
print(f"图表保存在: {script_dir}")
