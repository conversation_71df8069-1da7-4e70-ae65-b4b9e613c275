import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import os
import sys

# 添加字体修复模块的路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from fix_chinese_font import setup_chinese_font

# 设置中文字体
setup_chinese_font()

# 创建保存图表的目录
save_dir = "D:/aaaabysj/network_monitor最终 - 副本/chart"
os.makedirs(save_dir, exist_ok=True)

# 设置Seaborn风格
sns.set(style="whitegrid")

# 数据
models = ['SimpleModel', 'CNNLSTMModel', 'ResCNN-ABLGAN']
stages = ['数据捕获', '特征提取', '模型推理', '结果处理']

# 各阶段延迟数据 (ms)
latency_data = {
    'SimpleModel': [0.8, 1.5, 0.8, 0.5],
    'CNNLSTMModel': [0.8, 1.5, 2.1, 0.6],
    'ResCNN-ABLGAN': [0.8, 1.5, 4.5, 0.7]
}

# 计算累积延迟
cumulative_latency = {}
for model in models:
    cumulative_latency[model] = np.cumsum(latency_data[model])

# 创建图表
plt.figure(figsize=(12, 8))

# 设置颜色
colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']

# 创建堆叠条形图
bar_width = 0.6
x = np.arange(len(models))

bottom = np.zeros(len(models))
for i, stage in enumerate(stages):
    stage_latency = [latency_data[model][i] for model in models]
    plt.bar(x, stage_latency, bar_width, bottom=bottom, label=stage, color=colors[i])
    
    # 添加数值标签
    for j, v in enumerate(stage_latency):
        plt.text(j, bottom[j] + v/2, f'{v}ms', ha='center', va='center', fontsize=10, color='white', fontweight='bold')
    
    bottom += stage_latency

# 添加总延迟标签
for i, model in enumerate(models):
    total_latency = sum(latency_data[model])
    plt.text(i, total_latency + 0.3, f'总计: {total_latency}ms', ha='center', va='bottom', fontsize=12, fontweight='bold')

# 设置坐标轴
plt.ylabel('延迟 (ms)', fontsize=14)
plt.xticks(x, models, fontsize=12)
plt.yticks(fontsize=12)
plt.ylim(0, 10)  # 调整Y轴范围

# 添加标题
plt.title('图5-8 不同模型的端到端检测延迟分析', fontsize=16, fontweight='bold', pad=20)
plt.suptitle('各处理阶段的延迟贡献', fontsize=14, y=0.95)

# 添加图例
plt.legend(loc='upper left', fontsize=12)

# 添加网格线
plt.grid(axis='y', linestyle='--', alpha=0.7)

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig(os.path.join(save_dir, "detection_latency.png"), dpi=300, bbox_inches='tight')
plt.close()

print("检测延迟分析图已生成并保存。")
