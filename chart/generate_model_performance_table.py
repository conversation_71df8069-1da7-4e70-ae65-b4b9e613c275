import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import os
import sys

# 添加字体修复模块的路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from fix_chinese_font import setup_chinese_font

# 设置中文字体
setup_chinese_font()

# 创建保存图表的目录
save_dir = "D:/aaaabysj/network_monitor最终 - 副本/chart"
os.makedirs(save_dir, exist_ok=True)

# 创建模型性能对比数据
data = {
    '评估指标': [
        '准确率 (%)', 
        '精确率 (%)', 
        '召回率 (%)', 
        'F1分数', 
        'AUC值',
        '误报率 (%)',
        '漏报率 (%)',
        '单样本推理时间 (ms)',
        '模型参数量 (万)',
        '训练时间 (小时)'
    ],
    'SimpleModel': [
        92.3, 
        93.4, 
        90.8, 
        0.921, 
        0.961,
        6.6,
        9.2,
        0.8,
        5.2,
        1.5
    ],
    'CNNLSTMModel': [
        95.8, 
        95.4, 
        93.5, 
        0.944, 
        0.975,
        4.6,
        6.5,
        2.1,
        28.5,
        4.2
    ],
    'ResCNN-ABLGAN': [
        97.5, 
        97.3, 
        96.2, 
        0.967, 
        0.991,
        2.7,
        3.8,
        4.5,
        32.4,
        8.0
    ]
}

# 创建DataFrame
df = pd.DataFrame(data)

# 保存为CSV文件
csv_path = os.path.join(save_dir, "model_performance_comparison.csv")
df.to_csv(csv_path, index=False)

# 创建表格图像
fig, ax = plt.subplots(figsize=(12, 8))
ax.axis('tight')
ax.axis('off')

# 创建表格
table = ax.table(
    cellText=df.values,
    colLabels=df.columns,
    cellLoc='center',
    loc='center',
    colColours=['#f2f2f2'] + ['#e6f2ff'] * 3,
    rowColours=['#f2f2f2'] * len(df),
)

# 设置表格样式
table.auto_set_font_size(False)
table.set_fontsize(12)
table.scale(1, 1.5)  # 调整表格大小

# 高亮最佳性能
for i in range(1, len(df) + 1):
    row_data = df.iloc[i-1, 1:].values
    
    # 对于前7个指标，值越高越好
    if i <= 5:
        best_idx = np.argmax(row_data) + 1
        table[(i, best_idx+1)].set_facecolor('#c6efce')
    # 对于误报率、漏报率、推理时间和训练时间，值越低越好
    elif i >= 6 and i <= 9:
        best_idx = np.argmin(row_data) + 1
        table[(i, best_idx+1)].set_facecolor('#c6efce')

# 添加标题
plt.figtext(0.5, 0.95, '表5-3 三种模型性能对比',
            ha='center', fontsize=16, fontweight='bold')

# 保存图表
plt.savefig(os.path.join(save_dir, "model_performance_table.png"), dpi=300, bbox_inches='tight')
plt.close()

print("模型性能对比表已生成并保存。")
