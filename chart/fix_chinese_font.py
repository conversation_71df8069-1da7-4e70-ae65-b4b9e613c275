import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import sys
import subprocess
import platform

def setup_chinese_font():
    """设置中文字体支持"""
    # 检查操作系统
    system = platform.system()
    
    # 检查是否已有中文字体
    fonts = [f.name for f in fm.fontManager.ttflist]
    chinese_fonts = [f for f in fonts if '黑体' in f or 'SimHei' in f or '宋体' in f or 'SimSun' in f or 'Microsoft YaHei' in f]
    
    if chinese_fonts:
        print(f"系统已有中文字体: {', '.join(chinese_fonts)}")
        # 使用找到的第一个中文字体
        plt.rcParams['font.sans-serif'] = [chinese_fonts[0]] + plt.rcParams['font.sans-serif']
        plt.rcParams['axes.unicode_minus'] = False
        return True
    
    # 如果没有找到中文字体，尝试安装
    print("未找到中文字体，尝试安装...")
    
    if system == 'Windows':
        # 在Windows上，尝试使用系统自带的字体
        font_paths = [
            'C:/Windows/Fonts/simhei.ttf',  # 黑体
            'C:/Windows/Fonts/simsun.ttc',  # 宋体
            'C:/Windows/Fonts/msyh.ttc'     # 微软雅黑
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                # 创建matplotlib字体配置目录
                font_dir = os.path.join(os.path.expanduser('~'), '.matplotlib', 'fonts', 'ttf')
                os.makedirs(font_dir, exist_ok=True)
                
                # 复制字体文件
                target_path = os.path.join(font_dir, os.path.basename(font_path))
                if not os.path.exists(target_path):
                    import shutil
                    shutil.copy(font_path, target_path)
                    print(f"已复制字体文件: {font_path} -> {target_path}")
                
                # 清除matplotlib字体缓存
                cache_dir = os.path.join(os.path.expanduser('~'), '.matplotlib')
                for cache_file in ['fontList.json', 'fontList.py3k.json', 'fontList.cache']:
                    cache_path = os.path.join(cache_dir, cache_file)
                    if os.path.exists(cache_path):
                        os.remove(cache_path)
                        print(f"已删除字体缓存: {cache_path}")
                
                # 设置字体
                font_name = 'SimHei' if 'simhei' in font_path.lower() else 'SimSun' if 'simsun' in font_path.lower() else 'Microsoft YaHei'
                plt.rcParams['font.sans-serif'] = [font_name] + plt.rcParams['font.sans-serif']
                plt.rcParams['axes.unicode_minus'] = False
                
                print(f"已设置中文字体: {font_name}")
                return True
    
    # 如果以上方法都失败，尝试使用无衬线字体并警告用户
    print("警告: 未能设置中文字体，图表中的中文可能无法正确显示")
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans'] + plt.rcParams['font.sans-serif']
    plt.rcParams['axes.unicode_minus'] = False
    return False

if __name__ == "__main__":
    setup_chinese_font()
    
    # 测试中文显示
    plt.figure(figsize=(8, 6))
    plt.title('中文显示测试')
    plt.xlabel('横坐标')
    plt.ylabel('纵坐标')
    plt.plot([1, 2, 3, 4], [1, 4, 9, 16], label='测试数据')
    plt.legend()
    plt.grid(True)
    
    # 保存测试图
    test_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'chinese_font_test.png')
    plt.savefig(test_path, dpi=300)
    plt.close()
    
    print(f"测试图片已保存至: {test_path}")
    print("如果测试图片中的中文显示正常，则字体设置成功")
