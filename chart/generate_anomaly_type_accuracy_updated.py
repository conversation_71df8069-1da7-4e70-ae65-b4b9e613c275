import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import os

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 创建保存图表的目录
save_dir = "D:/aaaabysj/network_monitor最终 - 副本/chart"
os.makedirs(save_dir, exist_ok=True)

# 设置Seaborn风格
sns.set(style="whitegrid")

# 数据 - 只使用公开数据集中的异常类型
anomaly_types = [
    'DNS隧道',
    'ICMP隧道',
    'SSH隧道',
    'SSL/TLS隧道',
    '基于DNS的C2',
    '基于HTTPS的C2',
    '基于WebSocket的C2',
    '自定义协议隧道'
]

accuracies = [
    99.1,
    98.7,
    98.2,
    97.5,
    97.9,
    96.8,
    95.7,
    95.3
]

# 对数据按准确率从高到低排序
sorted_indices = np.argsort(accuracies)[::-1]
anomaly_types = [anomaly_types[i] for i in sorted_indices]
accuracies = [accuracies[i] for i in sorted_indices]

# 为不同类别的异常设置不同颜色
colors = []
for anomaly in anomaly_types:
    if '隧道' in anomaly:
        colors.append('#1f77b4')  # 蓝色 - 隧道类
    elif 'C2' in anomaly:
        colors.append('#ff7f0e')  # 橙色 - C2类
    else:
        colors.append('#2ca02c')  # 绿色 - 其他类

# 创建图表
plt.figure(figsize=(12, 8))

# 创建水平条形图
bars = plt.barh(anomaly_types, accuracies, color=colors, height=0.6)

# 添加数值标签
for i, bar in enumerate(bars):
    width = bar.get_width()
    plt.text(width + 0.1, bar.get_y() + bar.get_height()/2, f'{width}%',
            ha='left', va='center', fontsize=11, fontweight='bold')

# 设置坐标轴
plt.xlim(94, 100.5)  # 修正X轴范围
plt.xlabel('准确率 (%)', fontsize=14)
plt.yticks(fontsize=12)
plt.xticks(fontsize=12)

# 添加标题
plt.title('图5-2 不同异常类型的检测准确率', fontsize=16, fontweight='bold', pad=20)
plt.suptitle('ResCNN-ABLGAN模型对各类异常的检测能力', fontsize=14, y=0.95)

# 添加图例
from matplotlib.patches import Patch
legend_elements = [
    Patch(facecolor='#1f77b4', label='隧道类'),
    Patch(facecolor='#ff7f0e', label='命令控制类')
]
plt.legend(handles=legend_elements, loc='lower right', fontsize=12)

# 添加网格线
plt.grid(axis='x', linestyle='--', alpha=0.7)

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig(os.path.join(save_dir, "anomaly_type_accuracy.png"), dpi=300, bbox_inches='tight')
plt.close()

print("不同异常类型的检测准确率图表已生成并保存。")
