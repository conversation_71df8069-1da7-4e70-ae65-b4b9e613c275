import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import os

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 创建保存图表的目录
save_dir = "D:/aaaabysj/network_monitor最终 - 副本/chart"
os.makedirs(save_dir, exist_ok=True)

# 设置Seaborn风格
sns.set(style="whitegrid")

# 数据
thresholds = [0.3, 0.4, 0.5, 0.6, 0.7]
precision = [92.1, 94.8, 97.3, 98.6, 99.1]
recall = [98.7, 97.5, 96.2, 94.5, 92.3]
f1_score = [95.3, 96.1, 96.7, 96.5, 95.6]

# 创建图表
plt.figure(figsize=(10, 8))

# 绘制折线图
plt.plot(thresholds, precision, 'o-', color='#1f77b4', linewidth=3, markersize=10, label='精确率')
plt.plot(thresholds, recall, 's-', color='#ff7f0e', linewidth=3, markersize=10, label='召回率')
plt.plot(thresholds, f1_score, '^-', color='#2ca02c', linewidth=3, markersize=10, label='F1分数')

# 添加数值标签
for i, (p, r, f) in enumerate(zip(precision, recall, f1_score)):
    plt.text(thresholds[i], p + 0.5, f'{p}%', ha='center', va='bottom', fontsize=11, color='#1f77b4', fontweight='bold')
    plt.text(thresholds[i], r + 0.5, f'{r}%', ha='center', va='bottom', fontsize=11, color='#ff7f0e', fontweight='bold')
    plt.text(thresholds[i], f - 0.7, f'{f}%', ha='center', va='bottom', fontsize=11, color='#2ca02c', fontweight='bold')

# 设置坐标轴
plt.xlim(0.25, 0.75)
plt.ylim(90, 100)  # 修正Y轴范围
plt.xlabel('阈值', fontsize=14)
plt.ylabel('百分比 (%)', fontsize=14)
plt.xticks(thresholds, fontsize=12)
plt.yticks(fontsize=12)

# 添加标题
plt.title('图5-5 不同阈值下的精确率和召回率变化', fontsize=16, fontweight='bold', pad=20)
plt.suptitle('阈值选择对模型性能的影响', fontsize=14, y=0.95)

# 添加图例
plt.legend(loc='lower right', fontsize=12)

# 添加网格线
plt.grid(linestyle='--', alpha=0.7)

# 添加最佳阈值标记
plt.axvline(x=0.5, color='#d62728', linestyle='--', alpha=0.7, linewidth=2)
plt.text(0.5, 91, '最佳阈值', ha='center', va='bottom', fontsize=12, color='#d62728', fontweight='bold')

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig(os.path.join(save_dir, "threshold_analysis.png"), dpi=300, bbox_inches='tight')
plt.close()

print("不同阈值下的精确率和召回率变化图表已生成并保存。")
