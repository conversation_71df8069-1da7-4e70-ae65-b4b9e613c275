import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import os
import sys

# 添加字体修复模块的路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from fix_chinese_font import setup_chinese_font

# 设置中文字体
setup_chinese_font()

# 创建保存图表的目录
save_dir = "D:/aaaabysj/network_monitor最终 - 副本/chart"
os.makedirs(save_dir, exist_ok=True)

# 设置Seaborn风格
sns.set(style="white")

# 定义类别
categories = [
    '正常流量',
    'SSH隧道',
    'SSL/TLS隧道',
    'DNS隧道',
    'ICMP隧道',
    '基于HTTPS的C2',
    '基于DNS的C2'
]

# 创建混淆矩阵数据 (行为真实类别，列为预测类别)
confusion_matrix = np.array([
    [985, 3, 5, 2, 1, 3, 1],   # 正常流量
    [4, 982, 8, 2, 1, 2, 1],   # SSH隧道
    [6, 5, 975, 3, 2, 8, 1],   # SSL/TLS隧道
    [2, 1, 2, 991, 1, 1, 2],   # DNS隧道
    [1, 2, 1, 3, 987, 2, 4],   # ICMP隧道
    [5, 2, 9, 1, 2, 978, 3],   # 基于HTTPS的C2
    [2, 1, 1, 5, 3, 2, 986]    # 基于DNS的C2
])

# 计算准确率
accuracy = np.trace(confusion_matrix) / np.sum(confusion_matrix)

# 创建图表
plt.figure(figsize=(10, 8))

# 绘制热力图
sns.heatmap(confusion_matrix, annot=True, fmt='d', cmap='Blues',
            xticklabels=categories, yticklabels=categories)

# 设置坐标轴
plt.xlabel('预测类别', fontsize=14)
plt.ylabel('真实类别', fontsize=14)
plt.xticks(rotation=45, ha='right', fontsize=11)
plt.yticks(fontsize=11)

# 添加标题
plt.title('图5-9 ResCNN-ABLGAN模型的混淆矩阵', fontsize=16, fontweight='bold', pad=20)
plt.figtext(0.5, 0.01, f'总体准确率: {accuracy:.4f}', ha='center', fontsize=12)

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig(os.path.join(save_dir, "confusion_matrix.png"), dpi=300, bbox_inches='tight')
plt.close()

print("混淆矩阵可视化已生成并保存。")
