import matplotlib.pyplot as plt
import matplotlib.patches as patches
import os
import sys

# 添加字体修复模块的路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from fix_chinese_font import setup_chinese_font

# 设置中文字体
setup_chinese_font()

# 创建保存图表的目录
save_dir = "D:/aaaabysj/network_monitor最终 - 副本/chart"
os.makedirs(save_dir, exist_ok=True)

# 创建图表
fig, ax = plt.figure(figsize=(12, 10)), plt.gca()
ax.axis('off')  # 关闭坐标轴

# 定义颜色
colors = {
    'data': '#3498db',      # 蓝色
    'preprocess': '#2ecc71', # 绿色
    'model': '#e74c3c',     # 红色
    'output': '#f39c12',    # 橙色
    'storage': '#9b59b6'    # 紫色
}

# 定义层级和组件
layers = [
    {'name': '数据采集层', 'color': colors['data'], 'y': 0.9, 'height': 0.15},
    {'name': '预处理层', 'color': colors['preprocess'], 'y': 0.7, 'height': 0.15},
    {'name': '分析引擎层', 'color': colors['model'], 'y': 0.5, 'height': 0.15},
    {'name': '存储层', 'color': colors['storage'], 'y': 0.3, 'height': 0.15},
    {'name': '展示交互层', 'color': colors['output'], 'y': 0.1, 'height': 0.15}
]

components = [
    # 数据采集层组件
    {'name': '网络接口捕获', 'layer': 0, 'x': 0.2, 'width': 0.15},
    {'name': 'PCAP文件解析', 'layer': 0, 'x': 0.4, 'width': 0.15},
    {'name': '流量镜像设备', 'layer': 0, 'x': 0.6, 'width': 0.15},
    {'name': '第三方数据源', 'layer': 0, 'x': 0.8, 'width': 0.15},
    
    # 预处理层组件
    {'name': '流量捕获模块', 'layer': 1, 'x': 0.2, 'width': 0.15},
    {'name': '会话重组模块', 'layer': 1, 'x': 0.4, 'width': 0.15},
    {'name': '特征提取模块', 'layer': 1, 'x': 0.6, 'width': 0.15},
    {'name': '数据清洗模块', 'layer': 1, 'x': 0.8, 'width': 0.15},
    
    # 分析引擎层组件
    {'name': 'ResCNN-ABLGAN', 'layer': 2, 'x': 0.2, 'width': 0.15},
    {'name': '多任务学习框架', 'layer': 2, 'x': 0.4, 'width': 0.15},
    {'name': '增量学习模块', 'layer': 2, 'x': 0.6, 'width': 0.15},
    {'name': '异常检测引擎', 'layer': 2, 'x': 0.8, 'width': 0.15},
    
    # 存储层组件
    {'name': '时序数据库', 'layer': 3, 'x': 0.2, 'width': 0.15},
    {'name': '特征存储', 'layer': 3, 'x': 0.4, 'width': 0.15},
    {'name': '模型存储', 'layer': 3, 'x': 0.6, 'width': 0.15},
    {'name': '告警数据库', 'layer': 3, 'x': 0.8, 'width': 0.15},
    
    # 展示交互层组件
    {'name': '实时监控界面', 'layer': 4, 'x': 0.2, 'width': 0.15},
    {'name': '异常检测界面', 'layer': 4, 'x': 0.4, 'width': 0.15},
    {'name': '分析报告界面', 'layer': 4, 'x': 0.6, 'width': 0.15},
    {'name': '配置管理界面', 'layer': 4, 'x': 0.8, 'width': 0.15}
]

# 绘制层级
for layer in layers:
    rect = patches.Rectangle(
        (0.05, layer['y']), 0.9, layer['height'],
        linewidth=2, edgecolor='black', facecolor=layer['color'], alpha=0.3
    )
    ax.add_patch(rect)
    ax.text(0.5, layer['y'] + layer['height']/2, layer['name'],
            ha='center', va='center', fontsize=14, fontweight='bold')

# 绘制组件
for comp in components:
    layer = layers[comp['layer']]
    rect = patches.Rectangle(
        (comp['x'], layer['y'] + 0.02), comp['width'], layer['height'] - 0.04,
        linewidth=1, edgecolor='black', facecolor=layer['color'], alpha=0.7
    )
    ax.add_patch(rect)
    ax.text(comp['x'] + comp['width']/2, layer['y'] + layer['height']/2, comp['name'],
            ha='center', va='center', fontsize=10)

# 绘制层级间的连接
for i in range(len(layers) - 1):
    # 绘制中央连接线
    ax.arrow(0.5, layers[i]['y'], 0, -0.05, head_width=0.02, head_length=0.01,
             fc='black', ec='black', length_includes_head=True)

# 添加标题
plt.figtext(0.5, 0.97, '图3-1 基于深度学习的加密流量异常检测系统架构',
            ha='center', fontsize=16, fontweight='bold')

# 保存图表
plt.savefig(os.path.join(save_dir, "system_architecture.png"), dpi=300, bbox_inches='tight')
plt.close()

print("系统架构图已生成并保存。")
