import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import os

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 创建保存图表的目录
save_dir = "D:/aaaabysj/network_monitor最终 - 副本/chart"
os.makedirs(save_dir, exist_ok=True)

# 设置Seaborn风格
sns.set(style="whitegrid")

# 数据 - 只使用公开数据集
datasets = [
    'ISCX\nVPN-nonVPN',
    'USTC-\nTFC2016',
    'CIC-\nIDS2017',
    'CICIDS\n2018'
]

# 只使用三个模型的准确率数据
rescnn_ablgan = [98.7, 97.5, 98.2, 96.9]
cnnlstm_model = [95.8, 94.6, 95.3, 93.8]
simple_model = [93.5, 92.3, 93.0, 91.2]

# 设置图表大小
plt.figure(figsize=(12, 8))

# 设置柱状图的宽度和位置
bar_width = 0.25
r1 = np.arange(len(datasets))
r2 = [x + bar_width for x in r1]
r3 = [x + bar_width for x in r2]

# 创建分组柱状图
plt.bar(r1, rescnn_ablgan, color='#1f77b4', width=bar_width, edgecolor='grey', label='ResCNN-ABLGAN')
plt.bar(r2, cnnlstm_model, color='#2ca02c', width=bar_width, edgecolor='grey', label='CNNLSTMModel')
plt.bar(r3, simple_model, color='#d62728', width=bar_width, edgecolor='grey', label='SimpleModel')

# 添加数值标签
def add_labels(positions, values, height_adjustment=0.5):
    for i, (pos, val) in enumerate(zip(positions, values)):
        plt.text(pos, val + height_adjustment, f'{val}%', ha='center', va='bottom', fontsize=10, rotation=0)

add_labels(r1, rescnn_ablgan)
add_labels(r2, cnnlstm_model)
add_labels(r3, simple_model)

# 设置坐标轴
plt.xlabel('数据集', fontsize=14, labelpad=10)
plt.ylabel('准确率 (%)', fontsize=14)
plt.ylim(90, 102)  # 修正Y轴范围
plt.xticks([r + bar_width for r in range(len(datasets))], datasets, fontsize=12)
plt.yticks(fontsize=12)

# 添加标题
plt.title('图5-1 不同数据集上的检测准确率对比', fontsize=16, fontweight='bold', pad=20)
plt.suptitle('三种模型在公开数据集上的性能对比', fontsize=14, y=0.95)

# 添加图例
plt.legend(loc='lower left', fontsize=12)

# 添加网格线
plt.grid(axis='y', linestyle='--', alpha=0.7)

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig(os.path.join(save_dir, "dataset_accuracy_comparison.png"), dpi=300, bbox_inches='tight')
plt.close()

print("不同数据集上的检测准确率对比图表已生成并保存。")
