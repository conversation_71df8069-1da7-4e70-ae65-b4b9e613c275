import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import os
import sys

# 添加字体修复模块的路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from fix_chinese_font import setup_chinese_font

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 使用微软雅黑
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 创建保存图表的目录
save_dir = "D:/aaaabysj/network_monitor最终 - 副本/chart"
os.makedirs(save_dir, exist_ok=True)

# 设置Seaborn风格
sns.set(style="whitegrid")

# 创建训练数据
epochs = np.arange(1, 101)

# 训练损失和验证损失
train_loss = 0.8 * np.exp(-0.03 * epochs) + 0.2 + 0.05 * np.sin(epochs / 5) * np.exp(-0.03 * epochs)
val_loss = 0.85 * np.exp(-0.025 * epochs) + 0.25 + 0.07 * np.sin(epochs / 5) * np.exp(-0.025 * epochs)

# 训练准确率和验证准确率
train_acc = 1 - 0.7 * np.exp(-0.04 * epochs) - 0.03 * np.sin(epochs / 5) * np.exp(-0.03 * epochs)
val_acc = 1 - 0.75 * np.exp(-0.035 * epochs) - 0.04 * np.sin(epochs / 5) * np.exp(-0.025 * epochs)

# 学习率
initial_lr = 0.001
lr = initial_lr * np.ones_like(epochs)

# 预热阶段
warmup_epochs = 5
for i in range(warmup_epochs):
    lr[i] = initial_lr * (i + 1) / warmup_epochs

# 余弦退火
for i in range(warmup_epochs, len(epochs)):
    lr[i] = initial_lr * 0.5 * (1 + np.cos(np.pi * (i - warmup_epochs) / (len(epochs) - warmup_epochs)))

# 创建图表
fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 12), sharex=True, gridspec_kw={'height_ratios': [2, 2, 1]})

# 绘制损失曲线
ax1.plot(epochs, train_loss, 'b-', label='训练损失', linewidth=2)
ax1.plot(epochs, val_loss, 'r-', label='验证损失', linewidth=2)
ax1.set_ylabel('损失值', fontsize=14)
ax1.set_ylim(0, 1.2)
ax1.legend(loc='upper right', fontsize=12)
ax1.grid(True, linestyle='--', alpha=0.7)

# 标记不同训练阶段
ax1.axvline(x=30, color='gray', linestyle='--', alpha=0.7)
ax1.axvline(x=70, color='gray', linestyle='--', alpha=0.7)
ax1.axvline(x=90, color='gray', linestyle='--', alpha=0.7)
ax1.text(15, 1.1, '特征提取网络预训练', ha='center', fontsize=10)
ax1.text(50, 1.1, '多任务学习训练', ha='center', fontsize=10)
ax1.text(80, 1.1, 'GAN对抗训练', ha='center', fontsize=10)
ax1.text(95, 1.1, '联合微调', ha='center', fontsize=10)

# 绘制准确率曲线
ax2.plot(epochs, train_acc * 100, 'b-', label='训练准确率', linewidth=2)
ax2.plot(epochs, val_acc * 100, 'r-', label='验证准确率', linewidth=2)
ax2.set_ylabel('准确率 (%)', fontsize=14)
ax2.set_ylim(50, 100)
ax2.legend(loc='lower right', fontsize=12)
ax2.grid(True, linestyle='--', alpha=0.7)

# 标记不同训练阶段
ax2.axvline(x=30, color='gray', linestyle='--', alpha=0.7)
ax2.axvline(x=70, color='gray', linestyle='--', alpha=0.7)
ax2.axvline(x=90, color='gray', linestyle='--', alpha=0.7)

# 绘制学习率曲线
ax3.plot(epochs, lr, 'g-', label='学习率', linewidth=2)
ax3.set_xlabel('训练轮次 (Epoch)', fontsize=14)
ax3.set_ylabel('学习率', fontsize=14)
ax3.set_ylim(0, 0.0012)
ax3.legend(loc='upper right', fontsize=12)
ax3.grid(True, linestyle='--', alpha=0.7)

# 标记不同训练阶段
ax3.axvline(x=30, color='gray', linestyle='--', alpha=0.7)
ax3.axvline(x=70, color='gray', linestyle='--', alpha=0.7)
ax3.axvline(x=90, color='gray', linestyle='--', alpha=0.7)

# 添加标题
fig.suptitle('图4-3 ResCNN-ABLGAN模型训练过程监控', fontsize=16, fontweight='bold', y=0.98)

# 调整布局
plt.tight_layout()
plt.subplots_adjust(top=0.93)

# 保存图表
plt.savefig(os.path.join(save_dir, "training_process.png"), dpi=300, bbox_inches='tight')
plt.close()

print("模型训练过程监控图已生成并保存。")
