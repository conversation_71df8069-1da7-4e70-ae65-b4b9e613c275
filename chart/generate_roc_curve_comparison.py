import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import os

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 创建保存图表的目录
save_dir = "D:/aaaabysj/network_monitor最终 - 副本/chart"
os.makedirs(save_dir, exist_ok=True)

# 设置Seaborn风格
sns.set(style="whitegrid")

# 模型和对应的AUC值
models = [
    'ResCNN-ABLGAN',
    'Attention-LSTM',
    'CNN-LSTM',
    '普通CNN',
    '普通LSTM',
    'XGBoost',
    '随机森林',
    '支持向量机'
]

auc_values = [0.991, 0.981, 0.975, 0.967, 0.963, 0.961, 0.953, 0.942]

# 为每个模型生成ROC曲线数据
# 这里我们使用模拟数据，实际应用中应使用真实的ROC曲线数据
fpr_points = np.linspace(0, 1, 100)
tpr_curves = []

# 生成模拟的ROC曲线数据
for auc in auc_values:
    # 使用贝塔分布生成不同形状的ROC曲线
    # 调整参数使曲线形状与AUC值匹配
    a = 0.5
    b = 5 * (1 - auc) + 0.5
    tpr = 1 - np.power(1 - fpr_points, a) / np.power(1 - fpr_points, a) + np.power(fpr_points, b)
    # 使用更简单的方法生成ROC曲线
    tpr = np.power(fpr_points, 1/(10*auc - 9))
    # 确保tpr在合理范围内
    tpr = np.clip(tpr, 0, 1)
    tpr_curves.append(tpr)

# 创建图表
plt.figure(figsize=(12, 10))

# 设置颜色和线型
colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
line_styles = ['-', '--', '-.', ':', '-', '--', '-.', ':']
line_widths = [3, 2.5, 2.5, 2, 2, 2, 1.5, 1.5]

# 绘制ROC曲线
for i, (model, tpr, auc) in enumerate(zip(models, tpr_curves, auc_values)):
    plt.plot(fpr_points, tpr, label=f'{model} (AUC={auc:.3f})', 
             color=colors[i], linestyle=line_styles[i], linewidth=line_widths[i])

# 绘制随机猜测的基准线
plt.plot([0, 1], [0, 1], 'k--', label='随机猜测 (AUC=0.5)', linewidth=1.5, alpha=0.7)

# 设置坐标轴
plt.xlim(0, 1)
plt.ylim(0, 1.05)
plt.xlabel('假正例率 (False Positive Rate)', fontsize=14)
plt.ylabel('真正例率 (True Positive Rate)', fontsize=14)
plt.xticks(fontsize=12)
plt.yticks(fontsize=12)

# 添加标题
plt.title('图5-6 ROC曲线对比', fontsize=16, fontweight='bold', pad=20)
plt.suptitle('不同模型的ROC曲线和AUC值对比', fontsize=14, y=0.95)

# 添加图例
plt.legend(loc='lower right', fontsize=11, frameon=True, framealpha=0.9)

# 添加网格线
plt.grid(linestyle='--', alpha=0.7)

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig(os.path.join(save_dir, "roc_curve_comparison.png"), dpi=300, bbox_inches='tight')
plt.close()

print("ROC曲线对比图表已生成并保存。")
