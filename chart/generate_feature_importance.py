import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import os
import sys

# 添加字体修复模块的路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from fix_chinese_font import setup_chinese_font

# 设置中文字体
setup_chinese_font()

# 创建保存图表的目录
save_dir = "D:/aaaabysj/network_monitor最终 - 副本/chart"
os.makedirs(save_dir, exist_ok=True)

# 设置Seaborn风格
sns.set(style="whitegrid")

# 特征重要性数据
features = [
    '数据包大小统计',
    '数据包间隔时间',
    '流持续时间',
    '流量突发性',
    'TLS握手特征',
    '证书链特征',
    '密码套件选择',
    '流量方向性',
    '数据包长度序列',
    '协议标志位统计'
]

# 特征重要性分数
importance_scores = [0.185, 0.152, 0.138, 0.125, 0.112, 0.095, 0.078, 0.052, 0.035, 0.028]

# 特征类别
feature_categories = [
    '统计特征',
    '统计特征',
    '统计特征',
    '时序特征',
    '协议特征',
    '协议特征',
    '协议特征',
    '统计特征',
    '时序特征',
    '统计特征'
]

# 为不同类别设置不同颜色
colors = []
for category in feature_categories:
    if category == '统计特征':
        colors.append('#1f77b4')  # 蓝色
    elif category == '时序特征':
        colors.append('#ff7f0e')  # 橙色
    else:  # 协议特征
        colors.append('#2ca02c')  # 绿色

# 对特征按重要性排序
sorted_indices = np.argsort(importance_scores)
features = [features[i] for i in sorted_indices]
importance_scores = [importance_scores[i] for i in sorted_indices]
colors = [colors[i] for i in sorted_indices]
feature_categories = [feature_categories[i] for i in sorted_indices]

# 创建图表
plt.figure(figsize=(12, 8))

# 创建水平条形图
bars = plt.barh(features, importance_scores, color=colors, height=0.6)

# 添加数值标签
for i, bar in enumerate(bars):
    width = bar.get_width()
    plt.text(width + 0.005, bar.get_y() + bar.get_height()/2, f'{width:.3f}',
            ha='left', va='center', fontsize=10, fontweight='bold')

# 设置坐标轴
plt.xlim(0, 0.25)
plt.xlabel('特征重要性分数', fontsize=14)
plt.yticks(fontsize=12)
plt.xticks(fontsize=12)

# 添加标题
plt.title('图5-7 特征重要性分析', fontsize=16, fontweight='bold', pad=20)
plt.suptitle('ResCNN-ABLGAN模型中各特征的重要性排序', fontsize=14, y=0.95)

# 添加图例
from matplotlib.patches import Patch
legend_elements = [
    Patch(facecolor='#1f77b4', label='统计特征'),
    Patch(facecolor='#ff7f0e', label='时序特征'),
    Patch(facecolor='#2ca02c', label='协议特征')
]
plt.legend(handles=legend_elements, loc='lower right', fontsize=12)

# 添加网格线
plt.grid(axis='x', linestyle='--', alpha=0.7)

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig(os.path.join(save_dir, "feature_importance.png"), dpi=300, bbox_inches='tight')
plt.close()

print("特征重要性分析图已生成并保存。")
