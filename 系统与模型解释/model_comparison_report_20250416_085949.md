# 模型性能比较报告 - 20250416_085949

## 总体性能指标

| model_type   |   parameters |   trainable_params |   model_size_mb |   layer_count |   complexity_score |   training_time |   avg_inference_time_ms |   accuracy |   precision |   recall |   f1_score |      auc |   flops_m |
|:-------------|-------------:|-------------------:|----------------:|--------------:|-------------------:|----------------:|------------------------:|-----------:|------------:|---------:|-----------:|---------:|----------:|
| SimpleModel  |        52322 |              52322 |        0.199593 |             3 |            1.41561 |         3.10688 |                 4.28431 |   0.546667 |    0.553846 | 0.586319 |   0.56962  | 0.561962 |  0.016512 |
| CNNLSTMModel |       633410 |             633410 |        2.41627  |             5 |            2.90084 |       183.736   |               346.054   |   0.51     |    0.52968  | 0.37785  |   0.441065 | 0.520066 |  2.04442  |
| ResCNNABLGAN |      1245426 |            1245426 |        4.75092  |            27 |           16.4574  |       558.029   |               615.357   |   0.511667 |    0.511667 | 1        |   0.676957 | 0.491879 |  6.88349  |

## 最佳模型

- **准确率**: SimpleModel (0.5467)
- **精确率**: SimpleModel (0.5538)
- **召回率**: ResCNNABLGAN (1.0000)
- **F1分数**: ResCNNABLGAN (0.6770)
- **AUC**: SimpleModel (0.5620)
- **训练时间**: SimpleModel (3.1069)
- **推理时间**: SimpleModel (4.2843)
- **模型大小**: SimpleModel (0.1996)
- **复杂度分数**: SimpleModel (1.4156)

## 结论

1. **SimpleModel** 模型在准确率方面表现最佳
2. **SimpleModel** 模型具有最快的推理速度
3. **ResCNNABLGAN** 模型结构最复杂

## 应用建议

- **高安全性要求场景**: 选择准确率和F1分数最高的模型
- **资源受限场景**: 选择参数数量少、推理速度快的模型
- **实时处理场景**: 选择推理时间最短的模型
