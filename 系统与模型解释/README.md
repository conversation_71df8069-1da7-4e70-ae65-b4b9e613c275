# 深度学习模型性能比较

本目录包含用于比较 `app/utils/deep_learning_model.py` 中不同网络流量分析模型性能的脚本和结果。

## 比较的模型

1. **SimpleModel** - 基础LSTM模型，结构简单，参数较少
2. **CNNLSTMModel** - 标准CNN-LSTM模型，结合了卷积神经网络和长短期记忆网络
3. **ResCNNABLGAN** - 高级模型，包含残差连接、注意力机制和对抗学习

## 比较指标

### 性能指标
- **准确率 (Accuracy)** - 正确预测的比例
- **精确率 (Precision)** - 预测为异常的样本中实际为异常的比例
- **召回率 (Recall)** - 实际异常样本中被正确预测的比例
- **F1分数** - 精确率和召回率的调和平均
- **AUC** - ROC曲线下面积，表示模型区分能力

### 资源指标
- **训练时间** - 模型训练所需的时间
- **推理时间** - 模型进行预测所需的时间
- **参数数量** - 模型的参数总数
- **模型大小** - 模型占用的存储空间
- **计算量** - 模型的浮点运算数（MFLOPs）
- **复杂度分数** - 综合考虑层数和参数量的复杂度评分

## 数据源支持

比较工具支持以下三种数据源：

### 1. 合成数据
自动生成的数据集，支持三种模式：
- **simple**: 简单模式，基于特征乘积判断
- **complex**: 复杂模式，结合多个特征判断
- **nonlinear**: 非线性模式，使用非线性函数判断

### 2. CSV文件数据
支持加载一个或多个CSV文件：
- 可指定特征列和标签列
- 自动合并多个文件
- 自动分割训练集和测试集

### 3. PCAP文件数据
支持从网络流量捕获文件中提取特征：
- 自动提取包大小、协议类型、端口信息等特征
- 基于文件名自动判断标签（正常/异常）
- 计算负载熟等高级特征

## 使用方法

### 基本使用

```bash
# 使用合成数据
$ python bijiao/model_comparison.py

# 指定要比较的模型
$ python bijiao/model_comparison.py --models simple,cnnlstm,advanced

# 使用复杂数据模式
$ python bijiao/model_comparison.py --pattern complex --noise 0.2
```

### 使用CSV文件

```bash
# 加载一个或多个CSV文件
$ python bijiao/model_comparison.py --csv-files path/to/file1.csv path/to/file2.csv

# 指定特征列和标签列
$ python bijiao/model_comparison.py --csv-files data.csv --feature-cols feature1 feature2 --label-col target
```

### 使用PCAP文件

```bash
# 加载一个或多个PCAP文件
$ python bijiao/model_comparison.py --pcap-files normal.pcap attack.pcap
```

### 高级训练选项

```bash
# 使用对抗训练
$ python bijiao/model_comparison.py --adversarial

# 使用GAN联合训练
$ python bijiao/model_comparison.py --gan

# 调整训练参数
$ python bijiao/model_comparison.py --epochs 5 --batch-size 64
```

## 输出结果

比较工具会生成以下输出：

1. **CSV结果文件**: `bijiao/model_comparison_results_*.csv`
   - 包含所有性能指标的详细数据

2. **比较图表**: `bijiao/model_comparison_charts_*.png`
   - 六个维度的可视化比较图表

3. **ROC曲线图**: `bijiao/model_roc_curves_*.png`
   - 各模型的ROC曲线比较

4. **详细报告**: `bijiao/model_comparison_report_*.md`
   - Markdown格式的综合分析报告

## 结果分析

比较结果可以帮助我们了解：

1. 哪个模型在异常检测任务上表现最好
2. 模型复杂度与性能之间的权衡
3. 不同模型在资源受限环境中的适用性

通过这些比较，可以根据具体应用场景选择最合适的模型。

## 示例数据

`bijiao/example_data/` 目录包含示例数据文件和使用说明。您可以将自己的CSV或PCAP文件放在这个目录中，然后使用上述命令进行模型比较。
