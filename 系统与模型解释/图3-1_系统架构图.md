# 系统架构图

```mermaid
%%{init: { 'themeVariables': { 'fontFamily': 'SimSun, Times New Roman', 'fontSize': '10.5pt', 'lineColor': '#000000', 'textColor': '#000000' } }}%%
flowchart TD
    subgraph 数据采集层
        A[实时流量捕获模块] --> B[PCAP文件解析模块]
        B --> C[流量重组模块]
        C --> D[多线程处理]
        D --> E[缓冲机制]
    end
    subgraph 预处理层
        E --> F[协议识别模块]
        F --> G[数据清洗模块]
        G --> H[特征提取模块]
        H --> I[特征处理模块]
        I --> J[流水线处理]
        J --> K[特征缓存机制]
    end
    subgraph 分析检测层
        K --> L[ResCNN-ABLGAN模型]
        L --> M[多任务学习模块]
        M --> N[增量学习模块]
        N --> O[决策融合模块]
        O --> P[GPU加速]
        P --> Q[并行机制]
    end
    subgraph 应用服务层
        Q --> R[可视化展示模块]
        R --> S[告警管理模块]
        S --> T[报表生成模块]
        T --> U[API接口模块]
        U --> V[Web技术]
        V --> W[响应式设计]
    end
    subgraph 存储层
        W --> X[流量数据存储]
        X --> Y[模型存储]
        Y --> Z[检测结果存储]
        Z --> AA[系统日志存储]
        AA --> AB[分层存储策略]
        AB --> AC[数据压缩与加密]
    end
    A --> F
    AC --> F
    AC --> L
    AC --> R
    AC --> X
```