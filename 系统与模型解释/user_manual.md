# 网络流量分析系统用户使用手册

本手册详细介绍了网络流量分析系统的使用方法，帮助用户快速上手并充分利用系统功能进行网络流量监控和安全分析。

## 目录

1. [系统概述](#1-系统概述)
2. [界面导航](#2-界面导航)
3. [基本操作](#3-基本操作)
4. [高级功能](#4-高级功能)
5. [报告与可视化](#5-报告与可视化)
6. [系统设置](#6-系统设置)
7. [快捷键参考](#7-快捷键参考)

## 1. 系统概述

网络流量分析系统是一款专业的网络安全监控工具，能够实时捕获、分析网络流量，检测恶意活动和加密通信。系统集成了多种先进的检测算法和深度学习模型，为网络安全提供全面保障。

### 1.1 主要功能

- 实时网络流量捕获与分析
- 恶意流量检测与告警
- 加密流量识别与分类
- 网络异常行为监控
- 流量统计与可视化
- 安全事件报告生成

### 1.2 系统要求

- 操作系统：Windows 10/11, Linux, macOS
- 处理器：Intel Core i5 或同等性能处理器
- 内存：最低 8GB，推荐 16GB 或更高
- 存储空间：最低 10GB 可用空间
- 网络：支持监听模式的网络接口
- 权限：需要管理员/root权限运行核心功能

## 2. 界面导航

系统界面由以下主要部分组成：

### 2.1 主控制面板

![主控制面板](images/main_dashboard.png)

1. **菜单栏**：包含文件、编辑、视图、工具和帮助等菜单
2. **工具栏**：常用功能的快捷按钮
3. **状态栏**：显示系统状态、捕获统计和警报信息

### 2.2 流量监控视图

流量监控视图显示实时网络流量数据，包括：

- **流量图表**：显示网络流量的实时变化
- **数据包列表**：显示捕获的数据包详情
- **协议分布**：显示不同协议的流量比例
- **地理分布**：显示流量的地理来源分布

### 2.3 安全告警面板

安全告警面板显示检测到的安全事件，包括：

- 告警级别（高、中、低）
- 告警类型（恶意流量、异常行为等）
- 源IP和目标IP
- 时间戳和详细描述

### 2.4 分析工具面板

分析工具面板提供各种分析功能，包括：

- 数据包分析器
- 流量统计工具
- 协议分析器
- 异常检测配置

## 3. 基本操作

### 3.1 启动系统

1. 以管理员/root权限运行应用程序
2. 在启动界面选择要监控的网络接口
3. 点击"开始监控"按钮开始捕获流量

### 3.2 流量捕获

#### 3.2.1 开始捕获

1. 在主界面点击"捕获"菜单
2. 选择"开始捕获"或点击工具栏上的开始按钮
3. 系统将开始实时捕获网络流量

#### 3.2.2 停止捕获

1. 点击"捕获"菜单中的"停止捕获"
2. 或点击工具栏上的停止按钮

#### 3.2.3 设置捕获过滤器

1. 点击"捕获"菜单中的"捕获过滤器"
2. 在弹出的对话框中输入BPF过滤表达式
   ```
   例如：host *********** and port 80
   ```
3. 点击"应用"保存过滤器设置

### 3.3 查看数据包

1. 在数据包列表中双击任意数据包
2. 系统将显示数据包的详细信息，包括：
   - 数据包头部信息
   - 协议层次结构
   - 原始十六进制数据

### 3.4 保存捕获数据

1. 点击"文件"菜单中的"保存捕获文件"
2. 选择保存格式（PCAP、PCAPNG等）
3. 指定文件名和保存位置
4. 点击"保存"按钮

## 4. 高级功能

### 4.1 恶意流量检测

系统自动检测恶意流量，用户可以：

1. 在主界面点击"安全"菜单
2. 选择"恶意流量检测"
3. 在设置面板中：
   - 选择检测模型（SimpleModel、CNNLSTMModel或ResCNN-ABLGAN）
   - 调整检测灵敏度（低、中、高）
   - 配置告警阈值

### 4.2 加密流量分析

1. 在主界面点击"分析"菜单
2. 选择"加密流量分析"
3. 系统将显示检测到的加密流量，包括：
   - 加密协议类型
   - 加密强度评估
   - TLS握手详情（如适用）
   - JA3指纹信息

### 4.3 自定义检测规则

1. 点击"工具"菜单中的"规则编辑器"
2. 在规则编辑器中：
   - 点击"新建规则"创建自定义规则
   - 设置规则名称和描述
   - 定义匹配条件（协议、端口、内容等）
   - 设置触发动作（告警、记录、阻止等）
3. 点击"保存"应用新规则

### 4.4 流量回放

1. 点击"工具"菜单中的"流量回放"
2. 选择要回放的PCAP文件
3. 设置回放速度（1x、2x、5x等）
4. 点击"开始回放"按钮

## 5. 报告与可视化

### 5.1 生成报告

1. 点击"报告"菜单
2. 选择报告类型：
   - 安全事件摘要
   - 流量统计报告
   - 异常行为分析
   - 自定义报告
3. 设置报告时间范围
4. 点击"生成报告"按钮

### 5.2 可视化选项

系统提供多种可视化视图：

1. **时间序列图**：显示流量随时间的变化
   - 点击"视图"菜单中的"时间序列图"
   - 选择要显示的指标（数据包数、字节数等）

2. **协议分布图**：显示不同协议的流量比例
   - 点击"视图"菜单中的"协议分布"
   - 选择图表类型（饼图、柱状图等）

3. **连接图**：显示网络连接关系
   - 点击"视图"菜单中的"连接图"
   - 调整布局和过滤选项

4. **热力图**：显示活动强度分布
   - 点击"视图"菜单中的"热力图"
   - 选择映射指标和颜色方案

### 5.3 导出可视化

1. 在任意可视化视图中右键点击
2. 选择"导出"选项
3. 选择导出格式（PNG、PDF、SVG等）
4. 指定文件名和保存位置

## 6. 系统设置

### 6.1 常规设置

1. 点击"设置"菜单中的"选项"
2. 在"常规"选项卡中配置：
   - 界面语言
   - 启动选项
   - 自动保存设置
   - 数据存储位置

### 6.2 告警设置

1. 点击"设置"菜单中的"告警配置"
2. 配置告警方式：
   - 屏幕通知
   - 声音提醒
   - 邮件通知
   - 短信通知（如已配置）
3. 设置告警级别阈值

### 6.3 性能设置

1. 点击"设置"菜单中的"性能"
2. 配置系统资源使用：
   - 捕获缓冲区大小
   - 最大处理线程数
   - 数据保留策略
   - 内存使用限制

### 6.4 模型设置

1. 点击"设置"菜单中的"检测模型"
2. 选择要使用的模型：
   - SimpleModel：资源占用低，适合基本检测
   - CNNLSTMModel：平衡性能和资源占用
   - ResCNN-ABLGAN：高精度检测，资源占用高
3. 配置模型参数和阈值

## 7. 快捷键参考

| 功能 | 快捷键 |
|------|--------|
| 开始捕获 | Ctrl+B |
| 停止捕获 | Ctrl+E |
| 保存捕获文件 | Ctrl+S |
| 打开捕获文件 | Ctrl+O |
| 应用过滤器 | Ctrl+F |
| 清除过滤器 | Ctrl+R |
| 查看数据包详情 | Enter |
| 标记数据包 | M |
| 生成报告 | Ctrl+G |
| 显示统计信息 | Ctrl+I |
| 全屏模式 | F11 |
| 帮助 | F1 |

## 附录：常见问题解答

**问题**：系统无法捕获网络流量，该怎么办？

**解答**：请检查以下几点：
1. 确认您以管理员/root权限运行程序
2. 确认选择了正确的网络接口
3. 检查网络接口是否支持监听模式
4. 临时关闭防火墙或安全软件进行测试

**问题**：如何提高恶意流量检测的准确性？

**解答**：
1. 使用更高级的检测模型（ResCNN-ABLGAN）
2. 定期更新模型和规则库
3. 调整检测灵敏度，找到适合您网络环境的平衡点
4. 添加自定义规则针对特定威胁

**问题**：系统资源占用过高怎么办？

**解答**：
1. 在"性能设置"中降低捕获缓冲区大小
2. 减少处理线程数
3. 使用更轻量级的检测模型
4. 应用更具体的捕获过滤器减少处理数据量
