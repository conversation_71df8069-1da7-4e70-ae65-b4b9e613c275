# 模型比较工具使用指南

本文档提供了使用模型比较工具的两种方式：命令行方式和脚本方式。您可以根据自己的偏好选择合适的方式。

## 一、命令行方式

命令行方式适合快速比较或一次性任务。

### 基本语法

```bash
python bijiao/model_comparison.py [数据源参数] [模型参数] [训练参数] [输出参数]
```

### 数据源参数（选择一种）

- PCAP文件：
  ```
  --pcap-files 文件1.pcap 文件2.pcap ...
  ```

- CSV文件：
  ```
  --csv-files 文件1.csv 文件2.csv ...
  --feature-cols 特征1 特征2 ...  # 可选
  --label-col 标签列名  # 可选
  ```

- 合成数据（不需要文件）：
  ```
  --pattern simple|complex|nonlinear  # 数据模式
  --noise 0.1  # 噪声水平(0-1)
  ```

### 模型参数

```
--models simple,cnnlstm,advanced  # 要比较的模型，用逗号分隔
```

可选的模型类型：
- `simple`: SimpleModel (基础LSTM模型)
- `cnnlstm`: CNNLSTMModel (标准CNN-LSTM模型)
- `advanced`: ResCNNABLGAN (高级残差网络模型)

### 训练参数

```
--epochs 2  # 训练轮数
--batch-size 32  # 批次大小
--adversarial  # 使用对抗训练（可选）
--gan  # 使用GAN联合训练（可选）
```

### 输出参数

```
--output-dir bijiao  # 结果输出目录
```

### 完整使用示例

#### 示例1：使用PCAP文件

```bash
python bijiao/model_comparison.py --pcap-files bijiao/Benign/BitTorrent.pcap bijiao/Benign/Gmail.pcap bijiao/Benign/Skype.pcap bijiao/Malware/Cridex.pcap bijiao/Malware/Zeus.pcap --models simple,cnnlstm,advanced --epochs 2
```

#### 示例2：使用CSV文件

```bash
python bijiao/model_comparison.py --csv-files bijiao/data/normal_traffic.csv bijiao/data/attack_traffic.csv --feature-cols src_port dst_port packet_size protocol --label-col is_attack --models simple,cnnlstm,advanced --epochs 2
```

#### 示例3：使用合成数据

```bash
python bijiao/model_comparison.py --pattern complex --noise 0.2 --models simple,cnnlstm,advanced --epochs 1
```

## 二、脚本方式

脚本方式更灵活，适合复杂的比较任务或批量处理。

### 基本脚本模板

创建一个名为 `run_comparison.py` 的文件，内容如下：

```python
import os
import sys
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入比较函数
from bijiao.model_comparison import compare_models

def main():
    """运行模型比较"""
    print("开始模型性能比较...")
    
    # ===== 配置参数 =====
    # 1. 要比较的模型
    model_types = ['simple', 'cnnlstm', 'advanced']
    
    # 2. 训练参数
    num_epochs = 2
    batch_size = 32
    use_adversarial = False
    use_gan = False
    
    # 3. 输出目录
    output_dir = 'bijiao/results'
    os.makedirs(output_dir, exist_ok=True)
    
    # ===== 选择数据源（取消注释一种方式）=====
    
    # 方式1: 使用PCAP文件
    pcap_files = [
        'bijiao/Benign/BitTorrent.pcap',
        'bijiao/Benign/Gmail.pcap',
        'bijiao/Benign/Skype.pcap',
        'bijiao/Malware/Cridex.pcap',
        'bijiao/Malware/Zeus.pcap'
    ]
    csv_files = None
    feature_cols = None
    label_col = None
    data_pattern = 'simple'
    noise_level = 0.1
    
    # 方式2: 使用CSV文件
    # pcap_files = None
    # csv_files = [
    #     'bijiao/data/normal_traffic.csv',
    #     'bijiao/data/attack_traffic.csv'
    # ]
    # feature_cols = None  # 使用所有特征列
    # label_col = 'label'  # 标签列名
    # data_pattern = 'simple'
    # noise_level = 0.1
    
    # 方式3: 使用合成数据
    # pcap_files = None
    # csv_files = None
    # feature_cols = None
    # label_col = None
    # data_pattern = 'complex'  # 'simple', 'complex', 或 'nonlinear'
    # noise_level = 0.2
    
    # ===== 运行比较 =====
    results = compare_models(
        num_epochs=num_epochs,
        batch_size=batch_size,
        data_pattern=data_pattern,
        noise_level=noise_level,
        model_types=model_types,
        use_adversarial=use_adversarial,
        use_gan=use_gan,
        csv_files=csv_files,
        pcap_files=pcap_files,
        feature_cols=feature_cols,
        label_col=label_col
    )
    
    # 打印结果摘要
    print("\n比较完成!")
    print("\n模型性能比较结果:")
    print(results.to_string(index=False))
    
    # 保存结果到CSV
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_path = f"{output_dir}/custom_comparison_results_{timestamp}.csv"
    results.to_csv(results_path, index=False)
    print(f"\n详细结果已保存到: {results_path}")

if __name__ == "__main__":
    main()
```

### 批量比较脚本

如果需要进行多组比较，可以使用以下脚本：

```python
import os
import sys
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入比较函数
from bijiao.model_comparison import compare_models

def run_comparison(name, pcap_files=None, csv_files=None, feature_cols=None, label_col=None, 
                  model_types=None, num_epochs=2, use_adversarial=False):
    """运行一次模型比较"""
    print(f"\n开始比较: {name}")
    
    if model_types is None:
        model_types = ['simple', 'cnnlstm', 'advanced']
    
    # 创建输出目录
    output_dir = f'bijiao/results/{name}'
    os.makedirs(output_dir, exist_ok=True)
    
    # 运行比较
    results = compare_models(
        num_epochs=num_epochs,
        batch_size=32,
        data_pattern='simple',
        noise_level=0.1,
        model_types=model_types,
        use_adversarial=use_adversarial,
        use_gan=False,
        csv_files=csv_files,
        pcap_files=pcap_files,
        feature_cols=feature_cols,
        label_col=label_col
    )
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_path = f"{output_dir}/results_{timestamp}.csv"
    results.to_csv(results_path, index=False)
    print(f"结果已保存到: {results_path}")
    
    return results

def main():
    """运行多组比较"""
    # 比较1: 基本流量分析
    basic_pcaps = [
        'bijiao/Benign/Skype.pcap',
        'bijiao/Benign/Gmail.pcap',
        'bijiao/Malware/Zeus.pcap',
        'bijiao/Malware/Tinba.pcap'
    ]
    basic_results = run_comparison(
        name="basic_traffic",
        pcap_files=basic_pcaps,
        model_types=['simple', 'cnnlstm', 'advanced'],
        num_epochs=1
    )
    
    # 比较2: 高级流量分析（使用对抗训练）
    advanced_pcaps = [
        'bijiao/Benign/BitTorrent.pcap',
        'bijiao/Benign/FTP.pcap',
        'bijiao/Malware/Cridex.pcap',
        'bijiao/Malware/Nsis-ay.pcap'
    ]
    advanced_results = run_comparison(
        name="advanced_traffic",
        pcap_files=advanced_pcaps,
        model_types=['cnnlstm', 'advanced'],
        num_epochs=2,
        use_adversarial=True
    )
    
    # 比较3: 使用CSV数据
    csv_files = [
        'bijiao/data/network_traffic.csv'
    ]
    csv_results = run_comparison(
        name="csv_data",
        csv_files=csv_files,
        feature_cols=None,  # 使用所有特征列
        label_col='label',  # 标签列名
        model_types=['simple', 'advanced'],
        num_epochs=2
    )
    
    print("\n所有比较已完成!")

if __name__ == "__main__":
    main()
```

### 高级比较类

对于更复杂的比较需求，可以使用以下比较类：

```python
import os
import sys
from datetime import datetime
import pandas as pd
import matplotlib.pyplot as plt

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入比较函数
from bijiao.model_comparison import compare_models

class ModelComparator:
    """模型比较器类"""
    
    def __init__(self, output_dir='bijiao/results'):
        """初始化比较器"""
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        self.results = []
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def compare_pcap_files(self, name, pcap_files, model_types=None, num_epochs=2, 
                          use_adversarial=False, use_gan=False):
        """比较PCAP文件"""
        if model_types is None:
            model_types = ['simple', 'cnnlstm', 'advanced']
            
        print(f"\n开始PCAP文件比较: {name}")
        print(f"使用文件: {', '.join(pcap_files)}")
        
        results = compare_models(
            num_epochs=num_epochs,
            batch_size=32,
            model_types=model_types,
            use_adversarial=use_adversarial,
            use_gan=use_gan,
            pcap_files=pcap_files
        )
        
        # 保存结果
        results['comparison_name'] = name
        self.results.append(results)
        
        # 保存单次比较结果
        results_dir = f"{self.output_dir}/{name}"
        os.makedirs(results_dir, exist_ok=True)
        results_path = f"{results_dir}/results_{self.timestamp}.csv"
        results.to_csv(results_path, index=False)
        
        return results
    
    def compare_csv_files(self, name, csv_files, feature_cols=None, label_col=None, 
                         model_types=None, num_epochs=2):
        """比较CSV文件"""
        if model_types is None:
            model_types = ['simple', 'cnnlstm', 'advanced']
            
        print(f"\n开始CSV文件比较: {name}")
        print(f"使用文件: {', '.join(csv_files)}")
        
        results = compare_models(
            num_epochs=num_epochs,
            batch_size=32,
            model_types=model_types,
            csv_files=csv_files,
            feature_cols=feature_cols,
            label_col=label_col
        )
        
        # 保存结果
        results['comparison_name'] = name
        self.results.append(results)
        
        # 保存单次比较结果
        results_dir = f"{self.output_dir}/{name}"
        os.makedirs(results_dir, exist_ok=True)
        results_path = f"{results_dir}/results_{self.timestamp}.csv"
        results.to_csv(results_path, index=False)
        
        return results
    
    def generate_summary_report(self):
        """生成汇总报告"""
        if not self.results:
            print("没有可用的比较结果")
            return
        
        # 合并所有结果
        all_results = pd.concat(self.results)
        
        # 保存汇总结果
        summary_path = f"{self.output_dir}/summary_{self.timestamp}.csv"
        all_results.to_csv(summary_path, index=False)
        
        # 创建汇总报告
        report = f"# 模型比较汇总报告 - {self.timestamp}\n\n"
        
        # 添加每个比较的最佳模型
        report += "## 各比较中的最佳模型\n\n"
        for name in all_results['comparison_name'].unique():
            report += f"### {name}\n\n"
            subset = all_results[all_results['comparison_name'] == name]
            
            # 找出最佳模型
            best_accuracy = subset.loc[subset['accuracy'].idxmax()]
            best_f1 = subset.loc[subset['f1_score'].idxmax()]
            best_speed = subset.loc[subset['avg_inference_time_ms'].idxmin()]
            
            report += f"- 最高准确率: **{best_accuracy['model_type']}** ({best_accuracy['accuracy']:.4f})\n"
            report += f"- 最高F1分数: **{best_f1['model_type']}** ({best_f1['f1_score']:.4f})\n"
            report += f"- 最快推理速度: **{best_speed['model_type']}** ({best_speed['avg_inference_time_ms']:.2f} ms)\n\n"
        
        # 添加总体最佳模型
        report += "## 总体最佳模型\n\n"
        
        # 按准确率排序
        report += "### 按准确率排序\n\n"
        accuracy_ranking = all_results.sort_values('accuracy', ascending=False)
        report += "| 排名 | 比较名称 | 模型 | 准确率 | F1分数 | 推理时间(ms) |\n"
        report += "|------|----------|------|--------|--------|-------------|\n"
        for i, (_, row) in enumerate(accuracy_ranking.iterrows(), 1):
            if i > 10:  # 只显示前10名
                break
            report += f"| {i} | {row['comparison_name']} | {row['model_type']} | {row['accuracy']:.4f} | {row['f1_score']:.4f} | {row['avg_inference_time_ms']:.2f} |\n"
        
        # 保存报告
        report_path = f"{self.output_dir}/summary_report_{self.timestamp}.md"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n汇总报告已保存到: {report_path}")
        print(f"汇总数据已保存到: {summary_path}")
        
        return report

# 使用示例
def main():
    # 创建比较器
    comparator = ModelComparator(output_dir='bijiao/custom_results')
    
    # 比较1: 基本流量
    basic_pcaps = [
        'bijiao/Benign/Skype.pcap',
        'bijiao/Benign/Gmail.pcap',
        'bijiao/Malware/Zeus.pcap',
        'bijiao/Malware/Tinba.pcap'
    ]
    comparator.compare_pcap_files(
        name="basic_traffic",
        pcap_files=basic_pcaps,
        model_types=['simple', 'cnnlstm', 'advanced'],
        num_epochs=1
    )
    
    # 比较2: 高级流量
    advanced_pcaps = [
        'bijiao/Benign/BitTorrent.pcap',
        'bijiao/Benign/FTP.pcap',
        'bijiao/Malware/Cridex.pcap',
        'bijiao/Malware/Nsis-ay.pcap'
    ]
    comparator.compare_pcap_files(
        name="advanced_traffic",
        pcap_files=advanced_pcaps,
        model_types=['cnnlstm', 'advanced'],
        num_epochs=2,
        use_adversarial=True
    )
    
    # 生成汇总报告
    comparator.generate_summary_report()

if __name__ == "__main__":
    main()
```

### 使用脚本的步骤

1. **创建脚本文件**：
   - 复制上面的基本脚本代码
   - 保存为 `run_comparison.py` 到项目根目录

2. **修改数据源**：
   - 将PCAP或CSV文件路径替换为您自己的文件路径
   - 根据需要调整其他参数

3. **运行脚本**：
   ```bash
   python run_comparison.py
   ```

4. **查看结果**：
   - 脚本运行完成后，会在控制台显示结果摘要
   - 详细结果保存在指定的输出目录下

## 三、高级使用技巧

### 1. 批量处理多个文件夹

```python
import os
import glob

# 获取所有PCAP文件
benign_folder = 'D:/your_data/normal'
malware_folder = 'D:/your_data/malware'

benign_files = glob.glob(os.path.join(benign_folder, '*.pcap'))
malware_files = glob.glob(os.path.join(malware_folder, '*.pcap'))

# 合并所有文件路径
pcap_files = benign_files + malware_files

# 使用这些文件进行比较
results = compare_models(
    pcap_files=pcap_files,
    model_types=['simple', 'cnnlstm', 'advanced'],
    num_epochs=2
)
```

### 2. 分批处理大型数据集

```python
import os
import glob

# 获取所有PCAP文件
benign_folder = 'D:/your_data/normal'
malware_folder = 'D:/your_data/malware'

benign_files = glob.glob(os.path.join(benign_folder, '*.pcap'))
malware_files = glob.glob(os.path.join(malware_folder, '*.pcap'))

# 分批处理
batch_size = 5  # 每批处理的文件数
all_results = []

for i in range(0, len(benign_files), batch_size):
    for j in range(0, len(malware_files), batch_size):
        batch_benign = benign_files[i:i+batch_size]
        batch_malware = malware_files[j:j+batch_size]
        
        batch_files = batch_benign + batch_malware
        print(f"处理批次: {len(batch_files)} 文件")
        
        # 使用这些文件进行比较
        results = compare_models(
            pcap_files=batch_files,
            model_types=['simple', 'cnnlstm', 'advanced'],
            num_epochs=1
        )
        
        all_results.append(results)

# 合并所有结果
import pandas as pd
final_results = pd.concat(all_results)
final_results.to_csv('bijiao/results/all_batches_results.csv', index=False)
```

### 3. 使用不同的训练方法

```python
# 标准训练
results_standard = compare_models(
    pcap_files=pcap_files,
    model_types=['simple', 'cnnlstm', 'advanced'],
    num_epochs=2,
    use_adversarial=False,
    use_gan=False
)

# 对抗训练
results_adversarial = compare_models(
    pcap_files=pcap_files,
    model_types=['cnnlstm', 'advanced'],
    num_epochs=2,
    use_adversarial=True,
    use_gan=False
)

# GAN联合训练
results_gan = compare_models(
    pcap_files=pcap_files,
    model_types=['advanced'],
    num_epochs=2,
    use_adversarial=False,
    use_gan=True
)
```

## 四、结果解读

比较完成后，您应该关注以下关键指标：

1. **性能指标**：
   - 准确率(accuracy)：正确预测的比例
   - 精确率(precision)：预测为异常的样本中实际为异常的比例
   - 召回率(recall)：实际异常样本中被正确预测的比例
   - F1分数：精确率和召回率的调和平均
   - AUC：ROC曲线下面积，表示模型区分能力

2. **资源指标**：
   - 训练时间：模型训练所需的时间
   - 推理时间：模型进行预测所需的时间
   - 参数数量：模型的参数总数
   - 模型大小：模型占用的存储空间
   - 计算量：模型的浮点运算数（MFLOPs）

3. **最佳模型推荐**：
   - 报告中会列出每个指标的最佳模型
   - 根据应用场景的需求选择合适的模型

## 五、常见问题解决

1. **数据标签问题**：
   如果您的PCAP文件没有被正确标记，可以通过文件夹名称或文件名来区分正常和异常流量。确保文件路径中包含"normal"/"benign"或"malware"/"attack"等关键词。

2. **内存不足**：
   处理大型PCAP文件可能需要大量内存。如果遇到内存不足问题，可以：
   - 减少处理的文件数量
   - 增加批次大小（`batch_size`）
   - 在更高配置的机器上运行
   - 使用分批处理方法

3. **处理时间过长**：
   如果处理时间过长，可以：
   - 减少训练轮数（`epochs`）
   - 只比较部分模型（例如只使用`simple`和`advanced`）
   - 使用较小的数据子集进行初步比较
