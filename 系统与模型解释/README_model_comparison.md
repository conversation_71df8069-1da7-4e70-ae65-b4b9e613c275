# 深度学习模型性能比较

本项目提供了一个工具，用于训练和比较三种不同的深度学习模型在网络流量分析任务上的性能。

## 模型说明

比较的三个模型包括：

1. **SimpleModel** - 基础LSTM模型，结构简单，参数较少
2. **CNNLSTMModel** - 标准CNN-LSTM模型，结合了卷积神经网络和长短期记忆网络
3. **ResCNNABLGAN** - 高级模型，包含残差连接、注意力机制和对抗学习

其中，ResCNNABLGAN模型使用GAN联合训练，其他两个模型使用标准训练方法。

## 使用方法

### 准备数据

确保您有训练集和测试集CSV文件。默认使用：
- 训练集：`train_dataset_20250415_233855.csv`
- 测试集：`test_dataset_20250415_234156.csv`

### 运行比较

使用以下命令运行模型比较：

```bash
python run_model_comparison.py --train-csv train_dataset_20250415_233855.csv --test-csv test_dataset_20250415_234156.csv --epochs 10 --batch-size 32
```

参数说明：
- `--train-csv`: 训练集CSV文件路径
- `--test-csv`: 测试集CSV文件路径
- `--epochs`: 训练轮数（默认：10）
- `--batch-size`: 批次大小（默认：32）
- `--output-dir`: 输出目录（默认：results/model_comparison）

### 输出结果

比较完成后，将在输出目录中生成以下文件：

1. **模型性能比较表格** - 包含各模型的准确率、精确率、召回率、F1分数、AUC等指标
2. **模型性能比较图表** - 可视化展示各模型的性能指标
3. **训练过程图表** - 展示各模型的训练损失、验证损失、准确率和GAN损失曲线
4. **ROC曲线图** - 展示各模型的ROC曲线
5. **详细比较报告** - Markdown格式的详细比较报告

## 评估指标

模型比较包括以下评估指标：

### 性能指标
- **准确率 (Accuracy)** - 正确预测的比例
- **精确率 (Precision)** - 预测为异常的样本中实际为异常的比例
- **召回率 (Recall)** - 实际异常样本中被正确预测的比例
- **F1分数** - 精确率和召回率的调和平均
- **AUC** - ROC曲线下面积，表示模型区分能力

### 资源指标
- **训练时间** - 模型训练所需的时间
- **推理时间** - 模型进行预测所需的时间
- **参数数量** - 模型的参数总数
- **模型大小** - 模型占用的存储空间
- **计算量** - 模型的浮点运算数（MFLOPs）
- **复杂度分数** - 综合考虑层数和参数量的复杂度评分

## 注意事项

1. 确保已安装所有必要的依赖库（PyTorch、NumPy、Pandas、Matplotlib、scikit-learn等）
2. 训练ResCNNABLGAN模型需要较大的计算资源，建议使用GPU
3. 如果遇到内存不足问题，可以尝试减小批次大小
4. 对于大型数据集，可能需要增加训练轮数以达到最佳效果
