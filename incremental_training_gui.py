"""
增量训练GUI客户端

此脚本提供了一个简单的图形用户界面，用于触发和监控模型的增量训练。
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import requests
import json
import threading
import time

# API端点
BASE_URL = "http://localhost:5000/api"
ADD_DATA_URL = f"{BASE_URL}/add-training-data"
STATUS_URL = f"{BASE_URL}/model-training-status"
TRIGGER_URL = f"{BASE_URL}/trigger-model-training"

class IncrementalTrainingGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("模型增量训练工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        self.create_widgets()
        self.monitoring = False
        self.monitor_thread = None
        
    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 模型选择
        model_frame = ttk.LabelFrame(main_frame, text="模型选择", padding="10")
        model_frame.pack(fill=tk.X, pady=5)
        
        self.model_var = tk.StringVar(value="anomaly_detector")
        ttk.Radiobutton(model_frame, text="异常流量检测模型", variable=self.model_var, 
                        value="anomaly_detector").pack(side=tk.LEFT, padx=10)
        ttk.Radiobutton(model_frame, text="加密流量检测模型", variable=self.model_var, 
                        value="encryption_detector").pack(side=tk.LEFT, padx=10)
        
        # 操作按钮
        button_frame = ttk.Frame(main_frame, padding="10")
        button_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(button_frame, text="检查模型状态", 
                   command=self.check_status).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="触发增量训练", 
                   command=self.trigger_training).pack(side=tk.LEFT, padx=5)
        
        self.monitor_var = tk.BooleanVar(value=False)
        self.monitor_button = ttk.Checkbutton(button_frame, text="监控训练进度", 
                                             variable=self.monitor_var,
                                             command=self.toggle_monitoring)
        self.monitor_button.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="清除日志", 
                   command=self.clear_log).pack(side=tk.RIGHT, padx=5)
        
        # 状态信息
        status_frame = ttk.LabelFrame(main_frame, text="模型状态", padding="10")
        status_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(status_frame, text="训练队列大小:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.queue_size_var = tk.StringVar(value="未知")
        ttk.Label(status_frame, textvariable=self.queue_size_var).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(status_frame, text="训练状态:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.training_status_var = tk.StringVar(value="未知")
        ttk.Label(status_frame, textvariable=self.training_status_var).grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        # 进度条
        self.progress_var = tk.DoubleVar(value=0.0)
        self.progress = ttk.Progressbar(main_frame, orient=tk.HORIZONTAL, 
                                        length=100, mode='indeterminate',
                                        variable=self.progress_var)
        self.progress.pack(fill=tk.X, pady=5)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, 
                                                 width=80, height=20)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.log_text.config(state=tk.DISABLED)
        
    def log(self, message):
        """添加消息到日志区域"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        
    def clear_log(self):
        """清除日志区域"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        
    def check_status(self):
        """检查模型状态"""
        model_name = self.model_var.get()
        self.log(f"\n正在检查模型 {model_name} 的状态...")
        
        try:
            self.progress.start()
            response = requests.get(f"{STATUS_URL}?model_name={model_name}")
            self.progress.stop()
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    status = data['status']
                    self.queue_size_var.set(str(status['queue_size']))
                    
                    training_status = "空闲"
                    if status['training_in_progress']:
                        training_status = "训练中"
                    elif status['training_triggered']:
                        training_status = "已触发训练"
                    
                    self.training_status_var.set(training_status)
                    
                    self.log(f"训练队列大小: {status['queue_size']}")
                    self.log(f"训练状态: {training_status}")
                    
                    # 打印最近的训练日志
                    if 'training_log' in status and status['training_log']:
                        self.log("\n最近的训练记录:")
                        for log in status['training_log'][-3:]:  # 只显示最近3条
                            self.log(f"- {log['timestamp']}: {'成功' if log['success'] else '失败'} - {log['message']}")
                else:
                    self.log(f"获取状态失败: {data.get('error')}")
            else:
                self.log(f"请求失败，状态码: {response.status_code}")
        except Exception as e:
            self.progress.stop()
            self.log(f"请求出错: {str(e)}")
            messagebox.showerror("错误", f"无法连接到服务器: {str(e)}\n请确保Flask应用程序正在运行。")
    
    def trigger_training(self):
        """触发模型增量训练"""
        model_name = self.model_var.get()
        self.log(f"\n正在触发模型 {model_name} 的增量训练...")
        
        try:
            self.progress.start()
            response = requests.post(TRIGGER_URL, json={'model_name': model_name})
            self.progress.stop()
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log(f"成功触发训练: {data.get('message')}")
                    messagebox.showinfo("成功", f"已成功触发模型 {model_name} 的增量训练")
                    
                    # 如果选择了监控，开始监控
                    if self.monitor_var.get():
                        self.start_monitoring()
                else:
                    self.log(f"触发训练失败: {data.get('error')}")
                    messagebox.showerror("错误", f"触发训练失败: {data.get('error')}")
            else:
                self.log(f"请求失败，状态码: {response.status_code}")
                messagebox.showerror("错误", f"请求失败，状态码: {response.status_code}")
        except Exception as e:
            self.progress.stop()
            self.log(f"请求出错: {str(e)}")
            messagebox.showerror("错误", f"无法连接到服务器: {str(e)}\n请确保Flask应用程序正在运行。")
    
    def toggle_monitoring(self):
        """切换监控状态"""
        if self.monitor_var.get():
            self.start_monitoring()
        else:
            self.stop_monitoring()
    
    def start_monitoring(self):
        """开始监控训练进度"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self.monitor_training)
            self.monitor_thread.daemon = True
            self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控训练进度"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(0.1)
    
    def monitor_training(self):
        """监控训练进度的线程函数"""
        model_name = self.model_var.get()
        self.log(f"\n开始监控模型 {model_name} 的训练进度...")
        
        self.progress.start()
        start_time = time.time()
        
        while self.monitoring:
            try:
                response = requests.get(f"{STATUS_URL}?model_name={model_name}")
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        status = data['status']
                        
                        # 更新UI
                        self.queue_size_var.set(str(status['queue_size']))
                        
                        training_status = "空闲"
                        if status['training_in_progress']:
                            training_status = "训练中"
                        elif status['training_triggered']:
                            training_status = "已触发训练"
                        
                        self.training_status_var.set(training_status)
                        
                        # 如果训练已完成
                        if not status['training_in_progress'] and not status['training_triggered']:
                            self.log("\n训练已完成!")
                            
                            # 打印最近的训练记录
                            if 'training_log' in status and status['training_log']:
                                self.log("\n最近的训练记录:")
                                for log in status['training_log'][-1:]:  # 只显示最近一条
                                    self.log(f"- {log['timestamp']}: {'成功' if log['success'] else '失败'} - {log['message']}")
                            
                            self.monitoring = False
                            break
                        else:
                            elapsed = int(time.time() - start_time)
                            self.log(f"训练中... (已等待 {elapsed} 秒)")
            except Exception as e:
                self.log(f"监控出错: {str(e)}")
            
            time.sleep(5)  # 每5秒检查一次
        
        self.progress.stop()
        self.monitor_var.set(False)
        self.log("\n监控已停止")

def main():
    root = tk.Tk()
    app = IncrementalTrainingGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
